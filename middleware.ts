import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { geolocation } from '@vercel/functions';

import { publicConfig as envConfig } from 'config/public-config';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { RequestHelpers } from 'helpers/RequestHelpers';
import { isAuthenticatedNextRequest } from 'helpers/is-authenticated-request';

const middleware = async (request: NextRequest) => {
    if (requestIsAssetRequest(request)) {
        return NextResponse.next();
    }

    if (!isAuthorizedLockedEnvironment(request)) {
        return NextResponse.redirect(new URL('/sorry', request.nextUrl.origin));
    }

    if (requestIsAPIRequest(request)) {
        return NextResponse.next();
    }

    const response = NextResponse.next();

    const user = await getRequestUser();
    if (user) {
        response.headers.set('x-user', RequestHelpers.encode(user));
    }

    const geo = geolocation(request);
    delete geo.flag;

    if (geo) {
        response.headers.set('x-geo', RequestHelpers.encode(geo));
    }

    if (requestRedirectsToDraw(request)) {
        if (!isAuthenticatedNextRequest(request)) {
            return NextResponse.redirect(new URL('/login', request.nextUrl.origin));
        }
    }

    const loginRedirect = requestRedirectsToLogin(request);

    if (loginRedirect) {
        if (!isAuthenticatedNextRequest(request)) {
            return NextResponse.redirect(new URL(`/login?redirect=${loginRedirect}`, request.nextUrl.origin));
        }
    }

    return response;
};

const _hasLockedEnvironmentAccess = (request: NextRequest) =>
    envConfig.environmentLockAccess === request.cookies.get('environmentLockAccess')?.value ||
    envConfig.environmentLockAccess === request.headers.get('environmentLockAccess');
const _isUnlockedEnvironment = () => envConfig.environmentLockAccess === undefined;
const _isAccessPage = (request: NextRequest) => request.nextUrl.pathname.startsWith('/access');
const _isSorryPage = (request: NextRequest) => request.nextUrl.pathname === '/sorry';

const isAuthorizedLockedEnvironment = (request: NextRequest) => {
    const hasEnvironmentAccess = _isUnlockedEnvironment() || _hasLockedEnvironmentAccess(request);

    return _isAccessPage(request) || _isSorryPage(request) || hasEnvironmentAccess;
};

const getRequestUser = async () => {
    try {
        const parsedCookies = await cookies();

        const dcideJwt = parsedCookies.get('dcide-jwt')?.value;
        if (!dcideJwt) {
            return null;
        }

        const environmentLockAccess = parsedCookies.get('environmentLockAccess')?.value;

        const response = await fetch(`${envConfig.urls.api}/users/me`, {
            headers: {
                authorization: dcideJwt,
                ...(environmentLockAccess ? { environmentLockAccess } : {}),
            },
        });

        const body = await response.text();

        try {
            const { user } = JSON.parse(body);

            if (user) {
                return user;
            }
        } catch (error) {
            console.error('Error parsing user response:', error, body);
        }
    } catch (error) {
        console.error('Error fetching user data:', error);
    }

    return null;
};

const requestRedirectsToDraw = (request: NextRequest) =>
    drawRoutes.some((drawRoutes) => request.nextUrl.pathname.startsWith(drawRoutes));

const drawRoutes = ['/logout', '/diagram', '/quotes', '/account', '/inbox'];

const requestRedirectsToLogin = (request: NextRequest) =>
    loginRoutes.find((loginRoutes) => request.nextUrl.pathname.startsWith(loginRoutes));

const loginRoutes: string[] = [ComponentHelpers.urls.manage(), ComponentHelpers.urls.teamComponents()];

const requestIsAPIRequest = (request: NextRequest) =>
    assetAPIUrls.some((baseUrl) => request.nextUrl.pathname.startsWith(baseUrl));

const requestIsAssetRequest = (request: NextRequest) =>
    assetAssetUrls.some((baseUrl) => request.nextUrl.pathname.startsWith(baseUrl));

const assetAPIUrls = ['/api/', '/_next/', '/_vercel/'];
const assetAssetUrls = ['/_next/static/', '/favicon.ico'];

const config = {
    matcher: '/:path',
};

export { isAuthorizedLockedEnvironment, middleware, config };
