name: Run Migrations

env:
  MONGODB_URI: ${{ startsWith(github.ref, 'refs/tags/v') && secrets.MONGODB_URI_PRODUCTION || secrets.MONGODB_URI_STAGING }}
  PAYLOAD_CONFIG_PATH: 'src/backend/payload.config.migrations.ts'
  CI_MIGRATING: 'true'
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

on:
  push:
    tags:
      - 'v*'
    branches:
      - 'main'
    paths:
      - 'src/migrations/**'
      - '.github/workflows/run-migrations.yml'

  workflow_dispatch:

jobs:
  migrate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dcide-setup-node
      - run: npm install -g husky

      # Fake env file to make config parser happy
      - run: cp .env.example .env
      - run: echo "MONGODB_URI=${{ env.MONGODB_URI }}" >> .env

      - run: npx payload migrate

  slack-workflow-status:
    if: failure()
    name: Post Workflow Status To Slack
    needs:
      - migrate
    runs-on: ubuntu-latest
    permissions:
      actions: 'read'
    steps:
      - name: Slack Workflow Notification
        uses: Gamesight/slack-workflow-status@v1.3.0
        with:
          repo_token: ${{secrets.GITHUB_TOKEN }}
          slack_webhook_url: ${{ secrets.SLACK_WEBHOOK }}
