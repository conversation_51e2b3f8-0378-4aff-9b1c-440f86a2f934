name: Vercel Next Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_DCIDE_APP_PROJECT_ID }}
  VERCEL_ENV: ${{ startsWith(github.ref, 'refs/tags/v') && 'production' || 'preview' }}
  NEXT_PUBLIC_AI_URL: ${{ startsWith(github.ref, 'refs/tags/v') && vars.NEXT_PUBLIC_AI_URL_PRODUCTION || vars.NEXT_PUBLIC_AI_URL_STAGING }}
  NEXT_PUBLIC_SERVER_URL: ${{ startsWith(github.ref, 'refs/tags/v') && vars.NEXT_PUBLIC_SERVER_URL_PRODUCTION || vars.NEXT_PUBLIC_SERVER_URL_STAGING }}
  NEXT_PUBLIC_FRONTEND_URL: ${{ startsWith(github.ref, 'refs/tags/v') && vars.NEXT_PUBLIC_FRONTEND_URL_PRODUCTION || vars.NEXT_PUBLIC_FRONTEND_URL_STAGING }}
  NEXT_PUBLIC_AI_API: ${{ startsWith(github.ref, 'refs/tags/v') && vars.PRODUCTION_AI_API || vars.STAGING_AI_API }}
  NEXT_PUBLIC_BACKEND_API: ${{ startsWith(github.ref, 'refs/tags/v') && vars.PRODUCTION_BACKEND_API || vars.STAGING_BACKEND_API }}
  NEXT_PUBLIC_BACKEND: ${{ startsWith(github.ref, 'refs/tags/v') && vars.PRODUCTION_BACKEND || vars.STAGING_BACKEND }}
  SENTRY_AUTH_TOKEN: ${{ startsWith(github.ref, 'refs/tags/v') && secrets.SENTRY_AUTH_TOKEN_PRODUCTION || secrets.SENTRY_AUTH_TOKEN_STAGING }}

on:
  push:
    tags:
      - 'v*'
    branches:
      - 'main'
      - 'test/**'

  workflow_dispatch:

jobs:
  Deploy-Vercel-Next:
    runs-on: ubuntu-latest
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dcide-setup-node

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=${{ env.VERCEL_ENV }} --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build Project Artifacts
        run: vercel build ${{ env.VERCEL_ENV == 'production' && '--prod' || '' }} --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt ${{ env.VERCEL_ENV == 'production' && '--prod' || '' }} --token=${{ secrets.VERCEL_TOKEN }}

  slack-workflow-status:
    if: failure()
    name: Post Workflow Status To Slack
    needs:
      - Deploy-Vercel-Next
    runs-on: ubuntu-latest
    permissions:
      actions: 'read'
    steps:
      - name: Slack Workflow Notification
        uses: Gamesight/slack-workflow-status@v1.3.0
        with:
          repo_token: ${{secrets.GITHUB_TOKEN }}
          slack_webhook_url: ${{ secrets.SLACK_WEBHOOK }}
