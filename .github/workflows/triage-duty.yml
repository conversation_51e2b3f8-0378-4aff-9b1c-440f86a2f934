name: Triage Duty - Slack Notification

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * 1'

permissions: read-all

jobs:
  slackNotification:
    name: Triage Duty - Slack Notification
    runs-on: ubuntu-latest
    steps:
      - name: Get the last workflow run ID
        id: get_last_run_id
        run: |
          LAST_RUN_ID=$(curl -s \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github+json" \
            https://api.github.com/repos/${{ github.repository }}/actions/workflows/triage-duty.yml/runs | jq '.workflow_runs[1].id')
          echo "Last workflow run ID: $LAST_RUN_ID"
          echo "LAST_RUN_ID=$LAST_RUN_ID" >> "$GITHUB_OUTPUT"

      - name: Download triage-duty-index
        uses: actions/download-artifact@v4
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          run-id: ${{ steps.get_last_run_id.outputs.LAST_RUN_ID }}
        continue-on-error: true

      - name: Get current index
        id: get_index
        run: |
          if [ -f triage-duty/triage-duty-index.txt ]; then
            echo "triage-duty/triage-duty-index.txt exists"
            echo "INDEX=$(cat triage-duty/triage-duty-index.txt)" >> "$GITHUB_OUTPUT"
          else
            echo "INDEX=0" >> "$GITHUB_OUTPUT"
          fi

      - name: Make message from rotation
        id: assign_triage_duty
        env:
          INDEX: ${{ steps.get_index.outputs.INDEX }}
        run: |
          echo "Current index: $INDEX"
          rotation=("Matt" "Svetlana" "Gabe" "Kobe")
          length=${#rotation[@]}
          current_index=$(( $INDEX ))
          if [ $current_index -eq $length ]; then
            echo "Reset index"
            current_index=0
          fi
          value=${rotation[$current_index]}
          echo "Current triage duty: $value!"
          echo "INDEX=$current_index" >> "$GITHUB_OUTPUT"
          echo "triage_duty_message=:pager: $value is on triage duty this week!" >> "$GITHUB_OUTPUT"
        shell: bash

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          MSG_MINIMAL: 'true'
          SLACK_CHANNEL: dcide-dev
          SLACK_MESSAGE: ${{ steps.assign_triage_duty.outputs.triage_duty_message }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

      - name: Increment index
        run: |
          echo $(( ${{ steps.assign_triage_duty.outputs.INDEX }} + 1 )) > triage-duty-index.txt
          echo "Index incremented to $(( ${{ steps.assign_triage_duty.outputs.INDEX }} + 1 ))!"

      - name: Upload index
        uses: actions/upload-artifact@v4
        with:
          name: triage-duty
          path: triage-duty-index.txt
