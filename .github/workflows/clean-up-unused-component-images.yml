name: Clean Up Unused Component Images

on:
  workflow_dispatch:

jobs:
  clean:
    runs-on: ubuntu-latest
    steps:
      - name: send staging clean request
        run: |
          curl --location '${{ vars.STAGING_BACKEND_API }}/clean-up-unused-component-images' \
            --header 'Authorization: ${{ secrets.DEVOPS_TOKEN }}'

      - name: send production clean request
        run: |
          curl --location '${{ vars.PRODUCTION_BACKEND_API }}/clean-up-unused-component-images' \
            --header 'Authorization: ${{ secrets.DEVOPS_TOKEN }}'
