import * as Sentry from '@sentry/nextjs';
import { publicConfig } from '@public-config';

if (publicConfig.sentryPublicKey) {
    Sentry.init({
        dsn: publicConfig.sentryPublicKey,
        tracesSampleRate: 0.5,
        replaysOnErrorSampleRate: 1,
        replaysSessionSampleRate: 1,
        sendDefaultPii: true,
        integrations: [
            Sentry.browserTracingIntegration(),
            Sentry.replayIntegration({
                maskAllText: false,
                maskAllInputs: false,
                blockAllMedia: false,
                networkDetailAllowUrls: ['dcide.app'],
                networkResponseHeaders: ['x-user'],
            }),
            Sentry.captureConsoleIntegration({ levels: ['error'] }),
        ],
    });
    Sentry.setTag('user.agent', navigator.userAgent);
}

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
