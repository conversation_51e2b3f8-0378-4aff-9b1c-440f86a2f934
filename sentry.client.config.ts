import * as Sentry from '@sentry/nextjs';
import { config } from '@config';

if (config.sentry) {
    Sentry.init({
        dsn: config.sentry.dsn,
        tracesSampleRate: 0.5,
        replaysOnErrorSampleRate: 1,
        replaysSessionSampleRate: 1,
        integrations: [
            Sentry.browserTracingIntegration(),
            Sentry.replayIntegration({
                maskAllText: false,
                maskAllInputs: false,
                blockAllMedia: false,
                networkDetailAllowUrls: ['dcide.app'],
                networkResponseHeaders: ['x-user'],
            }),
            Sentry.captureConsoleIntegration({ levels: ['error'] }),
        ],
        tracePropagationTargets: ['api.dcide.app'],
    });
    Sentry.setTag('user.agent', navigator.userAgent);
}
