diff --git a/node_modules/payload/dist/types/constants.d.ts b/node_modules/payload/dist/types/constants.d.ts
index ffb8bc8..813e0d2 100644
--- a/node_modules/payload/dist/types/constants.d.ts
+++ b/node_modules/payload/dist/types/constants.d.ts
@@ -1,4 +1,4 @@
-export declare const validOperators: readonly ["equals", "contains", "not_equals", "in", "all", "not_in", "exists", "greater_than", "greater_than_equal", "less_than", "less_than_equal", "like", "not_like", "within", "intersects", "near"];
+export declare const validOperators: readonly ["equals", "contains", "not_equals", "in", "all", "not_in", "exists", "greater_than", "greater_than_equal", "less_than", "less_than_equal", "like", "not_like", "within", "intersects", "near", "match"];
 export type Operator = (typeof validOperators)[number];
-export declare const validOperatorSet: Set<"all" | "contains" | "equals" | "exists" | "intersects" | "near" | "within" | "not_equals" | "in" | "not_in" | "greater_than" | "greater_than_equal" | "less_than" | "less_than_equal" | "like" | "not_like">;
+export declare const validOperatorSet: Set<"all" | "contains" | "equals" | "exists" | "intersects" | "near" | "within" | "not_equals" | "in" | "not_in" | "greater_than" | "greater_than_equal" | "less_than" | "less_than_equal" | "like" | "not_like" | "match">;
 //# sourceMappingURL=constants.d.ts.map
diff --git a/node_modules/payload/dist/types/constants.js b/node_modules/payload/dist/types/constants.js
index 2f2cf73..2012611 100644
--- a/node_modules/payload/dist/types/constants.js
+++ b/node_modules/payload/dist/types/constants.js
@@ -14,7 +14,8 @@ export const validOperators = [
     'not_like',
     'within',
     'intersects',
-    'near'
+    'near',
+    'match'
 ];
 export const validOperatorSet = new Set(validOperators);

