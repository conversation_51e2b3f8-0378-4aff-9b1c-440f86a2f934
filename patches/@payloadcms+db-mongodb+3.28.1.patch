diff --git a/node_modules/@payloadcms/db-mongodb/dist/queries/operatorMap.js b/node_modules/@payloadcms/db-mongodb/dist/queries/operatorMap.js
index cd4985b..538693d 100644
--- a/node_modules/@payloadcms/db-mongodb/dist/queries/operatorMap.js
+++ b/node_modules/@payloadcms/db-mongodb/dist/queries/operatorMap.js
@@ -11,7 +11,8 @@ export const operatorMap = {
     near: '$near',
     not_equals: '$ne',
     not_in: '$nin',
-    within: '$geoWithin'
+    within: '$geoWithin',
+    match: '$elemMatch'
 };

 //# sourceMappingURL=operatorMap.js.map
\ No newline at end of file
