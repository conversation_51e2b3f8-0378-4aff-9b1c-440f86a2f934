* {
    -webkit-user-drag: none;
}

html {
    scroll-padding-top: calc(var(--header-height) * 2);
    scroll-behavior: smooth;
}

body {
    &[data-resizing-ew='true'] * {
        cursor: col-resize;
    }

    &[data-touch-device='tv'] {
        user-select: none;
    }
}

:root {
    color-scheme: light;

    --default-gradient: linear-gradient(
        130deg,
        var(--mantine-color-primary-4) 0%,
        var(--mantine-color-brandOriginal-7) 100%
    );

    --header-height: 45px;
    --header-height-floating: 32px;
    --sidebar-nav-width: 240px;
    --diagram-sidebar-width: 450px;
    --diagram-sidebar-nav-width: 50px;

    --mobile-nav-height: 60px;

    --content-width: 1200px;
    --wide-content-width: 1600px;

    --navigate-cursor:
        url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 512 512" fill="white"><path fill="white" d="M241.5 233L180.5 308V326.5L437.5 332.5L368.5 243L241.5 233Z"/><path fill="white" d="M432 320V144C432 135.513 428.629 127.374 422.627 121.373C416.626 115.371 408.487 112 400 112C391.513 112 383.374 115.371 377.373 121.373C371.371 127.374 368 135.513 368 144V256"/><path d="M432 320V144C432 135.513 428.629 127.374 422.627 121.373C416.626 115.371 408.487 112 400 112C391.513 112 383.374 115.371 377.373 121.373C371.371 127.374 368 135.513 368 144V256" stroke="currentColor" stroke-width="32" stroke-linecap="round" stroke-linejoin="round"/><path d="M368 256V80C368 71.5131 364.629 63.3737 358.627 57.3726C352.626 51.3714 344.487 48 336 48C327.513 48 319.374 51.3714 313.373 57.3726C307.371 63.3737 304 71.5131 304 80V240" fill="white"/><path d="M368 256V80C368 71.5131 364.629 63.3737 358.627 57.3726C352.626 51.3714 344.487 48 336 48C327.513 48 319.374 51.3714 313.373 57.3726C307.371 63.3737 304 71.5131 304 80V240" stroke="currentColor" stroke-width="32" stroke-linecap="round" stroke-linejoin="round"/><path d="M240 241V96C240 87.5131 236.629 79.3737 230.627 73.3726C224.626 67.3714 216.487 64 208 64C199.513 64 191.374 67.3714 185.373 73.3726C179.371 79.3737 176 87.5131 176 96V320" fill="white"/><path d="M240 241V96C240 87.5131 236.629 79.3737 230.627 73.3726C224.626 67.3714 216.487 64 208 64C199.513 64 191.374 67.3714 185.373 73.3726C179.371 79.3737 176 87.5131 176 96V320" stroke="currentColor" stroke-width="32" stroke-linecap="round" stroke-linejoin="round"/> <path d="M304 240V48C304 39.5131 300.629 31.3737 294.627 25.3726C288.626 19.3714 280.487 16 272 16C263.513 16 255.374 19.3714 249.373 25.3726C243.371 31.3737 240 39.5131 240 48V240" fill="white"/><path d="M304 240V48C304 39.5131 300.629 31.3737 294.627 25.3726C288.626 19.3714 280.487 16 272 16C263.513 16 255.374 19.3714 249.373 25.3726C243.371 31.3737 240 39.5131 240 48V240" stroke="currentColor" stroke-width="32" stroke-linecap="round" stroke-linejoin="round"/><path d="M432 320C432 437.4 368 496 280 496C192 496 156.29 456.4 136 408L83.33 264C76.67 245.95 79.69 229.21 95.2 220.4C110.72 211.58 131.11 216.12 139.51 232.08L176 320" fill="white"/><path d="M432 320C432 437.4 368 496 280 496C192 496 156.29 456.4 136 408L83.33 264C76.67 245.95 79.69 229.21 95.2 220.4C110.72 211.58 131.11 216.12 139.51 232.08L176 320" stroke="currentColor" stroke-width="32" stroke-linecap="round" stroke-linejoin="round"/></svg>')
            10 10,
        auto;

    --comment-cursor:
        url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.25" viewBox="0 0 24 24" width="20" height="20"><path fill="white" d="m3 20 1.3-3.9C1.98 12.66 2.87 8.23 6.4 5.73a9.86 9.86 0 0 1 11.85.48 7.36 7.36 0 0 1 1.02 10.5C16.61 19.94 11.66 20.92 7.7 19L3 20" /></svg>')
            2 18,
        auto;
}

.sidebar-section + .sidebar-section {
    border-top: 1px solid var(--mantine-color-gray-1);
}

[data-show-export] {
    display: none;
}

.export-diagram {
    [data-hide-export] {
        display: none;
    }

    [data-show-export] {
        display: block !important;
    }
}

.comment-display div.tiptap {
    padding: 0;
}

.comment-mention {
    color: var(--mantine-color-brand-3);
}

.tiptap {
    p {
        margin: 0;
    }

    > * + * {
        margin-top: 10px;
    }

    li + li {
        margin-top: 5px;
    }
}

.react-resizable {
    pointer-events: none;

    .react-resizable-handle {
        pointer-events: all;
    }
}

.mantine-active {
    &:active {
        transform: none;
    }
}

.mantine-focus-always {
    &:focus {
        outline: none;
    }
}

.mantine-Notifications-root {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);

    z-index: 300;

    .is-diagram & {
        left: calc(50% - (var(--diagram-sidebar-width) / 2));
    }
}

.mantine-Notifications-notification {
    max-width: 360px;

    border-radius: var(--mantine-radius-xl);
    box-shadow: none;

    background-color: var(--mantine-color-gray-9);

    margin-bottom: var(--mantine-spacing-xs);

    &::before {
        display: none !important;
    }
}

.mantine-Notification-title {
    font-weight: 600;
    color: var(--mantine-color-white);
}

.mantine-Notification-description {
    color: var(--mantine-color-white);
}

.mantine-Notification-closeButton {
    color: var(--mantine-color-dimmed);

    &:hover {
        color: var(--mantine-color-gray-1) !important;
        background-color: transparent !important;
    }
}

.mantine-Drawer-root {
    position: relative;
    z-index: 202; // above mobile nav
}

.react-resizable-handle {
    background: none !important;

    &::after {
        content: '';

        position: absolute;
        right: -5px;
        bottom: -5px;

        display: block;
        width: 8px;
        height: 8px;

        background: var(--mantine-color-primary-6);
    }
}

.react-resizable-handle-n,
.react-resizable-handle-s,
.react-resizable-handle-e,
.react-resizable-handle-w {
    &::after {
        transform: translate(-3px, -3px) rotate(45deg);
    }
}

.react-resizable-handle-ne {
    top: -2px;
    right: -2px;
}

.react-resizable-handle-se {
    bottom: -2px;
    right: -2px;
}

.react-resizable-handle-sw {
    bottom: -2px;
    left: -2px;
}

.react-resizable-handle-nw {
    top: -2px;
    left: -2px;
}

.gradient-cyan-green {
    --color-1: var(--mantine-color-cyan-5);
    --color-2: var(--mantine-color-green-5);

    background: linear-gradient(90deg, var(--color-1) 0%, var(--color-2) 75%);

    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.gradient-text-default {
    --color-1: var(--mantine-color-primary-4);
    --color-2: var(--mantine-color-brandOriginal-4);

    background: linear-gradient(90deg, var(--color-1) 25%, var(--color-2) 75%);

    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

[data-sonner-toaster] {
    width: 320px !important;

    .is-diagram & {
        left: 52px !important;
        top: 55px !important;
    }

    .is-sidebar-open.is-diagram & {
        left: calc(var(--mantine-spacing-xs) + var(--sidebar-nav-width)) !important;
    }
}

mapbox-geocoder {
    input {
        border: none !important;

        font-weight: 400 !important;
        color: var(--mantine-color-black) !important;
        font-family: var(--mantine-font-family) !important;

        &::placeholder {
            font-weight: 400;
            color: var(--mantine-color-gray-5);
            font-family: var(--mantine-font-family) !important;
        }
    }

    [aria-label='Clear'] {
        width: 14px !important;
        height: 14px !important;

        margin-top: 3px;
        margin-left: 3px;

        color: var(--mantine-color-gray-6) !important;
    }
}

mapbox-search-listbox {
    [class$='Results'] {
        padding: 4px;

        border: 1px solid var(--mantine-color-gray-2);
        border-radius: var(--mantine-radius-sm);

        font-family: var(--mantine-font-family) !important;
    }

    [class$='Suggestion'] {
        padding: 8px;
        border-radius: var(--mantine-radius-sm);

        &[aria-selected='true'] {
            background-color: var(--mantine-color-gray-0);
        }
    }

    [class$='SuggestionName'] {
        font-weight: 500;
    }

    [class$='SuggestionDesc'] {
        color: var(--mantine-color-gray-6);
    }

    [class$='ResultsAttribution'] a {
        color: var(--mantine-color-gray-5);
        font-weight: 600;
        font-size: var(--mantine-font-size-xs);
    }
}

#app_shell {
    &.mantine-AppShell-root {
        min-height: 100dvh;
        background-color: var(--mantine-color-gray-0);
    }

    .mantine-AppShell-main {
        display: flex;
        flex-direction: column;
    }

    .mantine-AppShell-navbar {
        z-index: 203;

        border: none;
        background-color: transparent;

        @media (max-width: $mantine-breakpoint-md) {
            [data-sidebar-nav] {
                box-shadow: 20px 0 100px rgba(0, 0, 0, 0.2);
            }
        }
    }
}

.gradient-background {
    background-image: var(--default-gradient);
}

.gradient-background-light {
    position: relative;

    background-image: var(--default-gradient);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #fff;
        opacity: 0.7;
        z-index: 0;
    }

    > * {
        z-index: 1;
    }
}

.mantine-Checkbox-description {
    margin-top: 0;
    font-size: var(--mantine-font-size-sm);
}

.mantine-PillGroup-group {
    --pg-gap: 4px;
}
