import { expect, test } from '@jest/globals';
import { NextRequest } from 'next/server';
import { publicConfig as envConfig } from './config/public-config';
import { isAuthorizedLockedEnvironment } from './middleware';
import { RequestCookies } from 'next/dist/compiled/@edge-runtime/cookies';

const quickSetup = (nextUrl: string, environmentLockAccess?: string, cookie?: string) => {
    Object.defineProperty(envConfig, 'environmentLockAccess', { value: environmentLockAccess });
    const cookieListItem = { value: cookie } as unknown as RequestCookies;
    const request = {
        cookies: {
            get: jest.fn().mockReturnValue(cookieListItem),
        },
        nextUrl: {
            pathname: nextUrl,
        },
        headers: new Headers(),
    } as unknown as NextRequest;
    return request;
};

test('should return authorized when environment access is restricted and request is authorized', () => {
    const request = quickSetup('/some-page', 'access-key', 'access-key');
    expect(isAuthorizedLockedEnvironment(request)).toBe(true);
});

test('should return unauthorized when environment access is restricted and wrong cookie value', () => {
    const request = quickSetup('/some-page', 'access-key', 'not-access-key');
    expect(isAuthorizedLockedEnvironment(request)).toBe(false);
});

test('should return unauthorized when environment access is restricted and no cookie value', () => {
    const request = quickSetup('/some-page', 'access-key');
    expect(isAuthorizedLockedEnvironment(request)).toBe(false);
});

test('should return authorized when environment access is not restricted', () => {
    const request = quickSetup('/some-page');
    expect(isAuthorizedLockedEnvironment(request)).toBe(true);
});

test('should return authorized when request is on access page', () => {
    const request = quickSetup('/access');
    expect(isAuthorizedLockedEnvironment(request)).toBe(true);
});

test('should return authorized when request is on access page and environment access is restricted', () => {
    const request = quickSetup('/access', 'access-key');
    expect(isAuthorizedLockedEnvironment(request)).toBe(true);
});

test('should return authorized when request is on sorry page', () => {
    const request = quickSetup('/sorry');
    expect(isAuthorizedLockedEnvironment(request)).toBe(true);
});

test('should return authorized when request is on sorry page and environment access is restricted', () => {
    const request = quickSetup('/sorry', 'access-key');
    expect(isAuthorizedLockedEnvironment(request)).toBe(true);
});
