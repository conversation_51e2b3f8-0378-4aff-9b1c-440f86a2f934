import React from 'react';
import { GetServerSideProps } from 'next';

import NotFound from 'pages/404';

import { useCurrentUser } from 'hooks/use-current-user';

import { GenerateDatasheetBulk } from 'components/generate-datasheet/bulk/GenerateDatasheetBulk';

const CreateComponentPage = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return <GenerateDatasheetBulk />;
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default CreateComponentPage;
