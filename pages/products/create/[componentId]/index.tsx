import React, { <PERSON> } from 'react';
import { GetServerSideProps } from 'next';

import { Component } from 'models';

import { getParam } from 'helpers/get-param';
import { ComponentService } from 'services/ComponentService';
import { ComponentSignup } from 'components/component-signup/ComponentSignup';

const CreateComponentPageX: FC<{ component: Component }> = ({ component }) => {
    return <ComponentSignup component={component} />;
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const componentId = getParam(params, 'componentId')!;
    const component = await ComponentService.get(componentId, { req });

    if (!component || component.errors) {
        return {
            notFound: true,
        };
    }

    return {
        props: {
            component,
        },
    };
};

export default CreateComponentPageX;
