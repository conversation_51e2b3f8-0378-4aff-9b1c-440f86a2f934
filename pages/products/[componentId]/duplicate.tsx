import { useState } from 'react';

import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';

import { Button, Modal, SimpleGrid, Stack } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { Component } from 'models';

import { getParam } from 'helpers/get-param';
import { getId } from 'helpers/getId';

import { ComponentService } from 'services/ComponentService';

import { DatasheetMode } from 'components/datasheet';
import { ComponentDetail } from 'components/component-detail/ComponentDetail';

import { FormSubmit } from 'components/forms/FormSubmit';
import { FormStatus } from 'components/forms/FormStatus';
import { Form, FormOnSubmit } from 'components/forms/Form';
import { TextField } from 'components/forms/fields/TextField';

const DuplicateComponent = ({ component: incomingComponent }: { component: Component }) => {
    const router = useRouter();

    const [opened, handlers] = useDisclosure(true);
    const [component, setComponent] = useState(incomingComponent);

    const onSubmit: FormOnSubmit<Component> = async (values, { setSubmitError, clearErrors }) => {
        clearErrors();

        if (values.productIdentifier) {
            const existingComponent = await ComponentService.getExisting({
                type: component.type,
                productIdentifier: values.productIdentifier,
                manufacturer: getId(component.manufacturer)!,
            });

            if (existingComponent) {
                setSubmitError(`Product with part number ${values.productIdentifier} already exists`);
                return;
            }
        }

        setComponent({ ...component, ...values });
        handlers.close();
    };

    return (
        <>
            <Modal opened={opened} onClose={() => {}} title={`Duplicating ${component.name}`} withCloseButton={false}>
                <Form<Component>
                    defaultValues={{
                        name: `${component.name} (Copy)`,
                        productIdentifier: component.productIdentifier,
                        productSeries: component.productSeries,
                    }}
                    onSubmit={onSubmit}
                >
                    <Stack gap="xs">
                        <TextField name="name" label="Name" required />
                        <SimpleGrid cols={2} spacing="xs">
                            <TextField name="productIdentifier" label="Part Number" required />
                            <TextField name="productSeries" label="Product Series" />
                        </SimpleGrid>
                        <FormSubmit>Start editing specifications</FormSubmit>
                        <Button variant="transparent" onClick={router.back}>
                            Cancel
                        </Button>
                        <FormStatus />
                    </Stack>
                </Form>
            </Modal>
            <ComponentDetail
                key={component.productIdentifier} // rerender form on productIdentifier change
                component={component}
                initialMode={DatasheetMode.DUPLICATE}
            />
        </>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const componentId = getParam(params, 'componentId');

    if (!componentId) {
        return { notFound: true };
    }

    const component = await ComponentService.get(componentId, { depth: 0, req });
    const isTeamComponent = ComponentService.isTeamComponent(component);

    if (!component || isTeamComponent) {
        return { notFound: true };
    }

    return {
        props: {
            component: {
                ...component,
                id: '',
                productIdentifier: '',
            },
        },
    };
};

export default DuplicateComponent;
