import { useEffect } from 'react';

import { Component, getComponentPayloadValidator } from 'models';
import { assign, last } from 'radash';

import { getParam } from 'helpers/get-param';
import { UserService } from 'services/UserService';
import { ComponentService } from 'services/ComponentService';

import { GetServerSideProps } from 'next';
import { ComponentDetail } from 'components/component-detail/ComponentDetail';
import { ComponentDetail as TeamComponentDetail } from 'components/component-detail/ComponentDetail.Team';
import { useComponentMeta } from 'hooks/use-component-meta';
import { useCurrentUser } from 'hooks/use-current-user';
import { Metadata } from 'components/page/Metadata';
import { DatasheetMode } from 'components/datasheet';

const ComponentDetailPage = ({ component: serverComponent }: { component: Component }) => {
    const { uniqueName } = useComponentMeta(serverComponent);
    const user = useCurrentUser();

    const component = user ? serverComponent : getUnauthUserComponent(serverComponent);
    const isTeamComponent = ComponentService.isTeamComponent(serverComponent);

    useEffect(() => {
        if (!isTeamComponent) {
            UserService.pingProduct(component.id).then();
        }
    }, [component]);

    return (
        <>
            <Metadata title={uniqueName} description={component.description} />
            {isTeamComponent ? (
                <TeamComponentDetail component={component} />
            ) : (
                <ComponentDetail component={component} initialMode={DatasheetMode.VIEW} />
            )}
        </>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const componentIdWithSlug = getParam(params, 'componentId')!;
    const componentId = last(componentIdWithSlug.split('-'))!;
    const component = await ComponentService.get(componentId, { depth: 0, req });

    if (!component) {
        return { notFound: true };
    }

    return {
        props: {
            component,
        },
    };
};

const getUnauthUserComponent = (component: Component) =>
    assign(getComponentPayloadValidator(component.type).parse(component) as Component, component);

export default ComponentDetailPage;
