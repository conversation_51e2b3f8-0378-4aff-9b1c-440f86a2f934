import Link from 'next/link';
import { useSnapshot } from 'valtio';
import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import React, { FC, useEffect, useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer } from '@mantine/core';

import { Component } from 'models';

import { useComponentMeta } from 'hooks/use-component-meta';
import {
    useComponentContext,
    CurrentComponentContext,
} from 'components/component-datasheet/hooks/use-component-context';

import { sidebarNavState } from 'state/sidebar-nav';
import { componentSignupState } from 'components/component-signup/state';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { ComponentService } from 'services/ComponentService';

import { getParam } from 'helpers/get-param';

import { Page } from 'components/page';
import { FormSubmit } from 'components/forms/FormSubmit';
import { ComponentForm } from 'components/component-form';
import { AISidebar } from 'components/component-signup/components/AISidebar';
import { GenerateDatasheet } from 'components/generate-datasheet/GenerateDatasheet';

const GenerateDatasheetPage = () => {
    const { component } = useComponentContext();
    const { uniqueName } = useComponentMeta(component);

    const componentHasAi = component && component.type !== 'other';

    // close sidebar
    useEffect(() => {
        sidebarNavState.isOpen = false;

        return () => {
            sidebarNavState.isOpen = true;
        };
    }, []);

    const { aiOpened, aiOpenedExpanded } = useSnapshot(componentSignupState);
    const aiAssistantWidth = aiOpenedExpanded ? '70%' : '40%';

    const closeAiAssistant = () => {
        componentSignupState.aiOpened = false;
    };

    return (
        <Page
            hideFooter
            title={`Generate Datasheet for ${component.name}`}
            breadcrumbs={{
                showToggle: true,
                rightSection: componentHasAi ? (
                    <>
                        <Button
                            size="xs"
                            variant="subtle"
                            component={Link}
                            href={ComponentHelpers.urls.view(component.id)}
                        >
                            Discard changes
                        </Button>
                        <FormSubmit size="xs" variant="filled">
                            Save changes
                        </FormSubmit>
                    </>
                ) : (
                    <Button size="xs" component={Link} href={ComponentHelpers.urls.view(component.id)}>
                        Back to product
                    </Button>
                ),
            }}
        >
            <Page.WideContent title="Generate Datasheet" subtitle={uniqueName}>
                {componentHasAi ? <GenerateDatasheet /> : <NotAllowed />}

                <Drawer
                    keepMounted
                    position="right"
                    withCloseButton={false}
                    opened={aiOpened}
                    onClose={closeAiAssistant}
                    styles={{
                        body: { padding: 0, height: '100%' },
                    }}
                    size={aiAssistantWidth}
                >
                    <AISidebar />
                </Drawer>
            </Page.WideContent>
        </Page>
    );
};

const NotAllowed = () => {
    return (
        <Alert color="red">Sorry, this component type is not supported yet for automatic datasheet generation.</Alert>
    );
};

const WrappedGenerateDatasheetPage: FC<{ component: Component }> = ({ component: incomingComponent }) => {
    const router = useRouter();
    const [component, setComponent] = useState<Component | null>(incomingComponent ?? null);

    return (
        <CurrentComponentContext.Provider
            value={{
                component,
                setComponent,
            }}
        >
            <ComponentForm
                initialValues={incomingComponent ?? undefined}
                onUpdate={async (updatedComponent) => {
                    setComponent(updatedComponent);
                    await router.replace(ComponentHelpers.urls.view(incomingComponent.id), undefined, {
                        shallow: false,
                    });
                }}
            >
                <GenerateDatasheetPage />
            </ComponentForm>
        </CurrentComponentContext.Provider>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const componentId = getParam(params, 'componentId');

    if (!componentId) {
        return { notFound: true };
    }

    const component = await ComponentService.get(componentId, { depth: 0, req });

    if (!component) {
        return { notFound: true };
    }

    return {
        props: {
            component,
        },
    };
};

export default WrappedGenerateDatasheetPage;
