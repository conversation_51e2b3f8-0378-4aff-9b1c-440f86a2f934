import React, { FC } from 'react';

import { Component, ManufacturerProfile } from 'models';

import Head from 'next/head';
import { Stack } from '@mantine/core';

import { publicConfig } from '@public-config';

import { ComponentLanding } from 'components/component-landing';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { ApiService } from 'services/ApiService';

import { ErrorBoundary } from 'components/error-boundary/ErrorBoundary';
import { SearchPage } from 'components/search/SearchPage';
import { ComponentService } from 'services/ComponentService';
import { GetServerSideProps } from 'next';

const Search: FC<{
    recentCompanies: ManufacturerProfile[];
    highlightedCompanies: ManufacturerProfile[];
    featuredProducts: Component[];
    recentProducts: Component[];
}> = (props) => {
    const initialContent = (
        <Stack gap="xl">
            <ComponentLanding.Events />
            <ComponentLanding.Companies
                isCarousel
                companies={props.highlightedCompanies}
                title="Discover Profiles"
                compact={false}
            />
            <ComponentLanding.Companies
                isCarousel
                companies={props.recentCompanies}
                title="Recently Joined"
                subtitle="Discover the latest companies to join RE+Source PRO"
                compact={false}
            />
            <ComponentLanding.Products isCarousel products={props.featuredProducts} title="Discover Products" />
            <ComponentLanding.Products
                isCarousel
                products={props.recentProducts}
                title="Recently Added"
                subtitle="Discover the latest products added to RE+Source PRO"
            />
        </Stack>
    );

    return (
        <ErrorBoundary onError={() => InternalTrackingService.track('error.boundary.products')}>
            <SearchPage initialContent={initialContent} />
            {/*
                Make sure this head tag is behind the SearchPage
                SearchPage also has a Head element that overrides this one
            */}
            <Head>
                <title>RE+Source PRO</title>
                <meta name="description" content="Discover. Design. Deploy. Bring Microgrids to Life" />
            </Head>
        </ErrorBoundary>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
    const { docs: recentCompanies } = await CompanyProfileService.getRecentlyJoined(6, req);
    const results = await ApiService.get(`${publicConfig.urls.api}/globals/general?depth=1`, { req });

    const featuredProducts = await ComponentService.getFeaturedProducts(req);
    const { docs: recentProducts } = await ComponentService.getRecentlyAdded(8, req);

    return {
        props: {
            recentCompanies,
            highlightedCompanies: results?.highlights?.profiles || [],
            featuredProducts,
            recentProducts,
        },
    };
};

export default Search;
