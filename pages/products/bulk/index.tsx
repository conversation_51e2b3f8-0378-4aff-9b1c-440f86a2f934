import React, { FC } from 'react';
import { GetServerSideProps } from 'next';

import { ProductSeriesOverview } from 'components/component-bulk-editor/ProductSeriesOverview';
import { ComponentHelpers } from 'helpers/ComponentHelpers';

const ComponentProductSeriesSelector: FC = () => <ProductSeriesOverview />;

export const getServerSideProps: GetServerSideProps = async () => ({
    redirect: {
        destination: ComponentHelpers.urls.manage(),
        permanent: true,
    },
});

export default ComponentProductSeriesSelector;
