import React, { <PERSON> } from 'react';

import { GetServerSideProps } from 'next';

import { SavedItems } from 'components/saved-items/SavedItems';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

const Saved: FC = () => {
    return <SavedItems />;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            notFound: true,
        };
    }

    return {
        props: {},
    };
};

export default Saved;
