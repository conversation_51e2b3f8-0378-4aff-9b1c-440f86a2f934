import NotFound from 'pages/404';

import { useCurrentUser } from 'hooks/use-current-user';

import { Page } from 'components/page';
import { DiagramChatOverview } from 'components/diagram-chat-overview/DiagramChatOverview';

const DiagramChatsPage = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return (
        <Page
            showBackground
            hideFooter
            hideLicenseAgreement
            title="Diagram Chats"
            breadcrumbs={{
                isSticky: false,
                showToggle: true,
            }}
        >
            <Page.WideContent>
                <DiagramChatOverview />
            </Page.WideContent>
        </Page>
    );
};

export const getServerSideProps = async () => {
    return {
        props: {},
    };
};

export default DiagramChatsPage;
