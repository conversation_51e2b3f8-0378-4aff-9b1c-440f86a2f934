import { FC } from 'react';

import { GetServerSideProps } from 'next';

import NotFound from 'pages/404';
import { useCurrentUser } from 'hooks/use-current-user';
import { CompanyOverview } from 'components/company-overview/CompanyOverview';

const Companies: FC = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return <CompanyOverview />;
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default Companies;
