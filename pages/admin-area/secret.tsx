import React, { useState, FC } from 'react';

import DayJS from 'dayjs';

import NotFound from 'pages/404';

import useSWRImmutable from 'swr/immutable';
import { useCurrentUser } from 'hooks/use-current-user';

import { Page } from 'components/page';
import { Metadata } from 'components/page/Metadata';
import { ApiService } from 'services/ApiService';
import { BarChart } from '@mantine/charts';

import cx from 'components/stats/Stats.module.scss';

import { Group, Stack, TextInput, Title } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';

import { TbCalendar, TbSearch } from 'react-icons/tb';

import '@mantine/dates/styles.css';
import { DateService } from 'services/DateService';

const SecretPage: FC = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return (
        <Page showBackground hideFooter hideLicenseAgreement title="Stats">
            <Page.WideContent>
                <Metadata title="Stats" />
                <Secret />
            </Page.WideContent>
        </Page>
    );
};

const Secret = () => {
    const [filter, setFilter] = useState('');
    const [[start, end], setRange] = useState<[Date | null, Date | null]>([
        DayJS().endOf('day').subtract(14, 'days').toDate(),
        DayJS().endOf('day').toDate(),
    ]);

    const { data } = useSWRImmutable(
        start && end ? `/api/secret-overview?from=${start.getTime()}&to=${end.getTime()}` : null,
        (key) => {
            return ApiService.get(key);
        },
    );

    const activeUsers = data?.activeUsers || {};
    const events = data?.events || {};

    return (
        <Stack gap="xl">
            <Group>
                <TextInput
                    value={filter}
                    onChange={(event) => setFilter(event.currentTarget.value)}
                    placeholder="Filter events"
                    leftSection={<TbSearch size={16} />}
                />
                <DatePickerInput
                    type="range"
                    value={[start, end]}
                    onChange={(range) => {
                        const from = range[0] ? new Date(range[0]) : null;
                        const to = range[1] ? new Date(range[1]) : null;

                        setRange([from, to]);
                    }}
                    allowSingleDateInRange
                    placeholder="Select a date range"
                    leftSection={<TbCalendar size={16} />}
                />
            </Group>

            {start && end && activeUsers && <ActiveUsersChart from={start} to={end} activeUsers={activeUsers} />}

            {start &&
                end &&
                Object.entries(events).map(([title, timestamps]) => {
                    return !filter || title.toLowerCase().includes(filter.toLowerCase()) ? (
                        <SecretChart title={title} from={start} to={end} timestamps={timestamps as number[]} />
                    ) : null;
                })}
        </Stack>
    );
};

const ActiveUsersChart: FC<{
    from: Date;
    to: Date;
    activeUsers: { [key: string]: number[] };
}> = ({ from, to, activeUsers }) => {
    const data: any = [];
    const normalized: typeof activeUsers = {};

    Object.entries(activeUsers).forEach(([key, value]) => {
        normalized[DayJS(+key).startOf('day').valueOf()] = value;
    });

    DateService.interval(from, to).forEach((date) => {
        const startOfDay = DayJS(date).startOf('day');

        data.push({
            day: startOfDay.format('D MMM'),
            count: normalized[startOfDay.valueOf()]?.length,
        });
    });

    return (
        <Stack gap="xl" className={cx.graph}>
            <Title fw={700} fz={24} c="black">
                Active Users
            </Title>

            <BarChart h={300} data={data} dataKey="day" series={[{ name: 'count', color: 'teal' }]} />
        </Stack>
    );
};

const SecretChart: FC<{
    title: string;
    from: Date;
    to: Date;
    timestamps: number[];
}> = ({ title, from, to, timestamps }) => {
    const data: any = [];

    DateService.interval(from, to).forEach((date) => {
        data.push({
            day: DayJS(date).format('D MMM'),
            count: timestamps.filter((timestamp) => DayJS(timestamp).isSame(date, 'day')).length,
        });
    });

    return (
        <Stack gap="xl" className={cx.graph}>
            <Title fw={700} fz={24} c="black">
                {title}
            </Title>

            <BarChart h={300} data={data} dataKey="day" series={[{ name: 'count', color: 'teal' }]} />
        </Stack>
    );
};

export const getServerSideProps = async () => {
    return {
        props: {},
    };
};

export default SecretPage;
