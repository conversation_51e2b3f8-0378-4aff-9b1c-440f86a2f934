import React, { useState, FC } from 'react';

import DayJ<PERSON> from 'dayjs';
import { counting } from 'radash';

import NotFound from 'pages/404';

import useSWRImmutable from 'swr/immutable';
import { useCurrentUser } from 'hooks/use-current-user';

import { Page } from 'components/page';
import { Metadata } from 'components/page/Metadata';
import { ApiService } from 'services/ApiService';

import { Group, Stack, Switch, Table } from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';

import { TbCalendar } from 'react-icons/tb';

import '@mantine/dates/styles.css';

const SecretPage: FC = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return (
        <Page showBackground hideFooter hideLicenseAgreement title="Stats">
            <Page.WideContent>
                <Metadata title="Stats" />
                <Secret />
            </Page.WideContent>
        </Page>
    );
};

const Secret = () => {
    const [[start, end], setRange] = useState<[Date | null, Date | null]>([
        DayJS().endOf('day').subtract(14, 'days').toDate(),
        DayJS().endOf('day').toDate(),
    ]);
    const [withoutInternal, setInternalFilter] = useState<boolean>(true);

    const { data = [] } = useSWRImmutable(
        start && end ? `/api/tracking/search?from=${start.getTime()}&to=${end.getTime()}` : null,
        (key) => {
            return ApiService.get(key);
        },
    );

    const queries: string[] = data
        .filter((record: any) => (withoutInternal ? !record.metadata.internal : true))
        .map((record: any) => {
            return record.data.query.toLowerCase().trim();
        });

    const records = Object.entries(counting(queries, (x) => x))
        .map(([query, count]) => ({ query, count }))
        .filter((record) => !!record.query)
        .sort((a, b) => b.count - a.count);

    return (
        <Stack gap="xl">
            <Group>
                <DatePickerInput
                    type="range"
                    value={[start, end]}
                    onChange={(range) => {
                        const from = range[0] ? new Date(range[0]) : null;
                        const to = range[1] ? new Date(range[1]) : null;

                        setRange([from, to]);
                    }}
                    allowSingleDateInRange
                    placeholder="Select a date range"
                    leftSection={<TbCalendar size={16} />}
                />
                <Switch
                    checked={withoutInternal}
                    onChange={(e: any) => setInternalFilter(e.currentTarget.checked)}
                    label="Without internal"
                />
            </Group>
            <Table striped>
                <Table.Tbody>
                    {records.map((record) => (
                        <Table.Tr key={record.query}>
                            <Table.Td>{record.query}</Table.Td>
                            <Table.Td>{record.count}</Table.Td>
                        </Table.Tr>
                    ))}
                </Table.Tbody>
            </Table>
        </Stack>
    );
};

export const getServerSideProps = async () => {
    return {
        props: {},
    };
};

export default SecretPage;
