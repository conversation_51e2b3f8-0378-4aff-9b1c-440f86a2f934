import { FC } from 'react';

import { GetServerSideProps } from 'next';

import NotFound from 'pages/404';
import { useCurrentUser } from 'hooks/use-current-user';
import { EventOverview } from 'components/event-overview/EventOverview';

const Events: FC = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return <EventOverview />;
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default Events;
