import { FC } from 'react';

import { GetServerSideProps } from 'next';

import NotFound from 'pages/404';
import { EventDetail } from 'components/event-detail/EventDetail';

import { useCurrentUser } from 'hooks/use-current-user';
import { useRouter } from 'next/router';

const Companies: FC = () => {
    const { query } = useRouter();
    const user = useCurrentUser();

    return user?.developer ? <EventDetail slug={query.slug as string} /> : <NotFound />;
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default Companies;
