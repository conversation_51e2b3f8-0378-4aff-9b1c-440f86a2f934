import React, { <PERSON> } from 'react';

import NotFound from 'pages/404';

import { useCurrentUser } from 'hooks/use-current-user';

import { Page } from 'components/page';
import { Metadata } from 'components/page/Metadata';
import { ApiService } from 'services/ApiService';

import { Stack } from '@mantine/core';

import '@mantine/dates/styles.css';
import useSWR from 'swr';
import { BarChart } from '@mantine/charts';

const Pageviews: FC = () => {
    const user = useCurrentUser();

    const { data = { grouped: {} } } = useSWR('internal-pageviews', () => {
        return ApiService.get('/api/pageviews');
    });

    if (!user?.internal) return <NotFound />;

    const transform = (entries: any) => {
        const entriesAsArray: {
            timestamp: string;
            count: number;
        }[] = [];

        Object.entries(entries).forEach(([timestamp, count]) => {
            entriesAsArray.push({
                timestamp,
                // @ts-ignore
                count,
            });
        });

        return entriesAsArray;
    };

    return (
        <Page showBackground hideFooter hideLicenseAgreement title="Stats">
            <Page.WideContent>
                <Metadata title="Pageviews" />
                <Stack gap="xl">
                    {Object.entries(data.grouped).map(([pathname, entries]) => (
                        <div>
                            <h1>{pathname}</h1>
                            <BarChart
                                h={300}
                                data={transform(entries)}
                                dataKey="timestamp"
                                series={[{ name: 'count', color: 'teal' }]}
                            />
                        </div>
                    ))}
                </Stack>
            </Page.WideContent>
        </Page>
    );
};

export const getServerSideProps = async () => {
    return {
        props: {},
    };
};

export default Pageviews;
