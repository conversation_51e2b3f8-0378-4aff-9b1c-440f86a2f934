import NotFound from 'pages/404';

import { useCurrentUser } from 'hooks/use-current-user';

import { Page } from 'components/page';
import { Metadata } from 'components/page/Metadata';
import { ExhibitorMatch } from 'components/exhibitor-match/ExhibitorMatch';

const ExhibitorMatchPage = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return (
        <Page
            showBackground
            hideFooter
            hideLicenseAgreement
            title="Exhibitor Match Management"
            breadcrumbs={{
                isSticky: false,
                showToggle: true,
            }}
        >
            <Page.WideContent>
                <Metadata title="Exhibitor Match Management" />
                <ExhibitorMatch />
            </Page.WideContent>
        </Page>
    );
};

export const getServerSideProps = async () => {
    return {
        props: {},
    };
};

export default ExhibitorMatchPage;
