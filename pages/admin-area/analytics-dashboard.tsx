import { AppShell, Paper, Stack, Text, Title } from '@mantine/core';
import { Heatmap } from 'components/admin/analytics/heatmap/Heatmap';
import { useCurrentUser } from 'hooks/use-current-user';
import { proxy } from 'valtio';
import '@mantine/dates/styles.css';
import { DashboardDemographicsFilter } from 'components/admin/analytics/DashboardDemographicsFilter';
import { DashboardTopics } from 'components/admin/analytics/DashboardTopics';
import { DashboardPopularCompanies } from 'components/admin/analytics/DashboardPopularCompanies';
import { DashboardPopularProducts } from 'components/admin/analytics/DashboardPopularProducts';
import { Network } from 'components/admin/analytics/Network';

export const globalAnalyticsHash = proxy({
    hash: 'initial',
});

const AnalyticsDashboard = () => {
    // @ts-ignore
    window.changeGlobalHash = () => {
        globalAnalyticsHash.hash = Math.random().toString();
    };

    const user = useCurrentUser();

    if (!user?.internal) {
        return 'Access forbidden, please login.';
    }

    return (
        <AppShell
            header={{ height: 60 }} // Adjust height based on filter bar content
            padding="md"
            style={{ background: '#F6F8F9' }}
        >
            {/* Sticky Top Bar with Filters */}
            <AppShell.Header>
                <DashboardDemographicsFilter />
            </AppShell.Header>

            {/* Main Content */}
            <AppShell.Main>
                <Stack gap="xl" p="xl">
                    <Paper p="xl" bg="white" radius="md" withBorder>
                        <Stack mb={40} gap="xs">
                            <Title mt={80} fz={30}>
                                QR Scans
                            </Title>
                            <Text c="dimmed" fz="xs" fw={600} pl={2}>
                                Shows booth visitor activity as a percentile. A value of 60% means the booth ranks in
                                the top 60%, with more visits than 60% of booths.
                            </Text>
                        </Stack>
                        <Heatmap />
                    </Paper>
                    <Paper p="xl" bg="white" radius="md" withBorder>
                        <Stack mb={40} gap="xs">
                            <Title mt={40} fz={30}>
                                Network Connections
                            </Title>
                            <Text c="dimmed" fz="xs" fw={600} pl={2}>
                                Shows interactions by people of a company. Nodes are companies, and edges represent
                                peoples engagement via search, messaging, QR scans, AI chat, etc.
                            </Text>
                        </Stack>
                        <Network />
                    </Paper>
                    <Paper p="xl" bg="white" radius="md" withBorder>
                        <Stack gap="sm">
                            <Title mt={40} fz={30}>
                                Topics & Conversations
                            </Title>
                            <Text c="dimmed" fz="xs" fw={600} pl={2}>
                                Shows topics discussed in RE+Source Pro. Word size reflects topic frequency, with larger
                                words indicating more popular themes in chats. A value of 60 means the topic is in the
                                60th percentile, mentioned more often than 60% of other topics.
                            </Text>
                            <DashboardTopics />
                        </Stack>
                    </Paper>
                    <Paper p="xl" bg="white" radius="md" withBorder>
                        <Stack gap="sm">
                            <Title mt={40} fz={30}>
                                Company Analytics
                            </Title>
                            <Text c="dimmed" fz="xs" fw={600} pl={2}>
                                Shows the trend of companies popularity over time, calculated as a weighted average
                                score based on multiple engagement metrics: Impressions [how often a company is shown],
                                Clicks, Products Linked on designs, Mentions in conversations, and QR code scans. Higher
                                scores indicate greater visibility and interaction.
                            </Text>
                            <DashboardPopularCompanies />
                        </Stack>
                    </Paper>
                    <Paper p="xl" bg="white" radius="md" withBorder>
                        <Stack gap="sm">
                            <Title mt={40} fz={30}>
                                Product Analytics
                            </Title>
                            <Text c="dimmed" fz="xs" fw={600} pl={2}>
                                Shows the trend of products popularity over time, calculated as a weighted average score
                                based on multiple engagement metrics: Impressions [how often a product is shown],
                                Clicks, Products Linked on designs, Mentions in conversations. Higher scores indicate
                                greater visibility and interaction.
                            </Text>
                            <DashboardPopularProducts />
                        </Stack>
                    </Paper>
                </Stack>
            </AppShell.Main>
        </AppShell>
    );
};

export default AnalyticsDashboard;
