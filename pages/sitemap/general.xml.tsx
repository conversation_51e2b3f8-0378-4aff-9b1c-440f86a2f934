import { GetServerSideProps } from 'next';

const BASE_URL = 'https://dcide.app';

const SiteMap = () => {
    // getServerSideProps will do the heavy lifting
};

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
    const sitemap = `
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
            <url>
                <loc>${BASE_URL}/products</loc>
            </url>
            <url>
                <loc>${BASE_URL}/profiles</loc>
            </url>
        </urlset>
    `;

    res.setHeader('Content-Type', 'text/xml');
    res.write(sitemap);
    res.end();

    return {
        props: {},
    };
};

export default SiteMap;
