import { GetServerSideProps } from 'next';
import { ApiService } from 'services/ApiService';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

const BASE_URL = 'https://www.dcide.app';

import { publicConfig } from '@public-config';

const SiteMap = () => {
    // getServerSideProps will do the heavy lifting
};

export const getServerSideProps: GetServerSideProps = async ({ res, req }) => {
    const { products } = await ApiService.get<{
        products: {
            id: string;
            name: string;
            productSeries: string;
            productIdentifier: string;
        }[];
    }>(`${publicConfig.urls.api}/globals/sitemap`, { req });

    const urls: string[] = [];

    products.forEach((product) => {
        const { slug } = ComponentHelpers.generateMetadata(product);

        urls.push(`<url><loc>${BASE_URL}${ComponentHelpers.urls.view(product.id, slug)}</loc></url>`);
    });

    const sitemap = `
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
            ${urls.join('\n')}
        </urlset>
    `;

    res.setHeader('Content-Type', 'text/xml');
    res.write(sitemap);
    res.end();

    return {
        props: {},
    };
};

export default SiteMap;
