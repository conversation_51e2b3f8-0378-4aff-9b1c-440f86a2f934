import React, { FC, useEffect } from 'react';

import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';

import { Loader } from '@mantine/core';
import { Page } from 'components/page';

import { InternalTrackingService } from 'services/InternalTrackingService';
import { RouterService } from 'services/RouterService';

const Dispatcher: FC = () => {
    const { query } = useRouter();

    const { url, source } = query as {
        url: string;
        source: string;
    };

    useEffect(() => {
        InternalTrackingService.track('referral', {
            url,
            source,
        });

        RouterService.push(url || '').then();
    }, []);

    return (
        <Page>
            <Page.CenteredContent>
                <Loader size="md" color="blue" />
            </Page.CenteredContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default Dispatcher;
