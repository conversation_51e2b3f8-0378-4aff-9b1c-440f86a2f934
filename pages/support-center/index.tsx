import React, { FC } from 'react';

import { GetServerSideProps } from 'next';

import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

import { Loader } from '@mantine/core';
import { Page } from 'components/page/Page';

import { HorizontalTabs } from 'components/horizontal-tabs';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { SupportCenter } from 'components/support-center/SupportCenter';
import { SidebarNavBadge } from 'components/sidebar-nav/components/SidebarNav.Badge';

import { useSupportCenter } from 'hooks/use-support-center';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { SupportChannel } from 'services/SupportCenterService';

import { SupportCenterHelpers } from 'helpers/SupportCenterHelpers';

const SupportCenterPage: FC = () => {
    const { supportCenterChannels, isLoading } = useSupportCenter();

    const sortedSupportChannels = supportCenterChannels.sort((a, b) => {
        return SupportCenterHelpers.getUnreadCount(b) - SupportCenterHelpers.getUnreadCount(a);
    });

    return (
        <Page title="Lead Management" showBackground>
            <Page.WideContent>
                {isLoading && <Loader m="auto" size="sm" />}

                {!isLoading && supportCenterChannels.length === 0 && (
                    <EmptyMessage>You are not a support user for any company.</EmptyMessage>
                )}

                {supportCenterChannels.length === 1 ? (
                    <SupportCenterTab supportChannel={supportCenterChannels[0]} />
                ) : supportCenterChannels.length ? (
                    <HorizontalTabs
                        tabs={sortedSupportChannels.map((supportChannel: any) => ({
                            label: (
                                <>
                                    {supportChannel.company.name}{' '}
                                    <SidebarNavBadge ml={4}>
                                        {SupportCenterHelpers.getUnreadCount(supportChannel)}
                                    </SidebarNavBadge>
                                </>
                            ),
                            value: supportChannel.company.slug,
                            content: <SupportCenterTab supportChannel={supportChannel} />,
                        }))}
                    />
                ) : null}
            </Page.WideContent>
        </Page>
    );
};

const SupportCenterTab = ({ supportChannel }: { supportChannel: SupportChannel }) => {
    const { company } = useCompanyProfile(supportChannel.company);

    if (!company) {
        return null;
    }

    return <SupportCenter company={company} />;
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: '/login',
                permanent: true,
            },
        };
    }

    return {
        props: {},
    };
};

export default SupportCenterPage;
