import React, { FC } from 'react';
import { GetServerSideProps } from 'next';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { getParam } from 'helpers/get-param';

const Design: FC = () => <div />;

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const componentId = getParam(params, 'componentId')!;

    return {
        redirect: {
            destination: ComponentHelpers.urls.view(componentId),
            permanent: true,
        },
    };
};

export default Design;
