import { useEffect } from 'react';

import { UserReferrer } from 'models';

import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next/types';

import { Skeleton } from '@mantine/core';

import { Page } from 'components/page';
import { GridSection } from 'components/section/GridSection';
import { TeaserLoader } from 'components/loaders/TeaserLoader';

import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';
import { RouterHelpers } from 'helpers/RouterHelpers';

import { useLocalUserInfo } from 'hooks/use-local-user-info';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { publicConfig } from '@public-config';

const DashboardPage = () => {
    const router = useRouter();

    const team = useCurrentTeam();
    const user = useCurrentUser();
    const { referrer } = useLocalUserInfo();

    useEffect(() => {
        router.replace(RouterHelpers.urls.homepage(team, user, referrer)).then();
    }, [team, user, referrer]);

    return (
        <Page>
            <Page.WideContent>
                <Skeleton height={200} />
                <GridSection nbCols={4}>
                    <TeaserLoader />
                    <TeaserLoader />
                    <TeaserLoader />
                    <TeaserLoader />
                </GridSection>
            </Page.WideContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: publicConfig.urls.landingPage,
                permanent: false,
            },
        };
    }

    return {
        props: {},
    };
};

export default DashboardPage;
