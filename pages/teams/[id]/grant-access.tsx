import React, { <PERSON> } from 'react';

import { GetServerSideProps } from 'next';

import { Team, User } from 'models';

import { GrantTeamAccess } from 'components/grant-team-access/GrantTeamAccess';

import { TeamService } from 'services/TeamService';
import { UserService } from 'services/UserService';

import { TeamHelpers } from 'helpers/TeamHelpers';

import { getParam } from 'helpers/get-param';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

const GrantAccess: FC<{ team: Team; user: User }> = ({ team, user }) => {
    return <GrantTeamAccess team={team} user={user} />;
};

export const getServerSideProps: GetServerSideProps = async ({ req, params, query }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: `/login?redirect=${TeamHelpers.urls.grantAccess(getParam(params, 'id')!)}`,
                permanent: false,
            },
        };
    }

    const teamId = getParam(params, 'id')!;
    const userId = getParam(query, 'userId')!;

    const [team, user] = await Promise.all([TeamService.get(teamId, req), UserService.get(userId, req)]);

    if (!team || !user) {
        return { notFound: true };
    }

    return {
        props: {
            team,
            user,
        },
    };
};

export default GrantAccess;
