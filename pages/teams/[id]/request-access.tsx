import React, { useState } from 'react';

import { GetServerSideProps } from 'next';

import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Stack, Text, Title } from '@mantine/core';

import { Team } from 'models';

import { getParam } from 'helpers/get-param';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

import { TeamService } from 'services/TeamService';
import { TeamHelpers } from 'helpers/TeamHelpers';

import { useCurrentTeam } from 'hooks/use-current-team';

import { Page } from 'components/page';

const TeamAccess = ({ team }: { team: Team }) => {
    const currentTeam = useCurrentTeam();

    const [loading, setLoading] = useState(false);
    const [accessRequested, setAccessRequested] = useState(false);

    const requestAccess = async () => {
        setLoading(true);

        await TeamService.requestAccess(team.id);

        setAccessRequested(true);
        setLoading(false);
    };

    return (
        <Page showBackground title="Request access">
            <Page.CenteredContent maxWidth={500}>
                <Card withBorder p="xl">
                    {team.id === currentTeam?.id ? (
                        <Title>You&apos;re already part of this team.</Title>
                    ) : (
                        <Stack>
                            <Title>Request access to {team.name}</Title>
                            <Text>If you work for this company, request access to join the {team.name} team.</Text>
                            <Text>They will review your request and notify you once your request is approved.</Text>
                            <Button fullWidth onClick={requestAccess} loading={loading} disabled={accessRequested}>
                                Request access
                            </Button>

                            {accessRequested && (
                                <Alert color="green">
                                    Your request has been submitted. You will receive a notification once your request
                                    is approved.
                                </Alert>
                            )}
                        </Stack>
                    )}
                </Card>
            </Page.CenteredContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ req, params }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: `/login?redirect=${TeamHelpers.urls.requestAccess(getParam(params, 'id')!)}`,
                permanent: false,
            },
        };
    }

    const team = await TeamService.get(getParam(params, 'id')!, req);

    if (!team) {
        return { notFound: true };
    }

    return {
        props: {
            team,
        },
    };
};

export default TeamAccess;
