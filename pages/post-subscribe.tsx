import { useEffect, useState } from 'react';

import { GetServerSideProps } from 'next';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';

import { Loader, Text, Transition } from '@mantine/core';
import { useInterval } from '@mantine/hooks';

import { LocalNotificationService } from 'services/LocalNotificationService';
import { TeamService } from 'services/TeamService';

import { Page } from 'components/page';

import { useCurrentTeam } from 'hooks/use-current-team';
import { LocalStorageService } from 'services/LocalStorageService';

export const POST_SUBSCRIBE_REDIRECT_STORAGE_KEY = 'postSubscribeRedirect';

const PostSubscribePage = () => {
    const team = useCurrentTeam();
    const redirectUrl = LocalStorageService.get(POST_SUBSCRIBE_REDIRECT_STORAGE_KEY);

    const searchParams = useSearchParams();
    const customerId = searchParams?.get('customerId');

    useEffect(() => {
        if (customerId) {
            TeamService.postCreateSubscription(customerId);
        }
    }, []);

    const [seconds, setSeconds] = useState(0);
    const interval = useInterval(() => setSeconds((s) => s + 1), 1000);

    useEffect(() => {
        interval.start();

        return interval.stop;
    }, []);

    useEffect(() => {
        if (!team) {
            return;
        }

        const handle = async () => {
            LocalNotificationService.showSuccess({ message: 'Subscription sign up successful!' });

            await TeamService.subscriptionRefresh(team.id);

            if (searchParams?.has('customerId')) {
                await TeamService.postCreateSubscription(searchParams.get('customerId') as string);
            }

            if (redirectUrl) {
                LocalStorageService.clear(POST_SUBSCRIBE_REDIRECT_STORAGE_KEY);
                window.location = redirectUrl as any;
            } else {
                window.location = '/' as any;
            }
        };

        handle().then();
    }, [team, searchParams]);

    return (
        <Page.CenteredContent>
            <Loader color="primary" />

            <Text c="dimmed" className="animation-pulse">
                Redirecting...
            </Text>

            <Transition mounted={seconds > 5} transition="fade">
                {(transitionStyles) => (
                    <Text c="gray.5" fz="xs" style={transitionStyles}>
                        Is this taking too long?{' '}
                        <Text inherit component={Link} href={redirectUrl || '/'} td="underline">
                            Click here
                        </Text>{' '}
                        to go to the {redirectUrl ? 'redirect page' : 'home page'}.
                    </Text>
                )}
            </Transition>
        </Page.CenteredContent>
    );
};

export const getServerSideProps: GetServerSideProps = async () => ({
    props: {},
});

export default PostSubscribePage;
