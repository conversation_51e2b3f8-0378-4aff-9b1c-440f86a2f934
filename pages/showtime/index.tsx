import React, { <PERSON> } from 'react';

import { GetServerSideProps } from 'next';

import { Showtime } from 'components/showtime/Showtime';

const ShowtimePage: FC = () => {
    return <Showtime />;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
    if (!query.type || !query.referrer) {
        return {
            redirect: {
                destination: `/showtime?referrer=replus&type=${query.type ?? 'designer'}${query.profile ? `&profile=${query.profile}` : ''}`,
                permanent: true,
            },
        };
    }

    return {
        props: {},
    };
};

export default ShowtimePage;
