import React, { <PERSON> } from 'react';

import { Title } from '@mantine/core';

import { Page } from 'components/page';
import { SubscriptionFeatures } from 'components/subscriptions/subscription-features/SubscriptionFeatures';

const Upgrade: FC = () => (
    <Page showBackground>
        <Title my={80} fz={48} fw={800} ta="center">
            Plans
        </Title>
        <SubscriptionFeatures />
    </Page>
);

export const getServerSideProps = async () => ({
    props: {},
});

export default Upgrade;
