import React, { FC } from 'react';

import { UserReferrer } from 'models';

import { LoginForm } from 'components/login-form';

import { GetServerSideProps } from 'next';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';
import { LoginLayout } from 'components/login-layout/LoginLayout';

import { useLocalUserInfo } from 'hooks/use-local-user-info';
import { useRouter } from 'next/router';

const Login: FC = () => {
    const { query } = useRouter();

    const message = (
        <>
            New or existing user?
            <br />
            We will send you a login link, no credit card needed.
        </>
    );
    const { referrer } = useLocalUserInfo();

    const isREPlusVisitor = referrer === UserReferrer.REPLUS;

    let redirect = isREPlusVisitor ? '/resourcepro' : '/';

    if (query.redirect) {
        redirect = query.redirect as string;
    }

    return (
        <LoginLayout message={message}>
            <LoginForm redirect={redirect} />
        </LoginLayout>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    if (isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: '/',
            },
            props: {},
        };
    }

    return {
        props: {},
    };
};

export default Login;
