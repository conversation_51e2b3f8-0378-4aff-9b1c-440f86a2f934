import React, { useEffect, FC } from 'react';

import { useRouter } from 'next/router';

import { AuthenticationService } from 'services/AuthenticationService';

import { Page } from 'components/page';

const Logout: FC = () => {
    const router = useRouter();

    useEffect(() => {
        AuthenticationService.logout().then();
    }, [router]);

    return (
        <Page showBackground hideLicenseAgreement title="Log out">
            <Page.CenteredContent>Logging out...</Page.CenteredContent>
        </Page>
    );
};

export const getServerSideProps = async () => {
    return {
        props: {},
    };
};

export default Logout;
