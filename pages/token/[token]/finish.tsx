import React, { <PERSON> } from 'react';

import { GetServerSideProps } from 'next';

import { getParam } from 'helpers/get-param';

import { UserSignup } from 'components/user-signup/UserSignup';

const Finish: FC<{ magicToken: string }> = ({ magicToken }) => {
    return <UserSignup magicToken={magicToken} />;
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const magicToken = getParam(params, 'token');

    return {
        props: {
            magicToken,
        },
    };
};

export default Finish;
