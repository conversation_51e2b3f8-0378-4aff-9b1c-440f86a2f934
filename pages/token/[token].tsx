import React, { FC, useEffect, useState } from 'react';

import { GetServerSideProps } from 'next';

import { getParam } from 'helpers/get-param';

import { AuthenticationService } from 'services/AuthenticationService';
import { LoginLayout } from 'components/login-layout/LoginLayout';
import { RouterService } from 'services/RouterService';
import { useCurrentUser } from 'hooks/use-current-user';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';
import { RequestHelpers } from 'helpers/RequestHelpers';

type RequestState =
    | { status: 'waiting' }
    | { status: 'success'; user: { name?: string } }
    | { status: 'error'; message: string };

const Finish: FC<{ magicToken: string; queryRedirect?: string }> = ({ magicToken, queryRedirect }) => {
    const currentUser = useCurrentUser();

    const [authRequestState, setAuthRequestState] = useState<RequestState>(
        currentUser
            ? {
                  status: 'success',
                  user: currentUser,
              }
            : { status: 'waiting' },
    );

    useEffect(() => {
        AuthenticationService.login(magicToken)
            .then((user) => {
                setAuthRequestState({
                    status: 'success',
                    user: user,
                });
            })
            .catch((error) => {
                setAuthRequestState({
                    status: 'error',
                    message: error.message,
                });
            });
    }, [magicToken]);

    if (authRequestState.status === 'error') {
        return <LoginLayout isMinimal>Error logging in with magic token. Please try again.</LoginLayout>;
    }

    const user = currentUser ?? (authRequestState.status === 'success' ? authRequestState.user : null);

    if (user) {
        RouterService.replace(user.name ? queryRedirect || '/' : `/token/${magicToken}/finish`);
    }

    return <LoginLayout isMinimal>Logging in...</LoginLayout>;
};

export const getServerSideProps: GetServerSideProps = async ({ params, query, req }) => {
    const magicToken = getParam(params, 'token');

    const user = req.headers['x-user'] ? RequestHelpers.decode(req.headers['x-user'] as string) : null;
    if (user && isAuthenticatedRequest(req)) {
        return user.name
            ? {
                  redirect: {
                      destination: query.redirect || user.meta?.redirect || '/',
                  },
                  props: {},
              }
            : {
                  redirect: {
                      destination: `/token/${magicToken}/finish`,
                  },
                  props: {},
              };
    }

    return {
        props: {
            magicToken,
            queryRedirect: query.redirect || null,
        },
    };
};

export default Finish;
