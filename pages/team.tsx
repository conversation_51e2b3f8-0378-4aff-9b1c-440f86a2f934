import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';
import { GetServerSideProps } from 'next';
import React, { FC } from 'react';

const Team: FC = () => <React.Fragment></React.Fragment>;

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            notFound: true,
        };
    }

    return {
        redirect: {
            destination: '/account#team',
            permanent: true,
        },
        props: {},
    };
};

export default Team;
