import React, { FC } from 'react';

import { GetServerSideProps } from 'next';

import { Page } from 'components/page';
import { CompanyImport } from 'components/company-import/CompanyImport';

import { useCurrentUser } from 'hooks/use-current-user';

const CompanyImportPage: FC = () => {
    const user = useCurrentUser();

    return (
        <Page
            showBackground
            breadcrumbs={{
                isSticky: true,
            }}
            title="Import"
        >
            <Page.Content>{user?.developer || user?.internal ? <CompanyImport /> : 'No access'}</Page.Content>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default CompanyImportPage;
