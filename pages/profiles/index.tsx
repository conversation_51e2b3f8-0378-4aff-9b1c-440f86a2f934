import React, { FC } from 'react';

import { GetServerSideProps } from 'next';
import { CompanyProfile, PublishedStatus } from 'models';

import { Page } from 'components/page';
import { GridSection } from 'components/section/GridSection';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

import { CompanyProfileService } from 'services/CompanyProfileService';

const Profiles: FC<{
    profiles: CompanyProfile[];
}> = ({ profiles }) => (
    <Page
        showBackground
        breadcrumbs={{
            isSticky: true,
        }}
        title="Profiles"
    >
        <Page.Content>
            <GridSection nbCols={3}>
                {profiles.map((profile) => (
                    <CompanyProfileTeaser company={profile} key={profile.id} />
                ))}
            </GridSection>
        </Page.Content>
    </Page>
);

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
    const { docs: profiles } = await CompanyProfileService.list({ status: PublishedStatus.PUBLISHED }, req);

    return {
        props: {
            profiles,
        },
    };
};

export default Profiles;
