import React, { FC } from 'react';

import { GetServerSideProps } from 'next';

import { Alert, Text } from '@mantine/core';

import { useLivePreview } from '@payloadcms/live-preview-react';

import { ManufacturerProfile } from 'models';

import { publicConfig } from '@public-config';
import { getParam } from 'helpers/get-param';

import NotFound from 'pages/404';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { Page } from 'components/page';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { CompanyProfile } from 'components/company-profile/CompanyProfile';

const ManufacturerPage: FC<{ manufacturer?: ManufacturerProfile; slug: string }> = ({ manufacturer, slug }) => {
    const user = useCurrentUser();
    const team = useCurrentTeam();

    const { data } = useLivePreview({
        serverURL: publicConfig.urls.api,
        initialData: manufacturer || {},
        depth: 0,
    });

    if (!data) {
        return (
            <Page showBackground hideLicenseAgreement title={manufacturer?.name}>
                <Page.Content>
                    <Alert color="orange" title="Manufacturer not found">
                        Manufacturer by slug{' '}
                        <Text span fw={600}>
                            {slug}
                        </Text>{' '}
                        not found. Check if the manufacturer has a slug.
                    </Alert>
                </Page.Content>
            </Page>
        );
    }

    // Fix for useLivePreview always returning objects with depth=0
    // @ts-ignore
    const cleanData: ManufacturerProfile = {
        ...data,
        // @ts-ignore
        highlightedComponents: data.highlightedComponents?.map((component: any) =>
            typeof component === 'string' ? component : component.id,
        ),
    };

    if (user?.adminPanel || manufacturer?.team === team?.id) {
        return <CompanyProfile company={cleanData} />;
    }

    return <NotFound />;
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const slug = getParam(params, 'slug');

    if (!slug) {
        return { notFound: true };
    }

    const manufacturers = await CompanyProfileService.getBySlug(slug, undefined, req);
    const manufacturer = manufacturers?.docs?.[0] ?? null;

    return {
        props: {
            manufacturer: manufacturer ?? null,
            slug,
        },
    };
};

export default ManufacturerPage;
