import React, { useEffect, useState } from 'react';

import { GetServerSideProps } from 'next';

import { Alert, Button, Card, SimpleGrid, Stack, Text, Title } from '@mantine/core';

import { CompanyProfile } from 'models';

import { getParam } from 'helpers/get-param';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

import { UserService } from 'services/UserService';
import { CompanyProfileService } from 'services/CompanyProfileService';

import { useAction } from 'hooks/use-action';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';
import { useProfilePreview } from 'components/company-profile/hooks/use-profile-preview';
import { useTeam } from 'hooks/use-team';

import { Page } from 'components/page';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { TeamHelpers } from 'helpers/TeamHelpers';

const ManufacturerAccess = ({ company }: { company: CompanyProfile }) => {
    const currentUser = useCurrentUser();
    const currentTeam = useCurrentTeam();
    const { team: companyTeam } = useTeam(company.team);

    const [loading, setLoading] = useState(false);
    const { previewSecret, previewSecretIsValid } = useProfilePreview(company);

    const [requestAccess, requesting, requested] = useAction(async () => {
        await CompanyProfileService.requestAccess(company.id);
    });

    const [claimAccess, claiming, claimed] = useAction(async () => {
        await CompanyProfileService.moveTeam(company.id, {
            userId: currentUser!.id,
            teamId: currentTeam!.id,
            companyId: company.id,
            previewSecret,
        });

        await CompanyProfileService.navigate.view(company.slug);
    });

    const requestingOrClaiming = loading || requesting || claiming;
    const requestedOrClaimed = requested || claimed;

    useEffect(() => {
        if (!currentUser || !currentTeam || !companyTeam) {
            return;
        }

        if (companyTeam.id === currentTeam.id) {
            setLoading(true);
            CompanyProfileService.navigate.view(company.slug).then();

            return;
        }

        if (TeamHelpers.isTeamMember(companyTeam, currentUser)) {
            setLoading(true);
            UserService.switchTeam(companyTeam.id).then((result) => {
                if (result?.success) {
                    CompanyProfileService.navigate.view(company.slug).then();
                }
            });
        }
    }, [currentUser, currentTeam, companyTeam, company.slug]);

    return (
        <Page showBackground title="Request access">
            <Page.CenteredContent maxWidth={900}>
                <SimpleGrid
                    pos="relative"
                    cols={{
                        base: 1,
                        md: 2,
                    }}
                    spacing={8}
                >
                    <CompanyProfileTeaser company={company} />

                    <Card withBorder p="xl">
                        <Stack>
                            {company.internal ? (
                                <>
                                    <Title>Claim profile for {company.name}</Title>
                                    <Text>
                                        If you work for this company, claim this profile to manage the {company.name}{' '}
                                        profile, products and reference designs.
                                    </Text>
                                    {!previewSecretIsValid && (
                                        <Text>
                                            We will review your request and notify you once your request is approved.
                                        </Text>
                                    )}
                                    <Button
                                        fullWidth
                                        onClick={previewSecretIsValid ? claimAccess : requestAccess}
                                        loading={requestingOrClaiming}
                                        // disabled={requestedOrClaimed}
                                    >
                                        Claim this profile
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Title>Request access to {company.name}</Title>
                                    <Text>
                                        If you work for this company, request access to manage the {company.name}{' '}
                                        profile, products and reference designs.
                                    </Text>
                                    <Text>The admin of {company.name} will receive your request for approval.</Text>
                                    <Button
                                        fullWidth
                                        onClick={requestAccess}
                                        loading={requestingOrClaiming}
                                        disabled={requestedOrClaimed}
                                    >
                                        Request access
                                    </Button>
                                </>
                            )}

                            {requested && (
                                <Alert color="green">
                                    Your request has been submitted. You will receive a notification once your request
                                    is approved.
                                </Alert>
                            )}

                            {claimed && (
                                <Alert color="green">
                                    Your profile has been claimed.
                                    <br />
                                    You will be redirected to the profile page.
                                </Alert>
                            )}
                        </Stack>
                    </Card>
                </SimpleGrid>
            </Page.CenteredContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ req, params, query }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: `/login?redirect=${CompanyProfileHelpers.urls.requestAccess(getParam(params, 'slug')!, getParam(query, 'preview') || '')}`,
                permanent: false,
            },
        };
    }

    const slug = getParam(params, 'slug')!;

    const companies = await CompanyProfileService.getBySlug(slug, undefined, req);
    const company = companies?.docs?.[0] ?? null;

    if (!company) {
        return { notFound: true };
    }

    return {
        props: {
            company,
        },
    };
};

export default ManufacturerAccess;
