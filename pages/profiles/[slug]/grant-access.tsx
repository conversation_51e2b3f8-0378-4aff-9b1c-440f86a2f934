import React from 'react';

import { GetServerSideProps } from 'next';

import { CompanyProfile } from 'models';

import { getParam } from 'helpers/get-param';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { GrantCompanyAccess } from 'components/grant-company-access/GrantCompanyAccess';

const ManufacturerAccess = ({ company }: { company: CompanyProfile }) => {
    return <GrantCompanyAccess company={company} />;
};

export const getServerSideProps: GetServerSideProps = async ({ req, params }) => {
    if (!isAuthenticatedRequest(req)) {
        return { notFound: true };
    }

    const slug = getParam(params, 'slug');

    if (!slug) {
        return { notFound: true };
    }

    const companies = await CompanyProfileService.getBySlug(slug, undefined, req);
    const company = companies?.docs?.[0] ?? null;

    if (!company) {
        return { notFound: true };
    }

    return {
        props: {
            company,
        },
    };
};

export default ManufacturerAccess;
