import React, { FC, useEffect } from 'react';

import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';

import { ManufacturerProfile, PublishedStatus } from 'models';

import { getParam } from 'helpers/get-param';

import NotFound from 'pages/404';

import { useCurrentTeam } from 'hooks/use-current-team';
import { useCurrentUser } from 'hooks/use-current-user';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { CompanyProfile } from 'components/company-profile/CompanyProfile';
import { RouterService } from 'services/RouterService';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CryptoHelpers } from 'helpers/CryptoHelpers';

const ManufacturerPage: FC<{ company: ManufacturerProfile }> = ({ company }) => {
    const { query } = useRouter();

    const user = useCurrentUser();
    const team = useCurrentTeam();

    // TODO: remove when we fully move from Manufacturers/Distributors to Profile
    useEffect(() => {
        // No problem if manufacturer is already set
        if (company) {
            return;
        }

        const currentPath = window.location.pathname;

        // Current path is not a redirect to /profile
        if (!currentPath.includes('/manufacturers')) {
            return;
        }

        // router.query not available when using a redirect from a URL that doesn't match a route
        const slug = window.location.pathname.split('/').reverse()?.[0];

        if (slug) {
            const url = CompanyProfileHelpers.urls.view(slug);
            RouterService.replace(url).then();
        }
    }, [company]);

    const userIsDeveloper = user?.developer;
    const userIsCompanyTeamMember = company?.team === team?.id;
    const companyIsPublished = company?.status === PublishedStatus.PUBLISHED;
    const isPreviewUrl = query.preview === CryptoHelpers.unsafeSimpleHash(company?.slug);

    return userIsDeveloper || userIsCompanyTeamMember || companyIsPublished || isPreviewUrl ? (
        <CompanyProfile company={company} />
    ) : (
        <NotFound
            message={
                company && !companyIsPublished ? 'Profile is not published yet. Please check back later.' : undefined
            }
        />
    );
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const slugOrId = getParam(params, 'slug')!;

    const companies = await CompanyProfileService.getBySlug(slugOrId, undefined, req);
    const company = companies?.docs?.[0] ?? null;

    if (!company) {
        return { notFound: true };
    }

    return {
        props: {
            company,
        },
    };
};

export default ManufacturerPage;
