import React from 'react';

import { GetServerSideProps } from 'next';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { Page } from 'components/page';

import { GridSection } from 'components/section/GridSection';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

const ManageProfiles = () => {
    const { companies } = useCurrentTeamCompanies();
    return (
        <Page title="My profiles">
            <Page.Content>
                <GridSection nbCols={2}>
                    {companies.map((company) => (
                        <CompanyProfileTeaser key={company.id} company={company} />
                    ))}
                </GridSection>
            </Page.Content>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default ManageProfiles;
