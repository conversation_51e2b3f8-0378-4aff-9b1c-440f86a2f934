import { FC, useEffect } from 'react';
import { GetServerSideProps } from 'next';

import { useSearchPlanPage } from 'hooks/use-search-plan-page';

import { SearchPlanPage } from 'components/search-plan/SearchPlanPage';

const Plan: FC = () => {
    const { type, setType } = useSearchPlanPage();

    useEffect(() => {
        if (type === 'search') {
            setType('plan');
        }
    }, []);

    return <SearchPlanPage />;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
    if (!query.type || !query.referrer) {
        return {
            redirect: {
                destination: `/resourcepro/plan?referrer=replus&type=${query.type ?? 'designer'}`,
                permanent: true,
            },
        };
    }

    return {
        props: {},
    };
};

export default Plan;
