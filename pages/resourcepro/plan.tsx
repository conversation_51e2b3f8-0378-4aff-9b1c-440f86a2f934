import { FC } from 'react';
import { GetServerSideProps } from 'next';

import { PlanPage } from 'components/plan/PlanPage';

const Plan: FC = () => {
    return <PlanPage />;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
    if (!query.type || !query.referrer) {
        return {
            redirect: {
                destination: `/resourcepro/plan?referrer=replus&type=${query.type ?? 'designer'}`,
                permanent: true,
            },
        };
    }

    return {
        props: {},
    };
};

export default Plan;
