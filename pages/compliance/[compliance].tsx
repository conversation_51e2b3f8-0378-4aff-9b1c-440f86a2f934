import { Title } from '@mantine/core';
import { Compliance, Compliances } from 'models';
import { ComplianceProducts } from 'components/compliance/ComplianceProducts';
import { Page } from 'components/page';
import { FormatHelpers } from 'helpers/formatters';
import { getParam } from 'helpers/get-param';
import { GetServerSideProps } from 'next';

type CompliancePageProps = {
    compliance: Compliance;
};

const CompliancePage = ({ compliance }: CompliancePageProps) => {
    const complianceLabel = FormatHelpers.formatOption(Compliances.options, compliance);

    return (
        <Page showBackground hideLicenseAgreement title={complianceLabel}>
            <Page.FullScreenContent>
                <Page.Hero>
                    <Title>{complianceLabel}</Title>
                </Page.Hero>

                <ComplianceProducts compliance={compliance} />
            </Page.FullScreenContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps<CompliancePageProps> = async ({ params }) => {
    const complianceParam = getParam(params, 'compliance')?.toLowerCase();
    const compliance = getComplianceFromParam(complianceParam);

    if (!compliance) {
        return { notFound: true };
    }

    return {
        props: { compliance },
    };
};

const getComplianceFromParam = (param: string | undefined) => {
    switch (param) {
        case 'emerge':
            return Compliance.EMERGE;
        case 'current-os':
            return Compliance.CURRENT_OS;
        case 'odca':
            return Compliance.ODCA;
        case 'ce':
            return Compliance.CE;
        case 'ul':
            return Compliance.UL;

        default:
            return null;
    }
};

export default CompliancePage;
