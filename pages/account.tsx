import React from 'react';

import { Button } from '@mantine/core';
import { TbLogout } from 'react-icons/tb';

import { AuthenticationService } from 'services/AuthenticationService';
import { Page } from 'components/page/Page';
import { Account } from 'components/account/Account';
import { SubscriptionChangeNotice } from 'components/account/SubscriptionModals';

import { useCurrentUser } from 'hooks/use-current-user';
import { GetServerSideProps } from 'next';

const AccountPage = () => {
    const user = useCurrentUser();

    const handleLogout = async () => {
        await AuthenticationService.logout();
    };

    return user ? (
        <Page
            title="Settings"
            showBackground
            breadcrumbs={{
                rightSection: (
                    <Button
                        size="xs"
                        variant="subtle"
                        color="red"
                        onClick={handleLogout}
                        leftSection={<TbLogout size={14} />}
                    >
                        Logout
                    </Button>
                ),
            }}
        >
            <Page.Content title="Settings">
                <Account />
            </Page.Content>
            <SubscriptionChangeNotice />
        </Page>
    ) : null;
};

export const getServerSideProps: GetServerSideProps = async () => ({
    props: {},
});

export default AccountPage;
