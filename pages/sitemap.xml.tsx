import { GetServerSideProps } from 'next';

import { DateService } from 'services/DateService';

const BASE_URL = 'https://dcide.app';

const SiteMap = () => {
    // getServerSideProps will do the heavy lifting
};

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
    const date = DateService.format(new Date(), 'YYYY-MM-DD');
    const sitemap = `
        <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
            <sitemap>
                <loc>${BASE_URL}/sitemap/general.xml</loc>
                <lastmod>${date}</lastmod>
            </sitemap>
            <sitemap>
                <loc>${BASE_URL}/sitemap/products.xml</loc>
                <lastmod>${date}</lastmod>
            </sitemap>
            <sitemap>
                <loc>${BASE_URL}/sitemap/profiles.xml</loc>
                <lastmod>${date}</lastmod>
            </sitemap>
        </sitemapindex>
    `;

    res.setHeader('Content-Type', 'text/xml');
    res.write(sitemap);
    res.end();

    return {
        props: {},
    };
};

export default SiteMap;
