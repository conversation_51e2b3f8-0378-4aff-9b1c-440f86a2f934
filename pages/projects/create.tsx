import React, { <PERSON> } from 'react';

import { GetServerSideProps } from 'next';

import { Button, Group, Loader, Paper, Select, Space, Stack } from '@mantine/core';
import { openContextModal } from '@mantine/modals';

import { CreateProjectMetadataSchema, FeatureLimit, Team, TeamInfo } from 'models';

import { Page } from 'components/page';
import { FeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';
import { Form } from 'components/forms/Form';
import { TextField } from 'components/forms/fields/TextField';
import { FormSubmit } from 'components/forms/FormSubmit';
import { ModalTitle } from 'components/modals/ModalTitle';

import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

import { useCurrentTeam } from 'hooks/use-current-team';
import { useFeatureLimit } from 'hooks/use-feature-limit';
import { useCreateProjectTeams } from 'hooks/use-create-project-teams';
import { useAction } from 'hooks/use-action';
import { useRouterQuery } from 'hooks/use-router-query';

import { UserService } from 'services/UserService';
import { RouterService } from 'services/RouterService';
import { ProjectService } from 'services/ProjectService';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

type Values = {
    team: string;
    name: string;
};

const Create: FC<{
    teamProjectCounts: {
        [key: string]: number;
    };
}> = ({ teamProjectCounts }) => {
    const query = useRouterQuery();
    const { name, startFromExistingProject, referenceComponent, isReferenceDesign, manufacturer } =
        CreateProjectMetadataSchema.parse(query);

    const team = useCurrentTeam();

    const { createTeams, isLoading } = useCreateProjectTeams();

    const projectCount = teamProjectCounts[team?.id ?? ''] || 0;
    const maxNumberOfProjectsCanCreate = useFeatureLimit(FeatureLimit.PROJECTS);
    const reachedMaxProjects = projectCount >= maxNumberOfProjectsCanCreate;

    const hasCreateTeams = createTeams.length > 0;
    const canCreateInCurrentTeam = !!createTeams.find((needle) => needle.id === team?.id);

    const action = startFromExistingProject ? 'duplicate' : 'create';

    const openCreateTeamModal = () => {
        openContextModal({
            modal: 'createTeam',
            innerProps: {},
            withCloseButton: false,
            centered: true,
        });
    };

    const create = async (values: Values) => {
        let response = null;

        if (startFromExistingProject) {
            response = await ProjectService.duplicate(startFromExistingProject, values.name, isReferenceDesign);
        } else {
            response = await ProjectService.create({
                name: values.name,
                metadata: {
                    referenceComponent,
                    manufacturer,
                    isReferenceDesign: Boolean(isReferenceDesign || referenceComponent || manufacturer),
                },
            } as any);
        }

        if (!response?.doc) {
            throw new Error('Failed to create project');
        }

        const project = await ProjectService.get(response.doc.id);
        let url = ProjectHelpers.urls.editor(project.id);

        if (startFromExistingProject) {
            url += '?startedFromReferenceDesign=true';
        }

        await RouterService.replace(url);
    };

    if (isLoading) {
        return (
            <Wrapper action={action}>
                <Group justify="center">
                    <Loader color="blue" />
                </Group>
            </Wrapper>
        );
    }

    // The user has no team (is only a collaborator in another team)
    if (!team || !hasCreateTeams) {
        return (
            <Wrapper action={action}>
                <Stack gap="xs">
                    Create a team to continue.
                    <Button onClick={openCreateTeamModal}>Create team</Button>
                </Stack>
            </Wrapper>
        );
    }

    // The user is not allowed to create a project in the current team
    if (!canCreateInCurrentTeam) {
        return (
            <Wrapper action={action}>
                <Stack>
                    You do not have permission to create a project in this team.
                    <TeamSwitcher team={team} teams={createTeams} />
                </Stack>
            </Wrapper>
        );
    }

    return (
        <Wrapper action={action}>
            <Form<Values>
                onSubmit={create}
                defaultValues={{
                    name: ProjectService.generateName({ name, startFromExistingProject }),
                }}
            >
                <Stack gap="xs">
                    {reachedMaxProjects && <FeatureLimitTracker feature={FeatureLimit.PROJECTS} />}
                    {createTeams.length > 1 && <TeamSwitcher team={team} teams={createTeams} />}
                    <TextField name="name" label="Name" />
                    <FormSubmit disabled={reachedMaxProjects}>
                        {startFromExistingProject ? 'Duplicate' : 'Create'}
                    </FormSubmit>
                </Stack>
            </Form>
        </Wrapper>
    );
};

const TeamSwitcher: FC<{
    team: Team;
    teams: TeamInfo[];
}> = ({ team, teams }) => {
    const [switchToTeam, switching] = useAction(async (team: string) => {
        await UserService.switchTeam(team);
        await RouterService.refresh();
    });

    return (
        <Select
            name="team"
            label="Team"
            data={teams.map((team) => ({ value: team.id, label: team.name }))}
            value={team.id}
            onChange={async (value) => {
                if (value) {
                    await switchToTeam(value);
                }
            }}
            rightSection={switching && <Loader size="xs" color="blue" />}
        />
    );
};

const Wrapper: FC<{
    action: 'create' | 'duplicate';
    children: React.ReactNode;
}> = ({ action, children }) => (
    <Page showBackground title="Create project">
        <Page.CenteredContent>
            <Paper w="100%" maw={420} p="xl" shadow="lg">
                <ModalTitle>{action === 'create' ? 'Create project' : 'Duplicate project'}</ModalTitle>
                <Space h="lg" />
                {children}
            </Paper>
        </Page.CenteredContent>
    </Page>
);

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            notFound: true,
        };
    }

    const teamProjectCounts = await ProjectService.getTeamProjectCounts(req);

    return {
        props: {
            teamProjectCounts,
        },
    };
};

export default Create;
