import { getParam } from 'helpers/get-param';

import { GetServerSideProps } from 'next';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

const ProjectEdit = () => {
    return null;
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const projectId = getParam(params, 'projectId')!;

    return {
        redirect: {
            destination: ProjectHelpers.urls.editor(projectId),
            permanent: true,
        },
    };
};

export default ProjectEdit;
