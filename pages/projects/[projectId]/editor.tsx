import React, { FC, useEffect } from 'react';

import { Diagram } from 'components/diagram';
import {
    type Project,
    type ProjectDesign,
    type Diagram as DiagramType,
    PermissionProject,
    ProjectSchema,
} from 'models';

import { ApiService } from 'services/ApiService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { UserService } from 'services/UserService';

import { getParam } from 'helpers/get-param';
import { ProjectRestore } from 'components/project/components';
import { Pointer } from 'sync-engine';
import { GetServerSideProps } from 'next';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

import { state as currentProjectState } from 'state/current-project';
import { state as currentProjectDesignState } from 'state/current-project-design';
import { state as commentState, commentStateDefaults } from 'components/diagram/state/comment';
import { simulationState, simulationStateDefaults } from 'components/diagram/state/simulation';
import { validationState, validationStateDefaults } from 'components/diagram/state/validation';
import { wireSizingState, wireSizingStateDefaults } from 'components/diagram/state/wire-sizing';
import { chatState, chatStateDefaults } from 'components/diagram/state/chat';
import { state as selectionState, selectionStateDefaults } from 'components/diagram/state/selection';
import { permissionsState, setPermissions } from 'state/current-project-permissions';
import { sidebarNavState } from 'state/sidebar-nav';

import { StateHelpers } from 'helpers/StateHelpers';

import { publicConfig } from '@public-config';
import { useGlobalState } from 'hooks/use-global-state';
import { ModalService } from 'services/ModalService';

const ProjectEditor: FC<{
    project: Project;
    design: ProjectDesign;
    diagram: DiagramType;
    pointer: Pointer;
}> = ({ project, design, diagram, pointer }) => {
    useGlobalState(currentProjectState, 'project', project, null);
    useGlobalState(currentProjectDesignState, 'projectDesign', design, null);

    setPermissions(project);

    useEffect(() => {
        sidebarNavState.isFloating = true;
        sidebarNavState.isOpen = false;

        return () => {
            sidebarNavState.isFloating = false;
            sidebarNavState.isOpen = true;
        };
    }, [project]);

    useEffect(() => {
        if (project.permissionsAs !== 'anonymous') {
            UserService.pingProject(project.id).then();
        }
    }, [project]);

    useEffect(() => {
        const published = project.visibility === 'marketplace';
        const editable =
            project.permissions.includes(PermissionProject.ALL) ||
            project.permissions.includes(PermissionProject.ADMIN) ||
            project.permissions.includes(PermissionProject.EDIT);

        if (published && editable) {
            ModalService.open({
                title: 'Attention',
                children: (
                    <div>
                        You&apos;re about to make changes to a <strong>published</strong> reference design.
                        <br />
                        Please remember that any changes you make will be publicly visible.
                    </div>
                ),
            });
        }
    }, [project.id]);

    useEffect(() => {
        setPermissions(project);

        SidebarService.reset();

        return () => {
            permissionsState.permissions = {};

            StateHelpers.reset(chatState, chatStateDefaults);
            StateHelpers.reset(commentState, commentStateDefaults);
            StateHelpers.reset(selectionState, selectionStateDefaults);
            StateHelpers.reset(simulationState, simulationStateDefaults);
            StateHelpers.reset(validationState, validationStateDefaults);
            StateHelpers.reset(wireSizingState, wireSizingStateDefaults);

            LocalNotificationService.clean();
        };
    }, [project, design]);

    if (project.status === 'deleted') {
        return <ProjectRestore project={project} />;
    }

    return <Diagram preload={diagram} pointer={pointer} />;
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const projectId = getParam(params, 'projectId')!;

    const redirect = {
        redirect: {
            destination: ProjectHelpers.urls.requestAccess(projectId),
        },
        props: {},
    };

    try {
        const preload = await ApiService.get(`${publicConfig.urls.api}/projects/${projectId}/preload`, { req });

        if (!preload) {
            return redirect;
        }

        if (preload.project?.context?.teamSwitched) {
            // When fetching a project we switch the user's team in the backend
            // The problem is we've already fetched our user information in _app.getInitialProps
            // This data is outdated so we simply "refresh" our page to get the latest data
            return {
                redirect: {
                    destination: ProjectHelpers.urls.editor(projectId),
                },
                props: {},
            };
        }

        return {
            props: {
                ...preload,
                project: ProjectSchema.pick({
                    wireSizing: true,
                })
                    .passthrough()
                    .parse(preload.project),
            },
        };
    } catch (error) {
        console.error('Error generating server side props for editor', error);

        return redirect;
    }
};

export default ProjectEditor;
