import React, { FC } from 'react';
import { GetServerSideProps } from 'next';

import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { getParam } from 'helpers/get-param';

const Diagram: FC = () => <div />;

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const projectId = getParam(params, 'projectId')!;

    return {
        redirect: {
            destination: ProjectHelpers.urls.editor(projectId),
            permanent: true,
        },
    };
};

export default Diagram;
