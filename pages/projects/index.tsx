import React, { FC } from 'react';

import { GetServerSideProps } from 'next';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

import { ProjectOverview } from 'components/project-overview';

const ProjectOverviewPage: FC = () => {
    return <ProjectOverview />;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: '/login',
                permanent: true,
            },
        };
    }

    return {
        props: {},
    };
};

export default ProjectOverviewPage;
