import React, { FC, useEffect } from 'react';

import Head from 'next/head';

import { InternalTrackingService } from 'services/InternalTrackingService';

import { useSearchPlanPage } from 'hooks/use-search-plan-page';

import { ErrorBoundary } from 'components/error-boundary/ErrorBoundary';
import { GetServerSideProps } from 'next';
import { SearchPlanPage } from 'components/search-plan/SearchPlanPage';

const Search: FC = () => {
    const { type, setType } = useSearchPlanPage();

    useEffect(() => {
        if (type === 'plan') {
            setType('search');
        }
    }, []);

    return (
        <ErrorBoundary onError={() => InternalTrackingService.track('error.boundary.products')}>
            <SearchPlanPage />
            {/*
                Make sure this head tag is behind the SearchPage
                SearchPage also has a Head element that overrides this one
            */}
            <Head>
                <title>RE+Source PRO</title>
                <meta name="description" content="Discover. Design. Deploy. Bring Microgrids to Life" />
            </Head>
        </ErrorBoundary>
    );
};

export const getServerSideProps: GetServerSideProps = async () => {
    return {
        props: {},
    };
};

export default Search;
