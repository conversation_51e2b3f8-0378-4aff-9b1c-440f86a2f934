import React, { FC } from 'react';

import { GetServerSideProps } from 'next';

import { DesignOverview } from 'components/project-overview/DesignOverview';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';

const ProjectOverviewPage: FC = () => {
    return <DesignOverview />;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            redirect: {
                destination: '/login',
                permanent: true,
            },
        };
    }

    return {
        props: {},
    };
};

export default ProjectOverviewPage;
