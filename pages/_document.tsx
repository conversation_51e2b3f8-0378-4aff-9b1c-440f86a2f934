import { Html, <PERSON>, Main, NextScript } from 'next/document';

import Script from 'next/script';

import { ColorSchemeScript, mantineHtmlProps } from '@mantine/core';

import { publicConfig } from '@public-config';

const Document = () => {
    return (
        <Html lang="en" {...mantineHtmlProps}>
            <Head>
                <ColorSchemeScript forceColorScheme="light" />
                {/* Define isSpace function globally to fix markdown-it issues with Next.js + Turbopack */}
                {/* https://github.com/markdown-it/markdown-it/issues/1082 */}
                {/* https://github.com/vercel/next.js/issues/76802 */}
                <Script id="markdown-it-fix" strategy="beforeInteractive">
                    {`
                        if (typeof window !== 'undefined' && typeof window.isSpace === 'undefined') {
                            window.isSpace = function(code) {
                                return code === 0x20 || code === 0x09 || code === 0x0A || code === 0x0B || code === 0x0C || code === 0x0D;
                            };
                        }
                    `}
                </Script>
            </Head>
            {publicConfig.environment === 'production' && publicConfig.analyticsPublicKey && (
                <Script id="google-tag-manager" strategy="afterInteractive">
                    {`
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${publicConfig.analyticsPublicKey}');
                `}
                </Script>
            )}
            <body>
                <Main />
                <NextScript />
            </body>
        </Html>
    );
};

export default Document;
