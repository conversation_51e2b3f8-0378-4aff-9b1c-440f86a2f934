import React, { <PERSON> } from 'react';

import { OrderService } from 'services/OrderService';

import { OrderOverview } from 'components/order-overview/OrderOverview';
import { Order } from 'models';
import { GetServerSideProps } from 'next';

const OrderOverviewPage: FC<{ orders: Order[] }> = ({ orders }) => {
    return <OrderOverview orders={orders} />;
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
    const orders = await OrderService.list({ depth: 1, limit: 10, req });

    if (!orders || orders.errors) {
        return {
            notFound: true,
        };
    }

    return {
        props: { orders: orders.docs },
    };
};

export default OrderOverviewPage;
