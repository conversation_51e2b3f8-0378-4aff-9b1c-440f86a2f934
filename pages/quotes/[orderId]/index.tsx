import React, { <PERSON> } from 'react';

import { getParam } from 'helpers/get-param';
import { Order } from 'models';
import { OrderService } from 'services/OrderService';
import { GetServerSideProps } from 'next';
import { OrderDetail } from 'components/order/OrderDetail';

const OrderDetailPage: FC<{ order: Order }> = ({ order }) => {
    return <OrderDetail order={order} />;
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const orderId = getParam(params, 'orderId');

    if (!orderId) {
        return { notFound: true };
    }

    const order = await OrderService.get(orderId, undefined, req);

    if (!order || order.errors) {
        return { notFound: true };
    }

    return {
        props: { order },
    };
};

export default OrderDetailPage;
