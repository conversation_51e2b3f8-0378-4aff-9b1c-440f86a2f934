import React, { FC } from 'react';

import { Paper, Space } from '@mantine/core';

import { LocalNotificationService } from 'services/LocalNotificationService';

import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next';
import { isAuthenticatedRequest } from 'helpers/is-authenticated-request';
import { CreateOrderForm } from 'components/create-order-form/CreateOrderForm';
import { Page } from 'components/page/Page';

const OrderOverview: FC = () => {
    const router = useRouter();

    return (
        <Page showBackground>
            <Page.CenteredContent title="Create a new order">
                <Paper radius="sm" p="xl" shadow="xl" style={{ width: '100%', maxWidth: 460 }}>
                    <Space h="xl" />
                    <CreateOrderForm
                        onCreate={(order) => {
                            LocalNotificationService.showSuccess({
                                message: `Order ${order.name} successfully created.`,
                            });

                            const url = router.query.redirect
                                ? `${location.origin}${router.query.redirect}`
                                : `/quotes/${order.id}`;

                            router.replace(url);
                        }}
                    />
                </Paper>
            </Page.CenteredContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
    if (!isAuthenticatedRequest(req)) {
        return {
            notFound: true,
        };
    }

    return {
        props: {},
    };
};

export default OrderOverview;
