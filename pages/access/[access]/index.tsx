import React, { FC } from 'react';

import { Text } from '@mantine/core';
import { getParam } from 'helpers/get-param';

import { GetServerSideProps } from 'next';
import { LoginLayout } from 'components/login-layout/LoginLayout';
import { setEnvironmentLockAccessCookie } from 'helpers/setAccess';
import { SaveAccessTokenToLocalStorage } from 'components/SaveAccessTokenToLocalStorage';

const AccessPage: FC<{ message: string }> = ({ message }) => {
    return (
        <LoginLayout isMinimal>
            <Text>{message}</Text>
            <SaveAccessTokenToLocalStorage />
        </LoginLayout>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ res, params }) => {
    const environmentLockAccess = getParam(params, 'access');

    return setEnvironmentLockAccessCookie(res, environmentLockAccess);
};

export default AccessPage;
