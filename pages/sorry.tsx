import { FC } from 'react';

import { Box, Flex, Paper } from '@mantine/core';

const Sorry: FC = () => (
    <Flex
        align="center"
        justify="center"
        style={(theme) => ({
            minHeight: '100dvh',
            backgroundColor: theme.colors.gray[0],
        })}
    >
        <Paper
            radius="sm"
            p="xl"
            shadow="xl"
            style={() => ({
                width: '100%',
                maxWidth: 460,
            })}
        >
            <Box style={{ textAlign: 'center' }}>
                We are sorry, currently DCIDE is only available in the EU and the US.
            </Box>
        </Paper>
    </Flex>
);

export const getServerSideProps = async () => ({
    props: {},
});

export default Sorry;
