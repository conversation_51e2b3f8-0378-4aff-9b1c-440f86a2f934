import { getParam } from 'helpers/get-param';

import { GetServerSideProps } from 'next';

import { getId } from 'helpers/getId';
import { ArticleService } from 'services/ArticleService';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

const Article = () => {
    return null;
};

export const getServerSideProps: GetServerSideProps = async ({ params, req }) => {
    const articleId = getParam(params, 'articleId')!;
    const article = await ArticleService.get(articleId, req);

    if (!article || !article.company) {
        return {
            notFound: true,
        };
    }

    const hash = article.type === 'caseStudy' ? '#caseStudies' : article.type === 'promotion' ? '#promos' : '';

    return {
        redirect: {
            destination: CompanyProfileHelpers.urls.view(getId(article.company)!) + `?article=${article.id}${hash}`,
            permanent: true,
        },
    };
};

export default Article;
