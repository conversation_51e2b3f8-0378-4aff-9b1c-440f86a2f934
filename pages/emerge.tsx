import React from 'react';
import { z } from 'zod';

import { Stack, Space, Text, Paper } from '@mantine/core';

import { FormSubmissionType } from 'models';

import { Page } from 'components/page/Page';
import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import { TextField } from 'components/forms/fields/TextField';
import { SelectField } from 'components/forms/fields/SelectField';
import { FormConditional } from 'components/forms/FormConditional';

import { ApiService } from 'services/ApiService';
import { RouterHelpers } from 'helpers/RouterHelpers';
import { RouterService } from 'services/RouterService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { publicConfig } from '@public-config';

const Emerge = () => {
    return (
        <Page title="Emerge" showBackground hideLicenseAgreement>
            <Page.CenteredContent maxWidth={460}>
                <img
                    src="https://www.emergealliance.org/wp-content/uploads/2020/01/cropped-eMergeLogo_inhouse.png"
                    style={{ width: '60%', margin: '0 auto var(--mantine-spacing-xs)' }}
                />
                <Paper w="100%" radius="sm" p="xl" shadow="xl">
                    <Text>
                        <strong>DCIDE IDM Registration Request</strong>
                    </Text>
                    <Space h="xs" />
                    <Text>
                        Please use this form to register microgrid component products in the EMerge Alliance-sponsored
                        open-source Interoperability Data Model (IDM) Registry.
                    </Text>
                    <Space h="lg" />
                    <EmergeForm />
                </Paper>
            </Page.CenteredContent>
        </Page>
    );
};

const EmergeForm = () => {
    const submit = async (values: any) => {
        await ApiService.post(`${publicConfig.urls.api}/formSubmissions`, {
            type: FormSubmissionType.EMERGE,
            name: values.name,
            content: values,
        });

        LocalNotificationService.showSuccess({
            message: 'Thank you for your submission!',
        });

        await RouterService.push(RouterHelpers.urls.search());
    };

    return (
        <Form
            onSubmit={submit}
            zodSchema={z.object({
                name: z.string().min(3),
                email: z.string().email(),
                company: z.string().optional(),
                jobTitle: z.string().optional(),
                jobRole: z.string().optional(),
                jobRoleOther: z.string().optional(),
            })}
        >
            <Stack gap="xs">
                <TextField name="name" label="Full Name" required />
                <TextField name="email" label="E-mail" required />
                <TextField name="company" label="Company" />
                <TextField name="jobTitle" label="Job Title" />
                <SelectField
                    name="jobRole"
                    label="Role"
                    data={[
                        { label: 'Designer', value: 'Designer' },
                        { label: 'Manufacturer', value: 'Manufacturer' },
                        { label: 'Project Developer', value: 'Project Developer' },
                        { label: 'Other', value: 'Other' },
                    ]}
                />
                <FormConditional condition={({ jobRole }) => jobRole === 'Other'}>
                    <TextField name="jobRoleOther" placeholder="Please specify your job role" />
                </FormConditional>
                <FormSubmit>Continue to add your product</FormSubmit>
            </Stack>
        </Form>
    );
};

export default Emerge;
