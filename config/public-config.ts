import { z } from 'zod';

import { urlConfig, urlConfigSchema } from './url-config';
import { environmentLockAccess, environmentLockAccessSchema } from './environment-lock-access';
import { domain, domainSchema } from './domain';

import { getValue } from 'models';

const publicConfigSchema = z.object({
    environment: z.string().default('development'),
    environmentLockAccess: environmentLockAccessSchema,
    domain: domainSchema,
    urls: urlConfigSchema,
    sentryPublicKey: z.string().optional(),
    ablyPublicKey: z.string().optional(),
    userSnapPublicKey: z.string().optional(),
    mapboxPublicKey: z.string().optional(),
    imageKitPublicKey: z.string().optional(),
    analyticsPublicKey: z.string().optional(),
    aiFilterTimeout: z.number(),
});

export type PublicConfig = z.infer<typeof publicConfigSchema>;

export const publicConfig: PublicConfig = publicConfigSchema.parse({
    environment: process.env.ENVIRONMENT,
    environmentLockAccess,
    domain,
    urls: urlConfig,
    sentryPublicKey: process.env.NEXT_PUBLIC_SENTRY,
    ablyPublicKey: process.env.NEXT_PUBLIC_ABLY_API_KEY,
    analyticsPublicKey: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS,
    userSnapPublicKey: process.env.NEXT_PUBLIC_USERSNAP_SPACE_API_KEY,
    mapboxPublicKey: process.env.NEXT_PUBLIC_MAPBOX_API_KEY,
    imageKitPublicKey: process.env.NEXT_PUBLIC_IMAGEKIT,
    aiFilterTimeout: getValue('SEARCH_AI_FILTER_TIMEOUT', 2500),
});
