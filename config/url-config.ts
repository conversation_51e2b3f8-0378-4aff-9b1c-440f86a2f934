import { z } from 'zod';

export const urlConfigSchema = z.object({
    frontend: z.string(),
    backend: z.string(),
    api: z.string(),
    ai: z.string(),
    landingPage: z.string(),
});

export const urlConfig = urlConfigSchema.parse({
    ai: process.env.NEXT_PUBLIC_AI_URL,
    landingPage: process.env.NEXT_PUBLIC_LANDING_PAGE,
    api: process.env.NEXT_PUBLIC_BACKEND_URL,
    frontend: process.env.NEXT_PUBLIC_FRONTEND_URL,
    backend: process.env.NEXT_PUBLIC_SERVER_URL,
});
