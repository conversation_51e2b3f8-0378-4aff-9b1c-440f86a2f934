{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/node_modules/**", "!**/.next", "!**/.vercel", "!**/.git-blame-ignore-revs", "!**/lib", "!**/dist", "!**/build", "!**/(payload)", "!**/payload-types.ts"]}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 120, "attributePosition": "auto", "bracketSpacing": true}, "assist": {"actions": {"source": {"organizeImports": "off"}}}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noBannedTypes": "error", "noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error", "noAdjacentSpacesInRegex": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInnerDeclarations": "error", "noInvalidConstructorSuper": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "error", "useExhaustiveDependencies": "off", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error", "noInvalidBuiltinInstantiation": "error", "useValidTypeof": "error"}, "style": {"noNamespace": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useArrayLiterals": "off"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "warn", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "warn", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "noWith": "error"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "preserve", "trailingCommas": "all", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}}, "overrides": [{"includes": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off", "noInvalidBuiltinInstantiation": "off"}, "style": {"useConst": "error"}, "suspicious": {"noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off", "noVar": "error"}, "complexity": {"noArguments": "error"}}}}, {"includes": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off", "noInvalidBuiltinInstantiation": "off"}, "style": {"useConst": "error"}, "suspicious": {"noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off", "noVar": "error"}, "complexity": {"noArguments": "error"}}}}, {"includes": ["**/*.yml", "**/package.json", "**/biome.json"], "formatter": {"indentWidth": 2}}]}