@use 'sass:math';

// Define variables for your breakpoints,
// values must be the same as in your theme
$mantine-breakpoint-xs: '30em';
$mantine-breakpoint-sm: '48em';
$mantine-breakpoint-md: '64em';
$mantine-breakpoint-lg: '74em';
$mantine-breakpoint-xl: '90em';
$mantine-breakpoint-xxl: '100em';

@function rem($value) {
    @return #{math.div(math.div($value, $value * 0 + 1), 16)}rem;
}

@mixin light {
    [data-mantine-color-scheme='light'] & {
        @content;
    }
}

@mixin dark {
    [data-mantine-color-scheme='dark'] & {
        @content;
    }
}

@mixin hover {
    @media (hover: hover) {
        &:hover {
            @content;
        }
    }

    @media (hover: none) {
        &:active {
            @content;
        }
    }
}

@mixin smaller-than($breakpoint) {
    @media (max-width: $breakpoint) {
        @content;
    }
}

@mixin larger-than($breakpoint) {
    @media (min-width: $breakpoint) {
        @content;
    }
}

// Add direction mixins if you need rtl support
@mixin rtl {
    [dir='rtl'] & {
        @content;
    }
}

@mixin ltr {
    [dir='ltr'] & {
        @content;
    }
}
