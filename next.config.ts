import sass from 'sass';

import { SentryBuildOptions, withSentryConfig } from '@sentry/nextjs';
import { withPayload } from '@payloadcms/next/withPayload';
import { NextConfig } from 'next';

const environment =
    process.env.VERCEL_ENV === 'production'
        ? 'production'
        : process.env.VERCEL_ENV === 'preview'
          ? 'staging'
          : 'development';

const frontendUrl =
    environment === 'development' ? 'http://localhost:3001' : (process.env.NEXT_PUBLIC_FRONTEND_URL as string);

const nextConfig: NextConfig = {
    transpilePackages: ['dcide-component-models', 'dcide-sync-engine'],
    sassOptions: {
        prependData: `@use "./_mantine.scss" as *;`,
        logger: sass.Logger.silent,
    },
    async redirects() {
        return [
            {
                source: '/manufacturers/:slug*',
                destination: '/profiles/:slug*',
                permanent: true,
            },
            {
                source: '/manufacturers/signup',
                destination: '/profiles/signup',
                permanent: true,
            },
            {
                source: '/manufacturers/signup/:id',
                destination: '/profiles/signup/:id',
                permanent: true,
            },
            {
                source: '/distributors/:slug*',
                destination: '/profiles/:slug*',
                permanent: true,
            },
            {
                source: '/distributors/signup',
                destination: '/profiles/signup',
                permanent: true,
            },
            {
                source: '/distributors/signup/:id',
                destination: '/profiles/signup/:id',
                permanent: true,
            },
            {
                source: '/products/search',
                destination: '/search',
                permanent: true,
            },
            {
                source: '/profile/:slug',
                destination: '/profiles/:slug',
                permanent: true,
            },
            {
                source: '/profile/:slug/request-access',
                destination: '/profiles/:slug/request-access',
                permanent: true,
            },
            {
                source: '/profile/:slug/grant-access',
                destination: '/profiles/:slug/grant-access',
                permanent: true,
            },
            {
                source: '/profile/:slug/preview',
                destination: '/profiles/:slug/preview',
                permanent: true,
            },
        ];
    },
    headers: async () => [
        {
            source: '/api/:path*',
            headers: [
                { key: 'Access-Control-Allow-Credentials', value: 'true' },
                { key: 'Access-Control-Allow-Origin', value: frontendUrl },
                { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
                {
                    key: 'Access-Control-Allow-Headers',
                    value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, baggage, sentry-trace, source-next-config',
                },
            ],
        },
    ],
    productionBrowserSourceMaps: true,
    experimental: {
        serverSourceMaps: true,
    },
};

const sentryConfig: SentryBuildOptions = {
    silent: !process.env.CI,
    org: 'direct-energy-partners',
    project: `dcide-app-${environment}`,
    widenClientFileUpload: true,
    // @ts-expect-error
    transpileClientSDK: false,
    hideSourceMaps: false,
    disableLogger: true,
    reactComponentAnnotation: {
        enabled: true,
    },
    tunnelRoute: '/monitoring',
    automaticVercelMonitors: true,
    ...(environment !== 'development' ? { authToken: process.env.SENTRY_AUTH_TOKEN } : {}),
};

const config = withPayload(withSentryConfig(nextConfig, sentryConfig));

export default config;
