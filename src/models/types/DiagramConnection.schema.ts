import { z } from 'zod';

import {
    CableLinesValidator,
    CableArrangement,
    CableToCableClearance,
    CableValidator,
    ConductorMaterial,
    Current,
    DuctToDuctClearance,
    InsulationMaterial,
    Length,
    PowerFactor,
    Resistance,
    Temperature,
    Voltage,
    WireSize,
    CableSupportType,
    CableOrientation,
    ThermalResistivity,
} from '../validators';

import { AnchorSchema } from './Anchor.schema';

import {
    DefaultIECInstallationMethod,
    DefaultIECReferenceInstallationMethod,
    getDefaultTemperature,
    IECInstallationMethodValidator,
    IECReferenceInstallationMethod,
    isUnderground,
    NECInstallationMethod,
} from './InstallationType';

import { ConnectionCores } from './ConnectionCores';
import { isNumber } from 'radash';

export const DiagramConnectionAnchorSchema = AnchorSchema;

const emptyNumberToNull = (value: unknown) => {
    if (typeof value !== 'number') {
        return null;
    }

    return value;
};

// TODO: refactor in specifications and configuration
export const DiagramConnectionSchema = z
    .object({
        id: z.string(),
        cableId: z.string().nullable().default(null),
        label: z.string().default(''),
        notes: z.string().default(''),
        from: AnchorSchema,
        to: AnchorSchema,
        voltage: Voltage.validator, // TODO: remove, requirements.voltage
        current: Current.validator, // TODO: remove
        lines: CableLinesValidator.default({}),
        multiple: z
            .object({
                parallel: z.preprocess((value) => {
                    if (!isNumber(value)) {
                        return 1;
                    }

                    return value < 1 ? 1 : value;
                }, z.number().int().min(1)),
            })
            .default({}),
        wireSize: WireSize.validator,
        length: z.preprocess((value) => {
            // config
            return value ? value : undefined;
        }, Length.validator.default({})),
        resistance: z.preprocess((value) => {
            // config
            return value ? value : undefined;
        }, Resistance.validator.default({})),

        conductorMaterial: z
            .union([ConductorMaterial.validator, ConductorMaterial.NECValidator])
            .nullable()
            .default('copper'),
        insulationMaterial: InsulationMaterial.validator.nullable().default('XLPE'),
        conductorTemperature: Temperature.validators.value,
        ambientTemperature: Temperature.validators.value, // config
        cores: z.nativeEnum(ConnectionCores).default(ConnectionCores.SINGLE),
        powerFactor: PowerFactor.validator, // config
        requirements: z // config
            .object({
                voltage: Voltage.validators.value,
                voltageDrop: z.number().min(0).max(1).nullable().default(null),
                current: Current.validators.value,
            })
            .default({}),
        installation: z
            .object({
                referenceMethod: z
                    .union([z.nativeEnum(NECInstallationMethod), z.nativeEnum(IECReferenceInstallationMethod)])
                    .nullable()
                    .default(DefaultIECReferenceInstallationMethod),
                method: z
                    .union([z.nativeEnum(NECInstallationMethod), IECInstallationMethodValidator])
                    .nullable()
                    .default(DefaultIECInstallationMethod),
                cableOrientation: CableOrientation.validator.nullable().default('horizontal'),
                cableSupportType: CableSupportType.validator.nullable().default(null),
                cableArrangement: CableArrangement.validator.nullable().default('bunched').catch('bunched'),
                cableToCableClearance: CableToCableClearance.validator.nullable().default('touching').catch('touching'),
                ductToDuctClearance: DuctToDuctClearance.validator.nullable().default('ducts-touching'),
                numberOfTraysOrLadders: z.preprocess(emptyNumberToNull, z.number().int().min(1).nullable()).default(1),
                cablesInVicinity: z.preprocess(emptyNumberToNull, z.number().int().min(0).nullable()).default(0),
                temperature: Temperature.validators.value.default({
                    value: getDefaultTemperature(isUnderground(IECReferenceInstallationMethod.A1)),
                    unit: 'K',
                }),
                soilThermalResistivity: ThermalResistivity.validator
                    .default({
                        value: 2.5,
                        unit: 'Km/W',
                    })
                    .nullable()
                    .catch(() => {
                        return null;
                    }),
                correctionFactorOverride: z.preprocess(emptyNumberToNull, z.number().nullable()).default(null),
                numberOfDucts: z
                    .object({
                        horizontal: z.preprocess(emptyNumberToNull, z.number().int().min(1).nullable()).default(1),
                        vertical: z.preprocess(emptyNumberToNull, z.number().int().min(1).nullable()).default(1),
                    })
                    .default({}),
            })
            .default({}),
    })
    .merge(
        CableValidator.shape.electrical._def.innerType.pick({
            resistancePerLength: true,
            numberOfConductors: true,
            hasPE: true,
        }),
    );
