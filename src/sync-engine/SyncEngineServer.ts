import { SyncEngine } from './SyncEngine';

import { Pointer, Transaction, getTransactions, getState, setServerState, validateState } from './types';

export class SyncEngineServer<State extends object> extends SyncEngine<State> {
    protected setState: setServerState<State>;
    private getTransactions: getTransactions;

    constructor({
        getState,
        setState,
        getTransactions,
        validateState,
    }: {
        getState: getState<State>;
        setState: setServerState<State>;
        getTransactions: getTransactions;
        validateState?: validateState<State>;
    }) {
        super({ getState, validateState });
        this.setState = setState;
        this.getTransactions = getTransactions;
    }

    pull = async (transactions: Transaction[], pointer: Pointer) => {
        const transactionsToPush = await this.push(pointer);

        const state = this.getState();

        transactions.forEach((transaction) => {
            const { success, errors } = this.apply(transaction, state);

            // Key principle: only the server can validate transactions
            transaction.status = success ? 'validatedByServer' : 'invalidatedByServer';

            if (!success) {
                transaction.errors = errors;
            }
        });

        const validTransactions = transactions.filter((transaction) => transaction.status === 'validatedByServer');

        if (validTransactions.length > 0) {
            await this.setState(state, validTransactions);
        }

        return {
            // Return all transactions, including the ones that have been rejected to the client who submitted them originally (validated === false)
            transactionsToReturn: [...transactionsToPush, ...transactions],
            // Return new transactions that have been validated
            transactionsToBroadcast: validTransactions,
        };
    };

    push = async (pointer: Pointer) => {
        // For optimization, getTransactions can return a filtered list of transactions directly
        const { transactions, filtered } = await this.getTransactions(pointer);

        if (pointer.id === '' || filtered) {
            return transactions;
        }

        const indexOfLastTransaction = transactions.findIndex((transaction) => transaction.id === pointer.id);

        if (indexOfLastTransaction === -1) {
            return [];
        }

        const transactionsAfterId = transactions.slice(indexOfLastTransaction + 1);

        // Test 'apply-transactions-in-reversed-order' fails if transactionsAfterTimestamp are returned
        // const transactionsAfterTimestamp = transactions.filter((transaction) => transaction.date > pointer.date);

        return transactionsAfterId;
    };
}
