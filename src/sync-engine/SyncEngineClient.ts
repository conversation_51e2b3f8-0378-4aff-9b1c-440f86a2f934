import { SyncEngine } from './SyncEngine';

import {
    Change,
    Pointer,
    Transaction,
    TransactionCallback,
    TransactionId,
    broadcastTransaction,
    getState,
    setClientState,
    updateUndoRedoCount,
    validateState,
} from './types';

export class SyncEngineClient<State extends object> extends SyncEngine<State> {
    protected setState: setClientState<State>;
    private updateUndoRedoCount: updateUndoRedoCount;

    // The transactions are the list of all the transactions that have been applied to the snapshot
    protected transactions: Transaction[] = [];
    private transactionsToUndo: Transaction[] = [];
    private transactionsToRedo: Transaction[] = [];
    // The snapshot is the last validated state of the data
    private snapshot: State = {} as State;
    // Points to the last transaction that has been applied to the snapshot
    private _pointer: Pointer = {
        id: '',
        date: '',
    };
    private broadcastTransaction: broadcastTransaction;
    private onTransaction: TransactionCallback<State>;
    // Only accept create, update and delete operations while record is true
    private record: boolean = true;

    public get pointer(): Pointer {
        return this._pointer;
    }

    private set pointer(pointer: Pointer) {
        this._pointer = pointer;
    }

    private batch = {
        changes: [] as Change[],
    };

    constructor({
        getState,
        validateState,
        setState,
        snapshot,
        broadcastTransaction,
        onTransaction,
        updateUndoRedoCount,
    }: {
        getState: getState<State>;
        setState: setClientState<State>;
        validateState?: validateState<State>;
        snapshot: State;
        broadcastTransaction?: broadcastTransaction;
        onTransaction?: TransactionCallback<State>;
        updateUndoRedoCount?: updateUndoRedoCount;
    }) {
        super({ getState, validateState });
        this.setState = setState;
        this.snapshot = snapshot;
        this.broadcastTransaction = broadcastTransaction ?? (() => {});
        this.onTransaction = onTransaction ?? (() => {});
        this.updateUndoRedoCount = updateUndoRedoCount ?? (() => {});

        this.setState = (state, message) => {
            this.record = false;
            setState(state, message);
            this.record = true;
        };
    }

    refresh = (state: State, pointer: Pointer) => {
        this.snapshot = state;
        this.pointer = pointer;
        this.transactions = [];
        this.transactionsToUndo = [];
        this.transactionsToRedo = [];
        this.batch.changes = [];
        this.setState(JSON.parse(JSON.stringify(state)), 'refresh');
    };

    create = (data: { [key: string]: unknown }, apply = true) => {
        if (!this.record) return;

        const change = this.newChange('create', data);

        this.mutate([change], apply);

        return change;
    };

    update = (data: Change['data'], apply = true, previousData: Change['data'] | undefined = undefined) => {
        if (!this.record) return;

        const change = this.newChange('update', data, previousData);

        this.mutate([change], apply);

        return change;
    };

    delete = (keys: string[], apply = true, previousData: Change['data'] | undefined = undefined) => {
        if (!this.record) return;

        const change = this.newChange('delete', keys, previousData);

        this.mutate([change], apply);

        return change;
    };

    save = (addToUndoHistory = true): boolean => {
        if (this.batch.changes.length === 0) return false;

        const transaction = this.newTransaction(this.batch.changes);

        const state = this.getState();
        const { error } = this.validate(state);

        if (!error) {
            this.saveTransaction(transaction, addToUndoHistory);
            this.broadcastTransaction({
                ...transaction,
                status: 'dispatched',
            });
            this.onTransaction(this.getState(), transaction);
            this.updateUndoRedoCount(this.getUndoRedoCount());
        } else {
            this.rebase();
        }

        this.batch.changes = [];

        return error;
    };

    getChanges = (): Change[] => {
        return JSON.parse(JSON.stringify(this.batch.changes));
    };

    getTemporaryState = (): State => {
        return this.getState();
    };

    private mutate = (changes: Change[], apply = true) => {
        const state = this.getState();

        // Do not record while applying changes to state
        this.record = false;
        this.handleChanges(changes, state, apply);
        this.record = true;

        this.batch.changes.push(...changes);
    };

    undo = () => {
        const transaction = this.transactionsToUndo.pop();

        if (!transaction) {
            return;
        }

        const revertedChanges = this.getRevertedChanges(transaction);

        this.mutate(revertedChanges);
        this.save(false);

        this.transactionsToRedo.push(transaction);
        this.updateUndoRedoCount(this.getUndoRedoCount());

        return transaction.changes;
    };

    getUndoCount = (): number => this.transactionsToUndo.length;

    redo = () => {
        const transaction = this.transactionsToRedo.pop();

        if (!transaction) {
            return;
        }

        // Create a new transaction that gets added to the local history
        this.mutate(transaction.changes);
        this.save();

        return transaction.changes;
    };

    getRedoCount = (): number => this.transactionsToRedo.length;

    pushToServer = (): Transaction[] => {
        const createdTransactions = this.transactions.filter(({ status }) => status === 'created');

        return createdTransactions;
    };

    // Pull the transactions from the server and apply them to obtain a new snapshot
    pullFromServer = (transactions: Transaction[]) => {
        if (!transactions.length) return;

        let transactionCount = 0;

        transactions.forEach((transaction) => {
            if (this.isTransactionAlreadyApplied(transaction.id)) {
                return;
            }

            transactionCount++;

            if (transaction.status === 'validatedByServer') {
                this.apply(transaction, this.snapshot);

                this.pointer = {
                    id: transaction.id,
                    date: transaction.date,
                };

                this.setTransactionStatus(transaction.id, 'validatedByServer');
            }

            // If the transaction is invalidated by the server, remove it from local transaction history
            if (transaction.status === 'invalidatedByServer') {
                this.removeTransaction(transaction.id);
            }
        });

        // If no transactions were applied, do not rebase
        if (transactionCount === 0) return;

        // Apply local transactions to the snapshot and update local state (=rebase)
        this.rebase();
    };

    pushToClient = () => {
        const transactions = this.transactions.filter(({ status }) => status === 'created');

        return transactions.map((transaction) => ({ ...transaction, status: 'dispatched' }));
    };

    // Apply transactions received from other client to snapshot and rebase local transactions
    // Note: the snapshot is not updated until the transaction is validated by the server in pullFromServer
    pullFromOtherClient = (transactions: Transaction[]) => {
        if (!transactions.length) {
            return;
        }

        // Remove invalid transactions
        const invalidTransactions = transactions.filter(({ status }) => status === 'invalidatedByClient');
        invalidTransactions.forEach(({ id }) => this.removeTransaction(id));

        // Apply local transactions to the snapshot and update local state (=rebase)
        const transactionsToInsert = transactions.filter(({ status }) => status === 'dispatched');
        this.rebase(transactionsToInsert);
    };

    pull = (transactions: Transaction[]) => {
        const transactionsFromOtherClient = transactions.filter(
            ({ status }) => status === 'dispatched' || status === 'invalidatedByClient',
        );
        const transactionsFromServer = transactions.filter(
            ({ status }) => status === 'validatedByServer' || status === 'invalidatedByServer',
        );

        this.pullFromServer(transactionsFromServer);
        this.pullFromOtherClient(transactionsFromOtherClient);
    };

    private rebase = (transactionsToInsert: Transaction[] = []) => {
        const rebasedState: State = JSON.parse(JSON.stringify(this.snapshot));

        const transactions = this.transactions.filter(({ status }) => status === 'created' || status === 'dispatched');

        [...transactions, ...transactionsToInsert].forEach((transaction) => {
            const { success } = this.apply(transaction, rebasedState);

            // Remove transaction and append to end of the transaction history if it is validly applied
            // Note: only add to undo history if the transaction was created locally. Discard transactions received from other clients in undo history.
            this.removeTransaction(transaction.id);
            if (success) this.saveTransaction(transaction, transaction.status === 'created');
        });

        this.setState(rebasedState, 'rebase');
    };

    private isTransactionAlreadyApplied = (transactionId: TransactionId): boolean => {
        const alreadyApplied = this.transactions.some(
            (transaction) => transaction.id === transactionId && transaction.status === 'validatedByServer',
        );

        return alreadyApplied;
    };

    private saveTransaction = (transaction: Transaction, addToUndoHistory: boolean = true) => {
        this.transactions.push(transaction);
        addToUndoHistory && this.transactionsToUndo.push(transaction);
    };

    private removeTransaction = (transactionId: TransactionId) => {
        this.transactions = this.transactions.filter((transaction) => {
            return transaction.id !== transactionId;
        });

        this.transactionsToUndo = this.transactionsToUndo.filter((transaction) => {
            return transaction.id !== transactionId;
        });

        this.transactionsToRedo = this.transactionsToRedo.filter((transaction) => {
            return transaction.id !== transactionId;
        });

        this.updateUndoRedoCount(this.getUndoRedoCount());
    };

    private broadcastTransactionRemovedToOtherClients = (transactionId: Transaction['id']) => {
        this.broadcastTransaction({
            id: transactionId,
            date: new Date().toISOString(),
            changes: [],
            status: 'invalidatedByClient',
        });
    };

    private getUndoRedoCount = () => {
        const meta = {
            undoCount: this.getUndoCount(),
            redoCount: this.getRedoCount(),
        };

        return meta;
    };

    private setTransactionStatus = (transactionId: Transaction['id'], status: Transaction['status']) => {
        const transaction = this.transactions.find((transaction) => transaction.id === transactionId);

        if (transaction) {
            transaction.status = status;
        }
    };

    getNumberOfTransactionsByStatus = (status: Transaction['status']) => {
        return this.transactions.filter((transaction) => transaction.status === status).length;
    };

    getNumberOfTransactions = () => ({
        created: this.getNumberOfTransactionsByStatus('created'),
        dispatched: this.getNumberOfTransactionsByStatus('dispatched'),
        validatedByServer: this.getNumberOfTransactionsByStatus('validatedByServer'),
        invalidatedByServer: this.getNumberOfTransactionsByStatus('invalidatedByServer'),
        invalidatedByClient: this.getNumberOfTransactionsByStatus('invalidatedByClient'),
    });
}
