export type TransactionId = string;

export type ChangeType = 'create' | 'update' | 'delete';

type Data = string | number | boolean | unknown[] | { [k: string]: unknown };

export type TransactionStatus =
    | 'created'
    | 'dispatched'
    | 'validatedByServer'
    | 'invalidatedByServer'
    | 'invalidatedByClient';

export type Change = {
    type: ChangeType;
    data: Data;
    previousData?: Data;
};

export type Transaction = {
    id: TransactionId;
    date: string;
    changes: Change[];
    status: TransactionStatus;
    errors?: string[];
};
