import { Pointer, Transaction } from './index';

export type getState<State> = () => State;
export type setClientState<State> = (state: State, message: string) => void;
export type setServerState<State> = (state: State, transactions: Transaction[]) => void;

export type ValidationResult = {
    errors: string[];
    success: boolean;
    error: boolean;
};

export type validateState<State> = (state: State) => ValidationResult;

export type getTransactions = (pointer?: Pointer) => Promise<{
    transactions: Transaction[];
    filtered?: boolean;
}>;

export type TransactionCallback<State> = (state: State, transaction: Transaction) => void;
export type broadcastTransaction = (transaction: Transaction) => void;

export type UndoRedoCount = {
    undoCount: number;
    redoCount: number;
};

export type updateUndoRedoCount = (undoRedoCount: UndoRedoCount) => void;
