import { unset } from './unset';

import { expect } from '@jest/globals';

test('remove key from object', async () => {
    const obj = {
        a: 1,
        b: 2,
        c: 3,
    };

    const result = unset(obj, 'b');

    expect(result).toEqual({
        a: 1,
        c: 3,
    });
});

test('remove key from nested object', async () => {
    const obj = {
        a: 1,
        b: {
            c: 2,
            d: 3,
        },
    };

    const result = unset(obj, 'b.c');

    expect(result).toEqual({
        a: 1,
        b: {
            d: 3,
        },
    });
});

test('remove key from array', async () => {
    const obj = {
        a: 1,
        b: [1, 2, 3],
    };

    const result = unset(obj, 'b.1');

    expect(result).toEqual({
        a: 1,
        b: [1, 3],
    });
});

test('remove key from nested array', async () => {
    const obj = {
        a: 1,
        b: {
            c: [1, 2, 3],
            d: 4,
        },
    };

    const result = unset(obj, 'b.c.1');

    expect(result).toEqual({
        a: 1,
        b: {
            c: [1, 3],
            d: 4,
        },
    });
});

test('remove key from array of objects', async () => {
    const obj = {
        a: 1,
        b: [
            {
                c: 1,
                d: 2,
            },
            {
                c: 3,
                d: 4,
            },
        ],
    };

    const result = unset(obj, 'b.1.d');

    expect(result).toEqual({
        a: 1,
        b: [
            {
                c: 1,
                d: 2,
            },
            {
                c: 3,
            },
        ],
    });
});

test('remove key from array of nested objects', async () => {
    const obj = {
        a: 1,
        b: [
            {
                c: 1,
                d: {
                    e: 2,
                    f: 3,
                },
            },
            {
                c: 4,
                d: {
                    e: 5,
                    f: 6,
                },
            },
        ],
    };

    const result = unset(obj, 'b.1.d.e');

    expect(result).toEqual({
        a: 1,
        b: [
            {
                c: 1,
                d: {
                    e: 2,
                    f: 3,
                },
            },
            {
                c: 4,
                d: {
                    f: 6,
                },
            },
        ],
    });
});

test('remove key from array of nested arrays', async () => {
    const obj = {
        a: 1,
        b: [
            {
                c: 1,
                d: [1, 2, 3],
            },
            {
                c: 4,
                d: [5, 6, 7],
            },
        ],
    };

    const result = unset(obj, 'b.1.d.1');

    expect(result).toEqual({
        a: 1,
        b: [
            {
                c: 1,
                d: [1, 2, 3],
            },
            {
                c: 4,
                d: [5, 7],
            },
        ],
    });
});

test('remove key from array of nested objects and arrays', async () => {
    const obj = {
        a: 1,
        b: [
            {
                c: 1,
                d: {
                    e: [1, 2, 3],
                    f: 4,
                },
            },
            {
                c: 5,
                d: {
                    e: [6, 7, 8],
                    f: 9,
                },
            },
        ],
    };

    const result = unset(obj, 'b.1.d.e.1');

    expect(result).toEqual({
        a: 1,
        b: [
            {
                c: 1,
                d: {
                    e: [1, 2, 3],
                    f: 4,
                },
            },
            {
                c: 5,
                d: {
                    e: [6, 8],
                    f: 9,
                },
            },
        ],
    });
});

test('remove element from array with single element', async () => {
    const obj = {
        a: 1,
        b: [1],
    };

    const result = unset(obj, 'b.0');

    expect(result).toEqual({
        a: 1,
        b: [],
    });
});

test('remove array element by id', async () => {
    const obj = {
        a: [
            {
                id: 'firstId',
                value: 'b',
            },
            {
                id: 'secondId',
                value: 'c',
            },
        ],
    };

    const result = unset(obj, 'a.secondId');

    expect(result).toEqual({
        a: [
            {
                id: 'firstId',
                value: 'b',
            },
        ],
    });
});

test('remove object from array', async () => {
    const obj = {
        ports: [
            {
                id: 'firstId',
            },
            {
                id: 'secondId',
            },
        ],
    };

    const result = unset(obj, 'ports.secondId');

    expect(result).toEqual({
        ports: [
            {
                id: 'firstId',
            },
        ],
    });
});
