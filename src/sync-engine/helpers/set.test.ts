import { set } from './set';

import { expect } from '@jest/globals';

test('set value in nested object', () => {
    const target = {
        a: {
            b: {
                c: 'd',
            },
        },
    };

    expect(set(target, 'a.b.c', 'e')).toEqual({
        a: {
            b: {
                c: 'e',
            },
        },
    });
});

test('set value in nested array', () => {
    const target = {
        a: {
            b: ['c', 'd'],
        },
    };

    expect(set(target, 'a.b.1', 'e')).toEqual({
        a: {
            b: ['c', 'e'],
        },
    });
});

test('set value in array by id', () => {
    const target = {
        a: [
            {
                id: 'firstId',
                value: 'b',
            },
            {
                id: 'secondId',
                value: 'c',
            },
        ],
    };

    expect(
        set(target, 'a.firstId', {
            id: 'firstId',
            value: 'e',
        }),
    ).toEqual({
        a: [
            {
                id: 'firstId',
                value: 'e',
            },
            {
                id: 'secondId',
                value: 'c',
            },
        ],
    });
});

test('set nested value at non-existing array index', () => {
    const target = {
        a: [
            { foo: 1, bar: 2 },
            { foo: 3, bar: 4 },
        ],
    };

    set(target, 'a.2.foo', 5);
    set(target, 'a.2.bar', 6);

    expect(target).toEqual({
        a: [
            { foo: 1, bar: 2 },
            { foo: 3, bar: 4 },
            { foo: 5, bar: 6 },
        ],
    });
});

test('set nested array at non-existing array index', () => {
    const target = {
        a: [
            [1, 2, 3],
            [4, 5, 6],
        ],
    };

    set(target, 'a.2.0', 7);
    set(target, 'a.2.1', 8);
    set(target, 'a.2.2', 9);

    expect(target).toEqual({
        a: [
            [1, 2, 3],
            [4, 5, 6],
            [7, 8, 9],
        ],
    });
});

test('set nested value at non-existing object key', () => {
    const target = {
        a: {
            alice: { foo: 1, bar: 2 },
            bob: { foo: 3, bar: 4 },
        },
    };

    set(target, 'a.charles.foo', 5);
    set(target, 'a.charles.bar', 6);

    expect(target).toEqual({
        a: {
            alice: { foo: 1, bar: 2 },
            bob: { foo: 3, bar: 4 },
            charles: { foo: 5, bar: 6 },
        },
    });
});

test('setting on array with a non-existing ID throws an error', () => {
    const target = {
        a: [
            { id: 'foo', val: 1 },
            { id: 'bar', val: 2 },
        ],
    };

    const setInvalidValue = () => set(target, 'a.baz.val', 3);

    expect(setInvalidValue).toThrow('Object with ID baz not found');
});

test('can set over null object values', () => {
    const target = {
        a: {
            b: null,
        },
    };

    set(target, 'a.b.c', 1);

    expect(target).toEqual({
        a: {
            b: {
                c: 1,
            },
        },
    });
});

test('can set over null array values', () => {
    const target = {
        a: [{ val: 'a' }, null, { val: 'c' }],
    };

    set(target, 'a.1.val', 'b');

    expect(target).toEqual({
        a: [{ val: 'a' }, { val: 'b' }, { val: 'c' }],
    });
});

test('set first value in array', () => {
    const target = {
        a: null,
    };

    set(target, 'a.0', 10000);

    expect(target).toEqual({
        a: [10000],
    });
});

test('set value false > true in object', () => {
    const target = {
        a: {
            b: {
                c: false,
            },
        },
    };

    set(target, 'a.b', { c: true });

    expect(target).toEqual({
        a: {
            b: {
                c: true,
            },
        },
    });
});
