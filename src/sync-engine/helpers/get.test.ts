import { get } from './get';

import { expect } from '@jest/globals';

test('get value from nested object', () => {
    const target = {
        a: {
            b: {
                c: 'd',
            },
        },
    };

    expect(get(target, 'a.b.c')).toBe('d');
});

test('get value from nested array', () => {
    const target = {
        a: {
            b: ['c', 'd'],
        },
    };

    expect(get(target, 'a.b.1')).toBe('d');
});

test('get value from array by id', () => {
    const target = {
        a: [
            {
                id: 'firstId',
                value: 'b',
            },
            {
                id: 'secondId',
                value: 'c',
            },
        ],
    };

    expect(get(target, 'a.firstId')).toEqual({
        id: 'firstId',
        value: 'b',
    });
});

test('get value from object at key which is undefined', () => {
    const target = {
        a: undefined,
    };

    expect(get(target, 'a')).toBe(undefined);
    expect(get(target, 'b')).toBe(undefined);
    expect(get(target, 'b.c')).toBe(undefined);
});
