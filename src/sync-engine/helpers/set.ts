import { isArray, isObject } from 'radash';

const isDigit = (string: string) => string.match(/^\d+$/);

export const set = <T extends object>(target: T, path: string, value: any): T => {
    const pathArray = path.split('.');

    let returnValue: any = target;

    pathArray.forEach((key, index, pathArray) => {
        if (index === pathArray.length - 1) {
            if (Array.isArray(returnValue)) {
                const _returnValue: any[] = returnValue;

                if (isDigit(key)) {
                    if (value === undefined) {
                        _returnValue.splice(parseInt(key), 1);
                    } else {
                        setValue(_returnValue, parseInt(key), value);
                    }
                } else {
                    const index = _returnValue.findIndex((item) => item?.id === key);

                    if (index === -1 && value !== undefined) {
                        _returnValue.push({
                            id: key,
                            ...value,
                        });
                    } else if (value === undefined) {
                        _returnValue.splice(index, 1);
                    } else {
                        setValue(_returnValue, index, value);
                    }
                }
            } else {
                if (value === undefined) {
                    delete returnValue[key];
                } else {
                    setValue(returnValue, key, value);
                }
            }
        } else {
            if (Array.isArray(returnValue)) {
                const _returnValue: any[] = returnValue;

                if (isDigit(key)) {
                    const keyAsIndex = parseInt(key);

                    if (_returnValue[keyAsIndex] === undefined || _returnValue[keyAsIndex] === null) {
                        if (isDigit(pathArray[index + 1])) {
                            _returnValue[keyAsIndex] = [];
                        } else {
                            _returnValue[keyAsIndex] = {};
                        }
                    }

                    returnValue = _returnValue[keyAsIndex];
                } else {
                    returnValue = _returnValue.find((item) => item?.id === key);

                    if (!returnValue) {
                        throw new Error(`Object with ID ${key} not found`);
                    }
                }
            } else {
                if (returnValue[key] === undefined || returnValue[key] === null) {
                    if (isDigit(pathArray[index + 1])) {
                        returnValue[key] = [];
                    } else {
                        returnValue[key] = {};
                    }
                }

                returnValue = returnValue[key];
            }
        }
    });

    return target;
};

const setValue = (target: any, key: any, value: any) => {
    if (isObject(value)) {
        if (!isObject(target[key])) {
            target[key] = {};
        } else {
            Object.keys(target[key]).forEach((subKey) => {
                if (!(subKey in value)) {
                    delete target[key][subKey];
                }
            });
        }

        Object.entries(value).forEach(([subKey, subValue]) => {
            setValue(target[key], subKey, subValue);
        });
    } else if (isArray(value)) {
        if (!isArray(target[key]) || value.length !== target[key]?.length) {
            target[key] = [];
        }

        Object.entries(value).forEach(([subKey, subValue]) => {
            setValue(target[key], subKey, subValue);
        });
    } else {
        try {
            target[key] = value;
        } catch (error) {
            throw new Error(
                `Cannot set value ${JSON.stringify(value)} to key ${key} in target ${JSON.stringify(target)}: ${error}`,
            );
        }
    }

    return target;
};
