export const get = <T extends object>(target: T, path: string): any => {
    const pathArray = path.split('.');

    let returnValue: any = target;

    pathArray.forEach((key) => {
        if (Array.isArray(returnValue)) {
            const _returnValue: any[] = returnValue;

            if (key.match(/^\d+$/)) {
                returnValue = _returnValue?.[parseInt(key)];
            } else {
                returnValue = _returnValue.find((item) => item?.id === key);
            }
        } else {
            returnValue = returnValue?.[key];
        }
    });

    return returnValue;
};
