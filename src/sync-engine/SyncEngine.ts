import { uid } from 'radash';

import {
    Change,
    Transaction,
    getState,
    setServerState,
    setClientState,
    validateState,
    ValidationResult,
} from './types';
import { get, set, unset } from './helpers';

export abstract class SyncEngine<State extends object> {
    // Callback when local state is updated - Hello worl
    protected abstract setState: setClientState<State> | setServerState<State>;
    protected getState: getState<State>;
    private validateState: validateState<State>;
    private dateOfLastTransaction: string | undefined = undefined;

    constructor({ getState, validateState }: { getState: getState<State>; validateState?: validateState<State> }) {
        this.getState = getState;
        this.validateState = validateState ?? (() => ({ errors: [], success: true, error: false }));
    }

    private validateChange = (change: Change, target: State): ValidationResult => {
        const { data, type } = change;

        if (!data) {
            throw new Error('data is required to apply transaction to local state');
        }

        const returnValue: ValidationResult = {
            errors: [],
            success: false,
            error: false,
        };

        if (Object.keys(data).length === 0) {
            returnValue.errors.push('Changes without data are not allowed');
        }

        Object.entries(data).forEach(([key, value]) => {
            const currentValue = get(target, type === 'delete' ? value : key);

            if (type === 'create') {
                if (currentValue) {
                    returnValue.errors.push(`Cannot create key ${key} that already exists`);
                }
            }

            if (type === 'update') {
                if (currentValue === undefined) {
                    returnValue.errors.push(`Cannot update key ${key} that does not exist`);
                }
            }

            if (type === 'delete') {
                if (currentValue === undefined) {
                    returnValue.errors.push(`Cannot delete key ${value} that does not exist`);
                }
            }
        });

        return {
            ...returnValue,
            success: returnValue.errors.length === 0,
            error: returnValue.errors.length > 0,
        };
    };

    protected handleChanges = (changes: Change[], target: State, apply = true) => {
        changes.forEach((change) => {
            target = this.handleChange(change, target, apply);
        });
        return target;
    };

    private handleChange = (change: Change, target: State, apply = true) => {
        const { data, type } = change;

        if (!data) throw new Error('data is required to apply transaction to given target');

        if (type === 'create' && apply) {
            Object.entries(data).forEach(([key, value]) => {
                target = set(target, key, value);
            });
        } else if (type === 'update') {
            const previousData: { [key: string]: unknown } = {
                // If previousData is provided, use it, otherwise use the current value
                ...((change.previousData as { [key: string]: unknown }) ?? {}),
            };

            Object.entries(data).forEach(([key, value]) => {
                previousData[key] = key in previousData ? previousData[key] : get(target, key);

                if (apply) {
                    target = set(target, key, value);
                }
            });

            change.previousData = previousData;
        } else if (type === 'delete') {
            const keysToDelete = Object.values(data as string[]);

            const previousData: { [key: string]: unknown } = {
                // If previousData is provided, use it, otherwise use the current value
                ...((change.previousData as { [key: string]: unknown }) ?? {}),
            };

            keysToDelete.forEach((key) => {
                previousData[key] = key in previousData ? previousData[key] : get(target, key);

                if (apply) {
                    target = unset(target, key);
                }
            });

            change.previousData = previousData;
        }

        return target;
    };

    private getRevertedChange = (change: Change): Change => {
        const { data, type, previousData } = change;

        if (!data) throw new Error('data is required to apply transaction to given target');

        const revertedChange: Change = {
            type,
            data,
        };

        if (type === 'create') {
            revertedChange.type = 'delete';
            revertedChange.data = Object.keys(data);
        }

        if (type === 'update' && previousData) {
            revertedChange.type = 'update';
            revertedChange.data = previousData;
        }

        if (type === 'delete' && previousData) {
            revertedChange.type = 'create';
            revertedChange.data = previousData;
        }

        return revertedChange;
    };

    protected validate = (state: State) => {
        return this.validateState(state);
    };

    protected apply = (transaction: Transaction, target: State) => {
        const returnValue: ValidationResult = {
            errors: [],
            success: false,
            error: false,
        };

        const { changes } = transaction;

        let localTarget: State = JSON.parse(JSON.stringify(target));

        changes.forEach((change) => {
            // Break the loop if there is already an error
            if (returnValue.error) return;

            const { success, errors } = this.validateChange(change, localTarget);

            if (success) {
                localTarget = this.handleChange(change, localTarget);
            } else {
                returnValue.errors.push(...errors);
                returnValue.error = true;
            }
        });

        const validateStateResult = this.validateState(localTarget);

        if (validateStateResult.error) {
            returnValue.errors.push(...validateStateResult.errors);
            returnValue.error = true;
        }

        if (!returnValue.error) {
            returnValue.success = true;

            // Effectively apply changes to target
            changes.forEach((change) => {
                target = this.handleChange(change, target);
            });
        }

        return returnValue;
    };

    // Note: do not modify the original "changes" array
    private getChangesInReverseOrder = (changes: Change[]): Change[] => [...changes].reverse();

    protected getRevertedChanges = (transaction: Transaction): Change[] => {
        const { changes } = transaction;

        const changesInReversedOrder = this.getChangesInReverseOrder(changes);

        return changesInReversedOrder.map(this.getRevertedChange);
    };

    protected revert = (transaction: Transaction, target: State) => {
        const returnValue: ValidationResult = {
            errors: [],
            success: false,
            error: false,
        };

        const { changes } = transaction;

        let localTarget = JSON.parse(JSON.stringify(target));

        const changesInReversedOrder = this.getChangesInReverseOrder(changes);

        changesInReversedOrder.forEach((change) => {
            // Break the loop if there is already an error
            if (returnValue.error) return;

            const revertedChange = this.getRevertedChange(change);

            const { success, errors } = this.validateChange(revertedChange, localTarget);

            if (success) {
                localTarget = this.handleChange(revertedChange, localTarget);
            } else {
                returnValue.errors.push(...errors);
                returnValue.error = true;
            }
        });

        if (!returnValue.error) {
            returnValue.success = true;

            // Effectively apply changes to target
            changesInReversedOrder.forEach((change) => {
                this.handleChange(this.getRevertedChange(change), target);
            });
        }

        return returnValue;
    };

    protected newTransaction = (changes: Change[]): Transaction => {
        if (!changes.length) {
            throw new Error('Cannot create transaction with no changes');
        }

        let date = new Date().toISOString();

        if (date === this.dateOfLastTransaction) {
            // Throw warning because it is not normal that multiple transactions get created in the same millisecond
            // save is likely to be called multiple times in the same millisecond
            console.warn('Two transactions were created with the same date');

            date = new Date(new Date().getTime() + 1).toISOString();
        }

        return {
            id: uid(24),
            date,
            changes,
            status: 'created',
        };
    };

    protected newChange = (
        type: Change['type'],
        data: Change['data'],
        previousData: Change['data'] | undefined = undefined,
    ): Change => {
        if (Object.keys(data).length === 0) {
            throw new Error('Cannot create change with no data');
        }

        return {
            type,
            data,
            previousData,
        };
    };
}
