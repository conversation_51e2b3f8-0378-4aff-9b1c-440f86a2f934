import {
    MeasurementSystem,
    PortControlMethods,
    PowerFlowDirection,
    Voltage,
    WireSize,
    UnitConverter,
    LengthConverter,
    currentConverter,
    voltageConverter,
    TemperatureConverter,
    TVoltageUnit,
    Cable,
    powerConverter,
} from 'models';

import { counting, isNumber, sort } from 'radash';

import { round } from 'helpers/round';

type MeasurementWithoutUnit = {
    value?: number;
    min?: number;
    nom?: number;
    max?: number;
};

type Measurement<TUnit extends string> = MeasurementWithoutUnit & {
    unit: TUnit;
};

const FormatHelpers = {
    isEmpty: (measurement: MeasurementWithoutUnit) => {
        let total = 0;

        ['min', 'nom', 'max', 'value'].forEach((field) => {
            if (field in measurement && measurement[field as keyof typeof measurement]) {
                total += 1;
            }
        });

        return !!total;
    },

    formatMeasurement: <TUnit extends string, TBaseUnit extends TUnit>(
        toFormat: { value: number | null; unit: TUnit } | null,
        converter: UnitConverter<TUnit, TBaseUnit>,
    ) => {
        const { value, unit: from } = toFormat || {};

        if (isNumber(value) && from) {
            const best = converter.toBest({ value, from });

            return `${round(best.value, 1).toLocaleString()} ${best.unit}`;
        }

        return '';
    },

    formatValue: <TUnit extends string, TBaseUnit extends TUnit>(
        value: number | null | undefined,
        converter: UnitConverter<TUnit, TBaseUnit>,
        unit = converter.baseUnit,
    ) => {
        if (!isNumber(value)) {
            return '';
        }

        return FormatHelpers.formatMeasurement({ value, unit }, converter);
    },

    transformValue: <TUnit extends string, TBaseUnit extends TUnit>(
        value: number | null | undefined,
        converter: UnitConverter<TUnit, TBaseUnit>,
        from: TUnit,
        to: TUnit,
    ) => {
        if (!isNumber(value)) {
            return undefined;
        }

        if (value === undefined) return undefined;

        const convertedValue = converter.convert({
            value: +value,
            from,
            to,
        });

        return Number(convertedValue.value.toFixed(2));
    },

    transformToBestUnit: <TUnit extends string, TBaseUnit extends TUnit>(
        values: Measurement<TUnit>,
        converter: UnitConverter<TUnit, TBaseUnit>,
    ): Measurement<TUnit> => {
        const { min, nom, max, unit: baseUnit } = values;

        const valueToBaseUpon = nom || min || max;

        if (valueToBaseUpon === undefined) {
            return values;
        }

        const best = converter.toBest({
            value: +valueToBaseUpon,
            from: baseUnit,
        });

        if (!best) {
            return values;
        }

        const bestUnit = best.unit;

        const format = (value?: number) => {
            if (value === undefined) return undefined;

            const convertedValue = converter.convert({
                value: +value,
                from: baseUnit,
                to: bestUnit,
            });

            return Number(convertedValue.value.toFixed(2));
        };

        return {
            unit: bestUnit,
            min: format(min),
            nom: format(nom),
            max: format(max),
        };
    },

    formatMinNomMax: <TUnit extends string, TBaseUnit extends TUnit>(
        values: any = {},
        converter: UnitConverter<TUnit, TBaseUnit>,
    ): string => {
        const { min, nom, max, unit } = values;
        const hasMin = isNumber(min);
        const hasNom = isNumber(nom);
        const hasMax = isNumber(max);

        if (!unit) return '';

        const { nom: nomConverted, unit: bestNomUnit } = FormatHelpers.transformToBestUnit({ nom, unit }, converter);

        if (hasMin && hasNom && hasMax) {
            const {
                min: minConverted,
                max: maxConverted,
                unit: bestMinMaxUnit,
            } = FormatHelpers.transformToBestUnit({ min, max, unit }, converter);

            return `${nomConverted} ${bestNomUnit} (${minConverted}-${maxConverted} ${bestMinMaxUnit})`;
        }

        if (hasMin && hasNom) {
            const { min: minConverted, unit: bestMinUnit } = FormatHelpers.transformToBestUnit(
                { min, unit },
                converter,
            );

            return `${nomConverted} ${bestNomUnit} (${minConverted} ${bestMinUnit} min)`;
        }

        if (hasMin && hasMax) {
            const {
                min: minConverted,
                max: maxConverted,
                unit: bestMinMaxUnit,
            } = FormatHelpers.transformToBestUnit({ min, max, unit }, converter);

            return `${minConverted}-${maxConverted} ${bestMinMaxUnit}`;
        }

        if (hasNom && hasMax) {
            const { max: maxConverted, unit: bestMaxUnit } = FormatHelpers.transformToBestUnit(
                { max, unit },
                converter,
            );

            return `${nomConverted} ${bestNomUnit} (${maxConverted} ${bestMaxUnit} max)`;
        }

        if (hasMin) {
            const { min: minConverted, unit: bestMinUnit } = FormatHelpers.transformToBestUnit(
                { min, unit },
                converter,
            );

            return `${minConverted} ${bestMinUnit} min`;
        }

        if (hasNom) {
            return `${nomConverted} ${bestNomUnit}`;
        }

        if (hasMax) {
            const { max: maxConverted, unit: bestMaxUnit } = FormatHelpers.transformToBestUnit(
                { max, unit },
                converter,
            );

            return `${maxConverted} ${bestMaxUnit} max`;
        }

        return '';
    },

    formatCurrent: (values: any = {}) => {
        return 'value' in values
            ? FormatHelpers.formatValue(values.value, currentConverter, 'A')
            : FormatHelpers.formatMinNomMax(values, currentConverter);
    },

    formatVoltage: (values: any = {}) => {
        return 'value' in values
            ? FormatHelpers.formatValue(values.value, voltageConverter, 'V')
            : FormatHelpers.formatMinNomMax(values, voltageConverter);
    },

    formatPower: (values: any = {}) => {
        return 'value' in values
            ? FormatHelpers.formatValue(values.value, powerConverter, 'W')
            : FormatHelpers.formatMinNomMax(values, powerConverter);
    },

    formatLength: (value: number | null, measurementSystem: MeasurementSystem = MeasurementSystem.METRIC) => {
        return FormatHelpers.formatValue(value, new LengthConverter(measurementSystem), 'm');
    },

    formatOption: (options: any[], value: any) => {
        if (!value) {
            return '';
        }

        const option = options.find((option) => option.value === value);

        return option?.label || '';
    },

    formatPowerFlowDirection: (powerFlowDirection: string) => {
        return FormatHelpers.formatOption(PowerFlowDirection.options, powerFlowDirection);
    },

    formatWireSize: (wireSize: WireSize | null, measurementSystem: MeasurementSystem | null = null) => {
        const option = WireSize.options.find((option) => option.value === wireSize);

        if (!option) {
            return '';
        }

        if (measurementSystem === MeasurementSystem.METRIC) {
            // @ts-ignore
            return option.mm2 + ' mm²';
        }

        if (measurementSystem === MeasurementSystem.IMPERIAL) {
            // @ts-ignore
            return option.awg + ' AWG';
        }

        return option.label;
    },

    formatOperatingVoltage: (operatingVoltage: Cable['electrical']['operatingVoltage']): string | null => {
        if (!operatingVoltage?.betweenLines?.value) {
            return null;
        }

        const betweenLinesVoltage = FormatHelpers.formatValue(
            operatingVoltage.betweenLines.value,
            voltageConverter,
            'V',
        );
        const betweenLinesVoltageUnit = betweenLinesVoltage.split(' ')[1] as TVoltageUnit;
        const toEarthVoltage = betweenLinesVoltageUnit
            ? FormatHelpers.transformValue(
                  operatingVoltage.toEarth.value,
                  voltageConverter,
                  'V',
                  betweenLinesVoltageUnit,
              )
            : null;

        const formattedOperatingVoltage =
            toEarthVoltage && betweenLinesVoltage ? `${toEarthVoltage} / ${betweenLinesVoltage}` : null;
        return formattedOperatingVoltage;
    },

    formatControlMethod: (controlMethod: string | null) => {
        return FormatHelpers.formatOption(PortControlMethods.options, controlMethod);
    },

    formatIsolation: (isolated?: boolean) => {
        return isolated ? 'Isolated' : 'Not Isolated';
    },

    formatDimensions: <MSystem extends MeasurementSystem>(values: any, converter: LengthConverter<MSystem>) => {
        const { width, length, height, value, unit } = values;

        if (!unit) return '';

        if (isNumber(value)) {
            return FormatHelpers.formatValue(value, converter, unit);
        }

        const minValue = [width, length, height].reduce((min, next) => Math.min(next, min));

        const smallestUnit = converter.toBest({ value: minValue, from: unit }).unit;

        const formatted = [];

        const formattedWidth = FormatHelpers.transformValue(width, converter, unit, smallestUnit);
        const formattedLength = FormatHelpers.transformValue(length, converter, unit, smallestUnit);
        const formattedHeight = FormatHelpers.transformValue(height, converter, unit, smallestUnit);

        if (formattedLength) {
            formatted.push(`L: ${formattedLength} ${smallestUnit}`);
        }

        if (formattedWidth) {
            formatted.push(`W: ${formattedWidth} ${smallestUnit}`);
        }

        if (formattedHeight) {
            formatted.push(`H: ${formattedHeight} ${smallestUnit}`);
        }

        return formatted.join(' × ');
    },

    getUniqueVoltages: (dirtyVoltages: Voltage[]) => {
        const voltages = dirtyVoltages.filter(
            (voltage) => voltage && voltage.unit && (voltage.min || voltage.nom || voltage.max),
        );

        const delimiter = '|';
        const stringify = (voltage: Voltage) =>
            [voltage.min ?? 0, voltage.nom ?? 0, voltage.max ?? 0, voltage.unit].join(delimiter);
        const parse = (voltageString: string) => {
            const [min, nom, max, unit] = voltageString.split(delimiter);

            return {
                min: isNumber(+min) ? +min : null,
                nom: isNumber(+nom) ? +nom : null,
                max: isNumber(+max) ? +max : null,
                unit: unit ?? 'V',
            } as Voltage;
        };

        const voltagesCount = counting(voltages, stringify);
        const voltagesCountAsArray = Object.entries(voltagesCount).map(([voltage, count]) => ({ voltage, count }));
        const voltagesCountAsSortedArray = sort(voltagesCountAsArray, ({ count }) => count, true);
        const uniqueVoltages = voltagesCountAsSortedArray.map(({ voltage }) => parse(voltage));

        return uniqueVoltages;
    },

    formatTemperatureRange: <MSystem extends MeasurementSystem>(
        { min, max, unit }: { min?: number; max?: number; unit?: TemperatureConverter<MSystem>['units'][number] },
        converter: TemperatureConverter<MSystem>,
    ) => {
        const {
            min: minConverted,
            max: maxConverted,
            unit: bestMinMaxUnit,
        } = FormatHelpers.transformToBestUnit({ min, max, unit: unit ?? converter.baseUnit }, converter);

        const hasMin = isNumber(min);
        const hasMax = isNumber(max);

        if (!hasMin && !hasMax) {
            return '';
        }

        if (hasMin && hasMax) {
            return `${minConverted} to ${maxConverted} °${bestMinMaxUnit}`;
        }

        if (hasMin) {
            return `${minConverted} °${bestMinMaxUnit} min`;
        }

        if (hasMax) {
            return `${maxConverted} °${bestMinMaxUnit} max`;
        }
    },
};

export { FormatHelpers };
