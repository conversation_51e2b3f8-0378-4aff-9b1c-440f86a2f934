import { MeasurementSystem } from 'models';

import { snapshot } from 'valtio';

import { state } from 'state/default-measurement-system';

const MeasurementSystemService = {
    toggle: () => {
        state.measurementSystem = {
            [MeasurementSystem.METRIC]: MeasurementSystem.IMPERIAL,
            [MeasurementSystem.IMPERIAL]: MeasurementSystem.METRIC,
        }[state.measurementSystem || MeasurementSystem.IMPERIAL];
    },

    set: (measurementSystem: MeasurementSystem) => {
        state.measurementSystem = measurementSystem;
    },

    get: () => {
        const { measurementSystem } = snapshot(state);

        return measurementSystem || MeasurementSystemService.fallback();
    },

    fallback: () => MeasurementSystem.IMPERIAL,
};

export { MeasurementSystemService };
