import { mutate } from 'swr';

import { ApiService } from './ApiService';
import { IncomingMessage } from 'http';

import { CreateProjectMetadata, FeatureLimit, Project } from 'models';

import { publicConfig } from '@public-config';

import { InternalTrackingService } from 'services/InternalTrackingService';
import { UserService } from 'services/UserService';

import { PermissionServiceSubscription } from 'services/PermissionServiceSubscription';

import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { ServiceHelpers } from 'helpers/ServiceHelpers';

import { state as currentProjectState } from 'state/current-project';
import { state as recentProjectsState } from 'state/recent-projects';
import { PAGE_SIZE } from 'hooks/use-infinite-load';

const ProjectService = {
    navigate: ServiceHelpers.createNavigator(ProjectHelpers.urls),

    generateName: ({ name, startFromExistingProject }: { name?: string; startFromExistingProject?: string }) => {
        return name ? (startFromExistingProject ? `Copy of ${name}` : name) : `[untitled project]`;
    },

    list: async ({ depth = 1, limit = 16, projectIds }: { depth: number; limit: number; projectIds?: string[] }) => {
        const params = projectIds?.map((id) => `where[id][in]=${id}`).join('&');

        return await ApiService.get(
            `${publicConfig.urls.api}/projects?depth=${depth}&limit=${limit}${params ? `&${params}` : ''}`,
        );
    },

    getSearchUrl: (page: number = 1, limit: number = PAGE_SIZE) => {
        return `${publicConfig.urls.api}/projects?depth=0&limit=${limit}&page=${page}`;
    },

    create: async (
        payload: CreateProjectMetadata,
    ): Promise<
        | {
              doc: Project;
              errors: undefined;
          }
        | {
              doc: undefined;
              errors: {
                  message: string;
              }[];
          }
    > => {
        InternalTrackingService.track('project.create', payload);

        const result = await ApiService.post(`${publicConfig.urls.api}/projects`, payload);

        await PermissionServiceSubscription.mutateFeatureLimit(FeatureLimit.PROJECTS);
        await UserService.refreshProgress();

        return result;
    },

    duplicate: async (
        projectId: Project['id'],
        name: Project['name'],
        isReferenceDesign: Project['isReferenceDesign'] | undefined,
    ) => {
        InternalTrackingService.track('project.create', {
            fromProject: projectId,
            fromProjectIsReferenceDesign: isReferenceDesign,
            name,
        });

        const result = await ApiService.post(`${publicConfig.urls.api}/projects/${projectId}/duplicate?name=${name}`);

        await PermissionServiceSubscription.mutateFeatureLimit(FeatureLimit.PROJECTS);

        return result;
    },

    get: async (projectId: string, options: { depth?: number; req?: IncomingMessage } = {}) => {
        const { depth = 1, req } = options;

        return ApiService.get(`${publicConfig.urls.api}/projects/${projectId}?depth=${depth}`, { req });
    },

    getByDesign: async (designId: string) => {
        return ApiService.get(`${publicConfig.urls.api}/projects/get-by-design?designId=${designId}`);
    },

    update: async (projectId: string, payload: any) => {
        if (currentProjectState.project && currentProjectState.project.id === projectId) {
            InternalTrackingService.track('project.update', payload);

            Object.keys(payload).forEach((key) => {
                // @ts-ignore
                currentProjectState.project[key as any] = payload[key];
            });
        }

        const result = await ApiService.patch(`${publicConfig.urls.api}/projects/${projectId}`, payload);

        await mutate(`/api/projects/${projectId}?depth=0`, result.doc, { revalidate: false });

        return result;
    },

    updateStatus: async (projectId: string, payload: any) => {
        InternalTrackingService.track('project.updateStatus', payload);

        return await ApiService.patch(`${publicConfig.urls.api}/projects/${projectId}/status/edit`, payload);
    },

    delete: async (projectId: string) => {
        InternalTrackingService.track('project.delete');

        const recentProjects = recentProjectsState.projects;

        if (recentProjects.some((project) => project.id === projectId)) {
            recentProjectsState.projects = recentProjects.filter((project) => project.id !== projectId);
        }

        const result = await ApiService.post(`${publicConfig.urls.api}/projects/${projectId}/delete`);

        await PermissionServiceSubscription.mutateFeatureLimit(FeatureLimit.PROJECTS);

        return result;
    },

    restore: async (projectId: string) => {
        InternalTrackingService.track('project.restore');

        const result = await ApiService.post(`${publicConfig.urls.api}/projects/${projectId}/restore`);

        await PermissionServiceSubscription.mutateFeatureLimit(FeatureLimit.PROJECTS);

        return result;
    },

    inviteCollaborator: async (
        project: Project['id'],
        collaborator: {
            email: string;
            permissions: string[];
        },
        redirect: string,
    ) => {
        InternalTrackingService.track('project.inviteCollaborator', {
            collaborator: collaborator.email,
            permissions: collaborator.permissions,
        });

        const result = await ApiService.post(`${publicConfig.urls.api}/projects/${project}/collaborators/invite`, {
            project,
            collaborator,
            redirect,
            origin: `${location.origin}/token`,
        });

        if (currentProjectState.project?.collaborators) {
            currentProjectState.project.collaborators = result.collaborators;
        }
    },

    editCollaborator: async (project: Project['id'], collaborator: string, permissions: string[]) => {
        InternalTrackingService.track('project.editCollaborator', {
            collaborator,
            permissions,
        });

        const result = await ApiService.post(`${publicConfig.urls.api}/projects/${project}/collaborators/edit`, {
            collaborator,
            permissions,
        });

        if (currentProjectState.project?.collaborators) {
            currentProjectState.project.collaborators = result.collaborators;
        }
    },

    deleteCollaborator: async (project: Project['id'], collaborator: string) => {
        InternalTrackingService.track('project.removeCollaborator', {
            collaborator,
        });

        const result = await ApiService.post(`${publicConfig.urls.api}/projects/${project}/collaborators/delete`, {
            collaborator,
        });

        if (currentProjectState.project?.collaborators) {
            currentProjectState.project.collaborators = result.collaborators;
        }
    },

    deleteManufacturer: async (project: Project['id'], manufacturer: string) => {
        return ApiService.delete(`${publicConfig.urls.api}/projects/${project}/manufacturers/${manufacturer}`);
    },

    deleteFile: async (project: Project['id'], fileId: string) => {
        return ApiService.post(`${publicConfig.urls.api}/projects/${project}/files/delete`, {
            fileId,
        });
    },

    requestAccess: async (projectId: string, designId: string, diagramId: string) => {
        InternalTrackingService.track('project.requestAccess');

        await ApiService.post(`${publicConfig.urls.api}/projects/${projectId}/request-access`, {
            designId,
            diagramId,
        });
    },

    getRecentProjects: async (req?: IncomingMessage): Promise<Project[]> => {
        return ApiService.get(`${publicConfig.urls.api}/projects/recent`, { req });
    },

    getTeamProjectCounts: async (req?: IncomingMessage): Promise<Record<string, number>> => {
        return await ApiService.get(`${publicConfig.urls.api}/projects/team-project-counts`, { req });
    },
};

export { ProjectService };
