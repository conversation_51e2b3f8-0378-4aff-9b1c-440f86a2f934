import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import { generateJSON } from '@tiptap/core';

import { ApiService } from 'services/ApiService';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { FileService } from 'services/FileService';
import { Geo, GeoService } from 'services/GeoService';

import { textToHtml } from 'helpers/text-to-html';

import { CompanyProfileSchema } from 'models';

import { publicConfig } from '@public-config';

export type CompanyImport = {
    id: string;
    name: string;
    linkedin: string;
    status: 'pending' | 'imported' | 'archived' | 'ignored' | 'failed';
    imported?: string;
    data?: {
        name: string;
        description: string;
        similar_companies: CompanyImportSimilarCompany[];
        [key: string]: any;
    };
    createdAt: string;
};

export type CompanyImportSimilarCompany = {
    name: string;
    link: string;
    industry: string;
};

const CompanyImportService = {
    list: async () => {
        return await ApiService.get<{
            docs: CompanyImport[];
        }>(`${publicConfig.urls.api}/companyImports?pagination=false&where[status][not_equals]=imported`);
    },

    create: async (values: Partial<CompanyImport>) => {
        return await ApiService.post(`${publicConfig.urls.api}/companyImports?depth=0`, values);
    },

    createFromUrl: async (url: string) => {
        const linkedin = CompanyImportService.getLinkedInHandle(url);
        const data = await CompanyImportService.scrape(linkedin);

        if (data.code === 404) {
            alert(`Company not found: ${url}`);

            return;
        }

        return await CompanyImportService.create({
            name: data.name,
            linkedin,
            data,
        });
    },

    recreate: async (companyImport: CompanyImport) => {
        const data = await CompanyImportService.scrape(companyImport.linkedin);

        if (data.code === 404) {
            alert(`Company not found: ${companyImport.name}`);

            return;
        }

        return await ApiService.patch(`${publicConfig.urls.api}/companyImports/${companyImport.id}?depth=0`, {
            name: data.name,
            data,
        });
    },

    getLinkedInHandle: (url: string) => {
        return url.split('/')[4];
    },

    updateStatus: async (companyImport: CompanyImport, status: CompanyImport['status']) => {
        await ApiService.patch(`${publicConfig.urls.api}/companyImports/${companyImport.id}`, {
            status,
        });
    },

    delete: async (companyImport: CompanyImport) => {
        await ApiService.delete(`${publicConfig.urls.api}/companyImports/${companyImport.id}`);
    },

    scrape: async (linkedin: string) => {
        return await ApiService.get(`${publicConfig.urls.api}/manufacturers/prefill?linkedin=${linkedin}`);
    },

    isDataExpired: (companyImport: CompanyImport): boolean => {
        const createdDate = new Date(companyImport.createdAt);
        const now = new Date();
        const diffInHours = (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60);

        return diffInHours >= 48;
    },

    import: async (companyImport: CompanyImport) => {
        if (!companyImport.data) {
            alert('No data for this import.');

            return;
        }

        const isProduction = publicConfig.environment === 'production';

        const scraper = companyImport.data;
        const name = scraper.name;

        let profileImage = null;
        let coverImage = null;

        // @ts-ignore it's fine.
        let geo: Geo = null;

        await Promise.all([
            (async () => {
                if (scraper.profile_pic_url) {
                    const { id } = await FileService.createFromUrl(scraper.profile_pic_url, `${name}-logo`, name);
                    profileImage = id;
                }
            })(),
            (async () => {
                if (scraper.background_cover_image_url) {
                    const { id } = await FileService.createFromUrl(
                        scraper.background_cover_image_url,
                        `${name}-cover`,
                        name,
                    );
                    coverImage = id;
                }
            })(),
            (async () => {
                // @ts-ignore it's fine.
                geo = await GeoService.geocode(
                    [
                        scraper.hq?.country,
                        scraper.hq?.state,
                        scraper.hq?.city,
                        scraper.hq?.postal_code,
                        scraper.hq?.line_1,
                    ].join(' '),
                );
            })(),
        ]);

        const input = CompanyProfileSchema.omit({
            id: true,
        }).parse({
            name,
            about: generateJSON(textToHtml(scraper.description), [Document, Paragraph, Text]),
            website: prependProtocolIfNeeded(scraper.website),
            socials: {
                linkedin: `https://linkedin.com/company/${companyImport.linkedin}`,
                twitter: scraper.extra?.twitter_id ? `https://x.com/${scraper.extra.twitter_id}` : '',
                facebook: scraper.extra?.facebook_id ? `https://facebook.com/${scraper.extra.facebook_id}` : '',
            },
            logos: {
                small: profileImage,
                large: profileImage,
            },
            cover: coverImage,
            locations: geo
                ? [
                      {
                          name,
                          image: null,
                          address: {
                              name: geo.full_address,
                              // @ts-ignore it exists
                              street: geo.context.address?.street_name ?? '',
                              // @ts-ignore it exists
                              number: geo.context.address?.address_number ?? '',
                              postalCode: geo.context.postcode?.name ?? '',
                              city: geo.context.place?.name ?? '',
                              state: geo.context.region?.name ?? '',
                              country: geo.context.country?.name ?? '',
                              coordinates: [geo.coordinates.longitude, geo.coordinates.latitude],
                          },
                          isHeadquarter: true,
                          contactInformation: {
                              email: scraper.extra?.contact_email ?? undefined,
                              phone: scraper.extra?.phone_number ?? undefined,
                          },
                      },
                  ]
                : [],
            services: ['inAppSupport'],
            // hardcode <EMAIL> on production
            // I know this is dirty, but I don't care! 😎
            users: isProduction ? ['67d3ed15dbb4f28d28622050'] : [],
            // hardcode DEP Managed Profiles on production
            team: isProduction ? '67cf10a75da98ad04237e47c' : undefined,
            imported: true,
        });

        try {
            const { doc: company } = await CompanyProfileService.create(input);

            await ApiService.patch(`${publicConfig.urls.api}/companyImports/${companyImport.id}`, {
                status: 'imported',
                imported: company.id,
            });
        } catch (error) {
            console.error(error);

            await ApiService.patch(`${publicConfig.urls.api}/companyImports/${companyImport.id}`, {
                status: 'failed',
            });
        }
    },
};

const prependProtocolIfNeeded = (website: string) => {
    if (!website || typeof website !== 'string' || website.trim() === '') {
        return '';
    }

    if (!website.startsWith('http://') && !website.startsWith('https://')) {
        return `https://${website}`;
    }

    return website;
};

export { CompanyImportService };
