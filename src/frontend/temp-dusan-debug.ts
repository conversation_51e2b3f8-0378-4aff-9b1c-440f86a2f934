import { subscribe } from 'valtio';

import { state } from '@diagram/state/selection';
import { state as currentUserState } from 'state/current-user';

subscribe(state, () => {
    DusanLogger.log('✅ Dusan Selection Change');
    DusanLogger.log(JSON.stringify(state.selection));
});

const DusanLogger = {
    log: (...args: any[]) => {
        if (currentUserState?.user?.goofy === 'Spiritual Tom<PERSON>') {
            console.log(...args);
        }
    },
};

export { DusanLogger };
