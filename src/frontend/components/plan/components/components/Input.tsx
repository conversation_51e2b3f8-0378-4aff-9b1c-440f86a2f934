import { useEffect } from 'react';

import { useHotkeys } from 'react-hotkeys-hook';

import {
    ActionIcon,
    Button,
    Flex,
    Group,
    Menu,
    Stack,
    Textarea,
    TextareaProps,
    Tooltip,
    Transition,
} from '@mantine/core';
import { IoAdd, IoCalendarOutline, IoClose, IoReload, IoSend } from 'react-icons/io5';

import { Hint } from './Hint';
import { ThreadHistory } from './ThreadHistory';

import { useRouter } from 'next/router';
import { useCurrentUser } from 'hooks/use-current-user';
import { useSearchThread } from 'components/plan/hooks/use-search-thread';
import { useLocalEvent } from 'hooks/use-local-event';
import { useActiveEvents } from 'hooks/use-active-events';
import { useComponentSearchBar } from 'components/component-overview/hooks/use-component-search-bar';

import { ModalService } from 'services/ModalService';

import cx from './Input.module.scss';

type Props = TextareaProps & {
    isInitial?: boolean;
    handleSubmit?: () => void;
    setValue: (value: string) => void;
    isSteaming?: boolean;
    placeholder: string;
};

export const Input = ({ handleSubmit = () => {}, isInitial, setValue, isSteaming, placeholder, ...props }: Props) => {
    const router = useRouter();
    const user = useCurrentUser();

    const { create, searchThreadId } = useSearchThread();

    const { query } = useComponentSearchBar();

    useEffect(() => {
        if (!query) return;

        setValue(query);
    }, []);

    useHotkeys(
        'enter',
        (event) => {
            event.preventDefault(); // Prevent adding a new line
            handleSubmit();
        },
        {
            enableOnFormTags: ['textarea'],
        },
    );

    const getNewPlaceholder = useGetNewPlaceholder();
    const refresh = () => {
        setValue(getNewPlaceholder());
    };

    const newChat = async () => {
        if (!user) {
            ModalService.openLoginModal({
                title: 'Start new chat?',
                message: 'Sign up or log in to save this chat and start a new one',
                redirect: `/${router.asPath}&action=save-chat&id=${searchThreadId}`,
            });
            return;
        }

        await create();
    };

    return (
        <Stack gap="sm" className={cx.container} data-initial={isInitial}>
            <Flex className={`${cx.containerInner} ${isInitial && 'animation-border-spin'}`}>
                <Textarea
                    rows={4}
                    placeholder={placeholder || (isInitial ? getNewPlaceholder() : 'Provide more details...')}
                    classNames={cx}
                    className={cx.textarea}
                    autoFocus
                    {...props}
                />

                <Group className={cx.controls} gap={6}>
                    <ThreadHistory />

                    <Transition mounted={!!isInitial} transition="fade" duration={300} timingFunction="ease">
                        {(style) => (
                            <Button
                                size="xs"
                                leftSection={<IoReload />}
                                variant="outline"
                                onClick={refresh}
                                style={style}
                            >
                                Get suggestions
                            </Button>
                        )}
                    </Transition>

                    <Transition mounted={!isInitial} transition="fade" duration={300} timingFunction="ease">
                        {(style) => (
                            <Button size="xs" variant="outline" leftSection={<IoAdd />} onClick={newChat} style={style}>
                                New chat
                            </Button>
                        )}
                    </Transition>

                    <EventButton />

                    <ActionIcon
                        variant={isSteaming ? 'filled' : 'gradient'}
                        size={30}
                        className={cx.submitButton}
                        onClick={handleSubmit}
                        disabled={isSteaming}
                        aria-label="Submit"
                        ml="auto"
                    >
                        <IoSend size="1rem" />
                    </ActionIcon>
                </Group>
            </Flex>
        </Stack>
    );
};

const EventButton = () => {
    const { localEvent, setLocalEvent } = useLocalEvent();
    const { events } = useActiveEvents();

    const clearEvent = () => {
        setLocalEvent(null);
    };

    if (!localEvent && events.length)
        return (
            <Menu trigger="click-hover" position="top-start" shadow="xl">
                <Menu.Target>
                    <Button size="xs" variant="outline" leftSection={<IoCalendarOutline />}>
                        Choose event
                    </Button>
                </Menu.Target>
                <Menu.Dropdown>
                    <Menu.Label>Filter results by event</Menu.Label>
                    {events.map((event) => (
                        <Menu.Item key={event.id} onClick={() => setLocalEvent(event.id)}>
                            {event.name}
                        </Menu.Item>
                    ))}
                </Menu.Dropdown>
            </Menu>
        );

    if (localEvent)
        return (
            <Tooltip label="Results are filtered by event">
                <Button
                    size="xs"
                    variant="outline"
                    leftSection={<IoCalendarOutline />}
                    rightSection={<IoClose />}
                    onClick={clearEvent}
                >
                    {localEvent?.name}
                </Button>
            </Tooltip>
        );

    return null;
};

const useGetNewPlaceholder = () => {
    const { localEvent } = useLocalEvent();

    const prompts: SamplePrompt[] = [];

    for (const prompt of SAMPLE_PROMPTS) {
        if (prompt.event) {
            if (!localEvent) continue;

            const enhancedPrompt = prompt.prompt.replace(
                /{{event\.(.*)}}/g,
                (_, property) => localEvent[property as keyof typeof localEvent] as string,
            );

            prompts.push({
                ...prompt,
                prompt: enhancedPrompt,
            });
        } else {
            prompts.push(prompt);
        }
    }

    const getNewPlaceholder = (currentPlaceholder?: string) => {
        const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)].prompt;

        if (currentPlaceholder === randomPrompt) {
            return getNewPlaceholder(currentPlaceholder);
        }

        return randomPrompt;
    };

    return getNewPlaceholder;
};

type SamplePrompt = {
    label: string;
    prompt: string;
    event?: boolean;
};

const SAMPLE_PROMPTS = [
    {
        label: 'Products',
        prompt: 'I am looking for a 100 kWh energy storage system for my commercial building',
    },
    {
        label: 'Products',
        prompt: 'I am looking for a 30 kW DC/DC converter for my microgrid project',
    },
    {
        label: 'Companies',
        prompt: 'I am looking for solar panel suppliers with Made in USA options',
    },
    {
        label: 'Companies',
        prompt: 'I am looking for EPC partners for solar + storage projects',
    },
    {
        label: 'Companies',
        prompt: 'I am looking for distributors at {{event.name}}',
        event: true,
    },
    {
        label: 'Products',
        prompt: 'I am looking for 25kw inverters',
    },
    {
        label: 'Products',
        prompt: 'I am looking for solutions to maximize solar self-consumption',
    },
];
