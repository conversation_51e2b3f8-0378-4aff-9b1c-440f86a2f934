import { useEffect } from 'react';

import { Body } from './components/Body';

import { Page } from 'components/page';
import { Box } from '@mantine/core';

import { sidebarNavState } from 'state/sidebar-nav';

import { useIsMobile } from 'hooks/use-is-mobile';

import cx from './PlanPage.module.scss';

export const PlanPage = () => {
    const isMobile = useIsMobile('md');

    // close sidebar
    useEffect(() => {
        sidebarNavState.isOpen = false;

        return () => {
            if (!isMobile) {
                sidebarNavState.isOpen = true;
            }
        };
    }, [isMobile]);

    return (
        <Page.FullScreenContent>
            <Box className={cx.root} data-plan-page>
                <Body />
            </Box>
        </Page.FullScreenContent>
    );
};
