import { useEffect, useState } from 'react';

import { Group, GroupProps, Tooltip, Progress, DefaultMantineColor } from '@mantine/core';
import { useInterval } from '@mantine/hooks';

type Item = {
    label: string;
    color?: DefaultMantineColor;
    status?: 'info' | 'warning' | 'error';
};

type Props = GroupProps & {
    items: Item[];
    loading?: boolean;
};

export const ProgressWithSegments = (props: Props) => {
    const { items, loading, ...groupProps } = props;

    const [count, setCount] = useState(0);
    const interval = useInterval(() => {
        setCount((c) => c + 1);
    }, 500);

    useEffect(() => {
        if (loading) {
            interval.start();
        } else {
            interval.stop();
            setCount(0);
        }

        return interval.stop;
    }, [loading]);

    return (
        <Group gap={6} {...groupProps}>
            {items.map(({ label, status, color }, index) => {
                let progressColor = 'gray.2';

                if (color) {
                    progressColor = color;
                } else if (loading) {
                    if (index === count % items.length) {
                        progressColor = 'gray';
                    }
                } else {
                    if (status === 'error') {
                        progressColor = 'red.6';
                    } else if (status === 'warning') {
                        progressColor = 'yellow.4';
                    } else {
                        progressColor = 'green.6';
                    }
                }

                return (
                    <Tooltip label={label} disabled={!label}>
                        <Progress value={100} style={{ flex: 1 }} color={progressColor} />
                    </Tooltip>
                );
            })}
        </Group>
    );
};
