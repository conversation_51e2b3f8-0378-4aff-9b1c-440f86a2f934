import Link from 'next/link';

import { But<PERSON>, <PERSON>, Stack, Text } from '@mantine/core';

import { Event } from 'models';

import { useCompanyProfiles } from 'hooks/use-company-profiles';
import { useLocalEvent } from 'hooks/use-local-event';
import { useGeneralGlobals } from 'hooks/use-general-globals';

import { RouterHelpers } from 'helpers/RouterHelpers';

import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

const EmptyMessage = () => {
    const { localEvent } = useLocalEvent();

    return (
        <Card className="gradient-background-light">
            <Stack gap="xs">
                <Text fw={600}>You are not following any profiles yet. Discover some profiles below.</Text>

                {localEvent?.companies?.length ? <EventProfiles event={localEvent} /> : <HighlightedProfiles />}

                <Button color="brand" component={Link} href={RouterHelpers.urls.searchTab('profiles')}>
                    View more
                </Button>
            </Stack>
        </Card>
    );
};

const EventProfiles = ({ event }: { event: Event }) => {
    const { companies } = useCompanyProfiles({
        ids: event.companies.map(({ company: companyId }) => companyId),
    });

    if (!companies?.length) {
        return null;
    }

    return (
        <>
            {companies.slice(0, 10)?.map((profile) => (
                <CompanyProfileTeaser company={profile} />
            ))}
        </>
    );
};

const HighlightedProfiles = () => {
    const { globals } = useGeneralGlobals();

    if (!globals?.highlights?.profiles?.length) {
        return null;
    }

    return (
        <>
            {globals.highlights.profiles.slice(0, 10)?.map((profile) => (
                <CompanyProfileTeaser company={profile} />
            ))}
        </>
    );
};

export { EmptyMessage };
