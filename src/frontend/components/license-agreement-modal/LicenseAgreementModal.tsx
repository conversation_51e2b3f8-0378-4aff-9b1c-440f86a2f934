import { FC, useState } from 'react';

import { Anchor, Button, Flex, Modal, Text } from '@mantine/core';

import { InternalTrackingService } from 'services/InternalTrackingService';
import { LocalStorageService } from 'services/LocalStorageService';

export const LICENSE_AGREEMENT_KEY = 'license-agreement';

const LicenseAgreementModal: FC = () => {
    const [open, setOpen] = useState(true);
    const localValue = LocalStorageService.get(LICENSE_AGREEMENT_KEY);

    const handleAgree = () => {
        InternalTrackingService.track('legal.agree');

        setOpen(false);
        LocalStorageService.store(LICENSE_AGREEMENT_KEY, true);
    };

    const handleDisagree = () => {
        window.location.href = 'https://www.directenergypartners.com/';
    };

    if (localValue) return null;

    return (
        <Modal
            opened={open}
            onClose={() => {}}
            withCloseButton={false}
            title="License agreement"
            overlayProps={{
                blur: 3,
            }}
        >
            <Flex gap="md" align="center" direction="column">
                <Text>
                    <p>
                        Before you proceed to the platform, please take a moment to review and agree to the{' '}
                        <Anchor target="_blank" href="/license-agreement" fw={700}>
                            License Agreement
                        </Anchor>
                        .
                    </p>

                    <p>
                        By clicking the &quot;Agree&quot; button below, you acknowledge that you have read, understood,
                        and agree to the terms of the License Agreement.
                    </p>
                    <p>If you do not agree with these terms, exit the platform by clicking &quot;Reject&quot;.</p>
                </Text>
                <Flex gap="xs">
                    <Button data-autofocus variant="filled" onClick={handleAgree}>
                        Agree
                    </Button>
                    <Button color="red" variant="light" onClick={handleDisagree}>
                        Reject
                    </Button>
                </Flex>
            </Flex>
        </Modal>
    );
};

export { LicenseAgreementModal };
