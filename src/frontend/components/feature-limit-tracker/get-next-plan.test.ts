import { DesignerSubscription, FeatureLimit } from 'models';
import { getNextPlan } from './get-next-plan';
import { expect, test } from '@jest/globals';

test('getNextPlan should return nextPlan PLUS when PLUS is Infinity', () => {
    const currentPlan = DesignerSubscription.FREE;
    const expectedPlan = DesignerSubscription.PLUS;
    const testFeatureLimits: Record<FeatureLimit, Record<DesignerSubscription, number>> = {
        [FeatureLimit.SIMULATIONS]: {
            [DesignerSubscription.FREE]: 10,
            [DesignerSubscription.PLUS]: Infinity,
            [DesignerSubscription.PRO]: Infinity,
        },
    } as any;

    const nextPlan = getNextPlan(currentPlan, FeatureLimit.SIMULATIONS, testFeatureLimits);

    expect(nextPlan).toEqual(expectedPlan);
});

test('getNextPlan should return nextPlan when currentPlan is infinity', () => {
    const testFeatureLimits: Record<FeatureLimit, Record<DesignerSubscription, number>> = {
        [FeatureLimit.SIMULATIONS]: {
            [DesignerSubscription.FREE]: 10,
            [DesignerSubscription.PLUS]: Infinity,
            [DesignerSubscription.PRO]: Infinity,
        },
    } as any;

    const currentPlan = DesignerSubscription.PLUS;
    const expectedPlan = DesignerSubscription.PRO;

    const nextPlan = getNextPlan(currentPlan, FeatureLimit.SIMULATIONS, testFeatureLimits);

    expect(nextPlan).toEqual(expectedPlan);
});

test('getNextPlan should return PRO plan when PLUS is not enough', () => {
    const testFeatureLimits: Record<FeatureLimit, Record<DesignerSubscription, number>> = {
        [FeatureLimit.SIMULATIONS]: {
            [DesignerSubscription.FREE]: 10,
            [DesignerSubscription.PLUS]: 10,
            [DesignerSubscription.PRO]: Infinity,
        },
    } as any;

    const currentPlan = DesignerSubscription.FREE;
    const expectedPlan = DesignerSubscription.PRO;

    const nextPlan = getNextPlan(currentPlan, FeatureLimit.SIMULATIONS, testFeatureLimits);

    expect(nextPlan).toEqual(expectedPlan);
});

test('getNextPlan should return PLUS plan when PLUS has more than FREE and is not inifinity', () => {
    const testFeatureLimits: Record<FeatureLimit, Record<DesignerSubscription, number>> = {
        [FeatureLimit.SIMULATIONS]: {
            [DesignerSubscription.FREE]: 10,
            [DesignerSubscription.PLUS]: 25,
            [DesignerSubscription.PRO]: Infinity,
        },
    } as any;

    const currentPlan = DesignerSubscription.FREE;
    const expectedPlan = DesignerSubscription.PLUS;

    const nextPlan = getNextPlan(currentPlan, FeatureLimit.SIMULATIONS, testFeatureLimits);

    expect(nextPlan).toEqual(expectedPlan);
});
