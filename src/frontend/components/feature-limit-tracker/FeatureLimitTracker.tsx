import React from 'react';

import { <PERSON>, CardProps, <PERSON>lex, Loader, RingProgress, Stack, Text } from '@mantine/core';

import { FeatureLimit, FeatureLimits, DesignerSubscription, Team, type TeamInfo } from 'models';

import { useCheckFeatureLimit } from 'hooks/use-check-feature-limit';
import { useCurrentTeam } from 'hooks/use-current-team';
import { useCurrentTeams } from 'hooks/use-current-teams';
import { useCurrentTeamSubscription } from 'hooks/use-current-team-subscription';

import { SubscriptionOnboardingButton } from 'components/subscriptions/SubscriptionOnboardingButton';
import { getNextPlan } from './get-next-plan';

type TrackerCopy = {
    remaining: React.ReactNode;
    almostReached: React.ReactNode;
    reached: React.ReactNode;
};

type FeatureLimitTrackerCopy = Record<FeatureLimit, TrackerCopy>;

const getNextLimitCopy = (limit: number) =>
    limit === Infinity ? (
        <Text span variant="gradient" fw={700}>
            unlimited
        </Text>
    ) : (
        'even more'
    );

const getColor = (remaning: number) => {
    switch (true) {
        case remaning < 10:
            return 'red';
        case remaning < 25:
            return 'orange';
        case remaning < 50:
            return 'yellow';
        default:
            return 'primary';
    }
};

const getCopyKey = (remaning: number): keyof TrackerCopy => {
    switch (true) {
        case remaning <= 0:
            return 'reached';
        case remaning < 25:
            return 'almostReached';
        default:
            return 'remaining';
    }
};

const getCopy = ({
    remaining,
    nextPlan,
    teams,
    team,
}: {
    remaining: number;
    nextPlan: DesignerSubscription;
    teams: TeamInfo[];
    team: Team;
}): FeatureLimitTrackerCopy => ({
    [FeatureLimit.PROJECTS]: {
        remaining: (
            <>
                You have{' '}
                <b>
                    {remaining} {remaining === 1 ? 'project' : 'projects'}
                </b>{' '}
                remaining. Upgrade to unlock {getNextLimitCopy(FeatureLimits.PROJECTS[nextPlan])} projects!
            </>
        ),
        almostReached: (
            <>
                <b>Almost maxed out on projects!</b> Upgrade to unlock{' '}
                {getNextLimitCopy(FeatureLimits.PROJECTS[nextPlan])} projects!
            </>
        ),
        reached:
            teams.length > 1 ? (
                <>
                    <b>Project limit for team {team.name} reached!</b> Switch to another team or upgrade to unlock{' '}
                    {getNextLimitCopy(FeatureLimits.PROJECTS[nextPlan])} projects!
                </>
            ) : (
                <>
                    <b>Project limit reached!</b>
                    <br />
                    Upgrade to unlock {getNextLimitCopy(FeatureLimits.PROJECTS[nextPlan])} projects!
                </>
            ),
    },
    [FeatureLimit.AI_REQUESTS]: {
        remaining: (
            <>
                You have{' '}
                <b>
                    {remaining} AI {remaining === 1 ? 'credit' : 'credits'}
                </b>{' '}
                remaining. Upgrade to unlock {getNextLimitCopy(FeatureLimits.AI_REQUESTS[nextPlan])} AI access!
            </>
        ),
        almostReached: (
            <>
                <b>Almost maxed out on your AI credits!</b> Upgrade to unlock{' '}
                {getNextLimitCopy(FeatureLimits.AI_REQUESTS[nextPlan])} AI access!
            </>
        ),
        reached: (
            <>
                <b>AI limit reached!</b> Upgrade to unlock {getNextLimitCopy(FeatureLimits.AI_REQUESTS[nextPlan])} AI
                access!
            </>
        ),
    },
    [FeatureLimit.IMAGES]: {
        remaining: (
            <>
                You have{' '}
                <b>
                    {remaining} image {remaining === 1 ? 'upload' : 'uploads'}
                </b>{' '}
                remaining. Upgrade to unlock {getNextLimitCopy(FeatureLimits.IMAGES[nextPlan])} image uploads!
            </>
        ),
        almostReached: (
            <>
                <b>Almost maxed out on image uploads!</b> Upgrade to unlock{' '}
                {getNextLimitCopy(FeatureLimits.IMAGES[nextPlan])} image uploads!
            </>
        ),
        reached: (
            <>
                <b>Image upload limit reached!</b> Upgrade to unlock {getNextLimitCopy(FeatureLimits.IMAGES[nextPlan])}{' '}
                image uploads!
            </>
        ),
    },
    [FeatureLimit.SIMULATIONS]: {
        remaining: (
            <>
                You have{' '}
                <b>
                    {remaining} simulation {remaining === 1 ? 'credit' : 'credits'}
                </b>{' '}
                remaining. Upgrade to unlock {getNextLimitCopy(FeatureLimits.SIMULATIONS[nextPlan])} simulations!
            </>
        ),
        almostReached: (
            <>
                <b>Almost maxed out on your simulation credits!</b> Upgrade to unlock{' '}
                {getNextLimitCopy(FeatureLimits.SIMULATIONS[nextPlan])} simulations!
            </>
        ),
        reached: (
            <>
                <b>Simulation limit reached!</b> Upgrade to unlock{' '}
                {getNextLimitCopy(FeatureLimits.SIMULATIONS[nextPlan])} simulations!
            </>
        ),
    },
    [FeatureLimit.DESIGN_FILE_UPLOADS]: {
        remaining: (
            <>
                You have{' '}
                <b>
                    {remaining} design file {remaining === 1 ? 'upload' : 'uploads'}
                </b>{' '}
                remaining. Upgrade to unlock {getNextLimitCopy(FeatureLimits.DESIGN_FILE_UPLOADS[nextPlan])} uploads!
            </>
        ),
        almostReached: (
            <>
                <b>Almost maxed out on your design file uploads!</b> Upgrade to unlock{' '}
                {getNextLimitCopy(FeatureLimits.DESIGN_FILE_UPLOADS[nextPlan])} uploads!
            </>
        ),
        reached: (
            <>
                <b>Design file upload limit reached!</b> Upgrade to unlock{' '}
                {getNextLimitCopy(FeatureLimits.DESIGN_FILE_UPLOADS[nextPlan])} uploads!
            </>
        ),
    },
});

const DiagramFeatureLimitTracker = ({
    diagramId,
    feature,
    ...props
}: {
    diagramId: string;
    feature: FeatureLimit.IMAGES | FeatureLimit.DESIGN_FILE_UPLOADS;
    onUpgradeClick?: () => void;
} & CardProps) => {
    const { status } = useCheckFeatureLimit(feature, diagramId);

    return <FeatureLimitTrackerBase feature={feature} status={status} {...props} />;
};

const FeatureLimitTracker = ({
    feature,
    ...props
}: {
    feature: Exclude<FeatureLimit, FeatureLimit.IMAGES | FeatureLimit.DESIGN_FILE_UPLOADS>;
    onUpgradeClick?: () => void;
} & CardProps) => {
    const { status } = useCheckFeatureLimit(feature);
    return <FeatureLimitTrackerBase feature={feature} status={status} {...props} />;
};

const FeatureLimitTrackerBase = ({
    feature,
    status,
    onUpgradeClick,
    ...props
}: {
    feature: FeatureLimit;
    status: {
        count: number;
        limit: number;
    } | null;
    onUpgradeClick?: () => void;
} & CardProps) => {
    const team = useCurrentTeam()!;
    const teams = useCurrentTeams();

    const plan = useCurrentTeamSubscription();

    if (!status) {
        return (
            <Card w="100%" p="xs" {...props}>
                <Loader size="sm" color="primary" mx="auto" my="auto" />
            </Card>
        );
    }

    const { limit, count } = status;

    const nextPlan = getNextPlan(plan, feature, FeatureLimits);

    const remaining = Math.max(limit - count, 0);

    const remainingPercentage = (remaining / limit) * 100;
    const color = getColor(remainingPercentage);

    const copy = getCopy({
        remaining,
        nextPlan,
        teams,
        team,
    })[feature];
    const copyKey = getCopyKey(remainingPercentage);

    if (limit === 9999) return null; // 9999 = unlimited
    if (plan === DesignerSubscription.PRO) return null;

    return (
        <Card w="100%" p="xs" {...props}>
            <Flex h="100%" gap="xs" align="center">
                <RingProgress
                    roundCaps
                    size={80}
                    thickness={6}
                    sections={[{ value: remainingPercentage, color }]}
                    rootColor={`${color}.1`}
                    label={
                        <Stack align="center" ta="center" gap={0}>
                            <Text fz={10} c="dimmed">
                                Left
                            </Text>
                            <Text fz="sm" fw={800} c={color} mt={-4}>
                                {remaining}/{limit}
                            </Text>
                        </Stack>
                    }
                />

                <Stack align="flex-start" gap={8}>
                    <Text>{copy[copyKey]}</Text>

                    <SubscriptionOnboardingButton size="xs" onClick={onUpgradeClick}>
                        Upgrade to {nextPlan.toUpperCase()}
                    </SubscriptionOnboardingButton>
                </Stack>
            </Flex>
        </Card>
    );
};

export { FeatureLimitTracker, DiagramFeatureLimitTracker };
