import { FeatureLimit, DesignerSubscription } from 'models';

const getNextPlan = (
    currentPlan: DesignerSubscription,
    feature: FeatureLimit,
    featureLimits: Record<FeatureLimit, Record<DesignerSubscription, number>>,
): DesignerSubscription => {
    const nextPlan = currentPlan === DesignerSubscription.FREE ? DesignerSubscription.PLUS : DesignerSubscription.PRO;

    const nextPlanlimit = featureLimits[feature][nextPlan];
    const currentPlanLimit = featureLimits[feature][currentPlan];

    // Find the next plan that has a higher limit than the current plan
    if (currentPlanLimit != Infinity && currentPlanLimit >= nextPlanlimit) {
        return getNextPlan(nextPlan, feature, featureLimits);
    }
    return nextPlan;
};

export { getNextPlan };
