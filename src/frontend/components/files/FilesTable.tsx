import React, { FC, ReactNode, useEffect, useState } from 'react';

import { ActionIcon, Anchor, Box, Center, Flex, Group, Stack, Table, Text, Tooltip, Transition } from '@mantine/core';

import { TbFile } from 'react-icons/tb';
import { IoC<PERSON><PERSON>mark, IoEye, IoEyeOff, IoTrashOutline } from 'react-icons/io5';

import { CollectionFile, FileVisibility, digitalConverter } from 'models';
import { DateService } from 'services/DateService';
import { useFile } from 'hooks/use-file';
import { FileIdOrUrl } from './types';
import { AsyncActionIcon } from 'components/async-button/AsyncActionIcon';
import { FormatHelpers } from 'helpers/formatters';
import { getId } from 'helpers/getId';

type DeleteFile = (file: CollectionFile) => Promise<void>;
type ToggleVisibility = (file: CollectionFile) => Promise<void>;

type FilesTableRowsProps = {
    fileToAnimate?: FileIdOrUrl;
    onFinishAnimate?: () => void;
};

type FilesTableProps = {
    files?: CollectionFile[];
    deleteFile: DeleteFile;
} & FilesTableRowsProps;

const FilesTable: FC<FilesTableProps> = ({ files, ...rowsProps }) => {
    if (!files || files.length === 0) {
        return (
            <Center>
                <NoFiles />
            </Center>
        );
    }

    return (
        <Flex gap="sm" align="center">
            <Table
                horizontalSpacing={0}
                style={{
                    '& tbody > tr > td': {
                        verticalAlign: 'top',
                    },
                }}
            >
                <Table.Tbody>
                    {files.map((file) =>
                        file.url ? (
                            <UrlRow key={file.id} file={file} {...rowsProps} />
                        ) : (
                            <FileRow key={file.id} file={file} {...rowsProps} />
                        ),
                    )}
                </Table.Tbody>
            </Table>
        </Flex>
    );
};

type RowProps = {
    file: CollectionFile;
    deleteFile?: DeleteFile;
    toggleVisibility?: ToggleVisibility;
    selected?: boolean;
    onSelect?: (fileId: string) => void;
    children?: ReactNode;
} & FilesTableRowsProps;

const FileRow: FC<RowProps> = ({
    file,
    deleteFile,
    toggleVisibility,
    fileToAnimate,
    onFinishAnimate,
    selected,
    onSelect,
    children,
}) => {
    const { file: fileMeta } = useFile(file.file);

    const type = 'type' in file ? file.type : undefined;

    if (!fileMeta) {
        return null;
    }

    const RowContents = (
        <>
            {onSelect && (
                <Table.Td>
                    <Tooltip label="Select file">
                        <ActionIcon
                            size="sm"
                            radius={99}
                            color="primary.4"
                            onClick={() => onSelect(getId(file.file)!)}
                            variant={selected ? 'filled' : 'default'}
                        >
                            <IoCheckmark />
                        </ActionIcon>
                    </Tooltip>
                </Table.Td>
            )}
            <Table.Td>
                <Flex gap={4} align="center" justify="space-between">
                    <Anchor download href={fileMeta.url} target="_blank" style={{ textDecoration: 'none' }}>
                        <Stack gap={2}>
                            <Group gap={4}>
                                <TbFile size={12} />
                                <Text fw={700}>{type || 'File'}</Text>
                            </Group>

                            <Text c="dimmed" size="xs">
                                {fileMeta.filename}
                            </Text>

                            <Text c="dimmed" size="xs">
                                {fileMeta.filesize && FormatHelpers.formatValue(fileMeta.filesize, digitalConverter)}
                                {(fileMeta.createdAt || fileMeta.createdBy) && (
                                    <React.Fragment>
                                        {' • '}
                                        Uploaded
                                        {fileMeta.createdAt && (
                                            <> {DateService.formatDistanceToNow(fileMeta.createdAt)}</>
                                        )}
                                        {fileMeta.createdBy?.name && <> by {fileMeta.createdBy?.name}</>}
                                    </React.Fragment>
                                )}
                            </Text>
                        </Stack>
                    </Anchor>
                    {children}
                </Flex>
            </Table.Td>
            {toggleVisibility && (
                <FileVisibilityButton file={file} visibility={file.visibility} toggleVisibility={toggleVisibility} />
            )}
            {deleteFile && <FileDeleteButton file={file} deleteFile={deleteFile} />}
        </>
    );

    if (fileToAnimate && 'file' in fileToAnimate && fileToAnimate.file === fileMeta.id) {
        return <TransitionRow onFinishAnimate={onFinishAnimate}>{RowContents}</TransitionRow>;
    }

    return <Table.Tr>{RowContents}</Table.Tr>;
};

const UrlRow: FC<RowProps> = ({ file, deleteFile, fileToAnimate, onFinishAnimate }) => {
    const type = 'type' in file ? file.type : undefined;

    const RowContents = (
        <>
            <Table.Td>
                <Flex gap={4}>
                    <Anchor download href={file.url} target="_blank">
                        <Stack gap={2}>
                            <Group gap={4}>
                                <TbFile size={12} />
                                <Text fw={700}>{type ?? 'File'}</Text>
                            </Group>

                            <Text c="dimmed" size="xs">
                                {file.url}
                            </Text>
                        </Stack>
                    </Anchor>
                </Flex>
            </Table.Td>
            {deleteFile && <FileDeleteButton file={file} deleteFile={deleteFile} />}
        </>
    );

    if (fileToAnimate && 'url' in fileToAnimate && fileToAnimate.url === file.url) {
        return <TransitionRow onFinishAnimate={onFinishAnimate}>{RowContents}</TransitionRow>;
    }

    return <Table.Tr>{RowContents}</Table.Tr>;
};

const FileDeleteButton: FC<{ file: CollectionFile; deleteFile: DeleteFile }> = ({ file, deleteFile }) => {
    return (
        <Table.Td style={{ textAlign: 'right', paddingLeft: 4, width: 24 }}>
            <AsyncActionIcon color="red" variant="light" size="sm" onClick={async () => deleteFile(file)}>
                <IoTrashOutline size={12} />
            </AsyncActionIcon>
        </Table.Td>
    );
};

const FileVisibilityButton: FC<{
    file: CollectionFile;
    visibility: FileVisibility | null;
    toggleVisibility: ToggleVisibility;
}> = ({ file, visibility, toggleVisibility }) => {
    const nextVisibility = visibility === FileVisibility.PRIVATE ? FileVisibility.PUBLIC : FileVisibility.PRIVATE;

    return (
        <Table.Td style={{ textAlign: 'right', padding: 4, width: 24 }}>
            <Tooltip
                label={`File is ${visibility ?? FileVisibility.PUBLIC}. Click to make this file ${nextVisibility}.`}
            >
                <Box>
                    <AsyncActionIcon
                        variant="transparent"
                        size="sm"
                        onClick={async () => toggleVisibility(file)}
                        color={visibility === FileVisibility.PRIVATE ? 'red' : 'primary'}
                    >
                        {visibility === FileVisibility.PRIVATE ? <IoEyeOff size={12} /> : <IoEye size={12} />}
                    </AsyncActionIcon>
                </Box>
            </Tooltip>
        </Table.Td>
    );
};

const TransitionRow: FC<{ children: ReactNode; onFinishAnimate?: () => void }> = ({
    children,
    onFinishAnimate = () => {},
}) => {
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        const timeout = setTimeout(() => setMounted(true), 500);

        return () => {
            clearTimeout(timeout);
        };
    }, []);

    return (
        <Transition mounted={mounted} transition="pop" duration={400} timingFunction="ease" onEntered={onFinishAnimate}>
            {(styles) => <Table.Tr style={styles}>{children}</Table.Tr>}
        </Transition>
    );
};

const NoFiles: FC = () => <Text c="dimmed">No files uploaded yet</Text>;

export { FilesTable, FileRow };
