import { Component, ComponentType } from 'models';

const useDatasheetMeta = (component: Component) => {
    const componentType = ComponentType.options.find((option) => option.value === component.type);

    const subtitle = [componentType?.label, component.productIdentifier, component.productSeries]
        .filter(Boolean)
        .join(' · ');

    const shortSubtitle = [component.productIdentifier, component.productSeries].filter(Boolean).join(' · ');

    return { componentType, subtitle, shortSubtitle };
};

export { useDatasheetMeta };
