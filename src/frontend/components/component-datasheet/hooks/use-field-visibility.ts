import { DatasheetMode, useDatasheetMode } from 'components/datasheet/DatasheetMode';
import { valueHasNonNullNonUnitValue } from 'helpers/not-null-helpers';
import { useWatch } from 'react-hook-form';

export enum FieldVisibility {
    HIDDEN,
    VISIBLE,
}

export const useFieldVisibility = (name: string) => {
    const mode = useDatasheetMode();
    const value = useWatch({ name });

    if (mode !== DatasheetMode.VIEW) {
        return FieldVisibility.VISIBLE;
    }

    return valueHasNonNullNonUnitValue(value) ? FieldVisibility.VISIBLE : FieldVisibility.HIDDEN;
};
