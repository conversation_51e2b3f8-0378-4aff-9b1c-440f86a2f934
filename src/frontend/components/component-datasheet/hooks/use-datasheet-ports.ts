import { useFormContext, useWatch } from 'react-hook-form';

import { crush, isNumber, uid } from 'radash';

import { Component, getComponentPayloadValidator } from 'models';

import { useComponentBulkFields } from 'components/component-bulk-editor/ComponentBulkFieldsContext';
import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentHelpers } from 'helpers/ComponentHelpers';

const useDatasheetPorts = () => {
    const [ports, type] = useWatch({
        name: ['electrical.ports', 'type'],
    }) as [Component['electrical']['ports'], Component['type']];

    const { setValue } = useFormContext();
    const { addOverride } = useComponentBulkFields();
    const mode = useDatasheetMode();

    const addPort = (copyPortIndex?: number, numberOfCopies = 1) => {
        if (!ComponentHelpers.componentTypeHasElectrical(type)) return;

        const componentValidator = getComponentPayloadValidator(type);

        const newComponent = componentValidator.parse({ name: '' });

        if (!newComponent.electrical) {
            return;
        }

        const portToCopy = isNumber(copyPortIndex) ? ports[copyPortIndex] : newComponent.electrical.ports[0];
        const newPorts = Array.from({ length: numberOfCopies }, () => ({
            ...portToCopy,
            id: uid(8),
        }));

        // save the new port when bulk editing
        if (mode === DatasheetMode.BULK) {
            newPorts.forEach((port, index) => {
                const newPortValues = crush(port);

                Object.keys(newPortValues).forEach((key) => {
                    addOverride(`electrical.ports.${ports.length + index}.${key}`);
                });
            });
        }

        setValue('electrical.ports', [...ports, ...newPorts]);
    };

    const deletePort = (index: number) => {
        const newPorts = ports.filter((_, i) => i !== index);

        setValue('electrical.ports', newPorts);
    };

    return { addPort, deletePort };
};

export { useDatasheetPorts };
