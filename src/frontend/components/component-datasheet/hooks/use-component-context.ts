import { createContext, useContext } from 'react';
import { Component } from 'models';

const CurrentComponentContext = createContext<{
    component: Component | null;
    setComponent: (component: Component) => void;
}>({
    component: null,
    setComponent: () => {},
});

const useComponentContext = () => {
    const { component, setComponent } = useContext(CurrentComponentContext);

    if (component === null) {
        throw new Error('Component accessed outside of context');
    }

    return { component, setComponent };
};

const useOptionalComponentContext = () => {
    const { component, setComponent } = useContext(CurrentComponentContext);

    return { component, setComponent };
};

export { CurrentComponentContext, useComponentContext, useOptionalComponentContext };
