import { useEffect, useState } from 'react';

import { useWatch } from 'react-hook-form';

import { Component } from 'models';

const useDatasheetPort = (index: number) => {
    const values = useWatch({
        name: `electrical.ports.${index}`,
    }) as Component['electrical']['ports'][number];

    const initVoltageType =
        'AC' in values && values.AC.enabled ? 'AC' : 'DC' in values && values.DC.enabled ? 'DC' : null;
    const [voltageType, setVoltageType] = useState<'AC' | 'DC' | null>(initVoltageType);

    useEffect(() => {
        if (voltageType !== initVoltageType) {
            setVoltageType(initVoltageType);
        }
    }, [initVoltageType]);

    const hasAC = 'AC' in values;
    const hasDC = 'DC' in values;

    const showAC = hasAC && values.AC.enabled;
    const showDC = hasDC && values.DC.enabled;

    return { hasAC, hasDC, showAC, showDC, values, voltageType, setVoltageType };
};

export { useDatasheetPort };
