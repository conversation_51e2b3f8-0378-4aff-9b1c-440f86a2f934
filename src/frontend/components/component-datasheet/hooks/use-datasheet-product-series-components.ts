import { Component } from 'models';

import { getId } from 'helpers/getId';

import { useWatch } from 'react-hook-form';
import { useProductSeriesComponents } from 'hooks/use-product-series-components';

const useDatasheetProductSeriesComponents = () => {
    const [manufacturer, productSeries] = useWatch({
        name: ['manufacturer', 'productSeries'],
    }) as [Component['manufacturer'], Component['productSeries']];

    return useProductSeriesComponents({
        manufacturerId: getId(manufacturer),
        productSeries: productSeries,
    });
};

export { useDatasheetProductSeriesComponents };
