import React, { <PERSON> } from 'react';

import { Stack } from '@mantine/core';

import { Component } from 'models';

import { Datasheet, DatasheetMode } from 'components/datasheet';

import { General, Electrical, Files } from './sections/team';
import { Images } from './sections/Images';

type ComponentDatasheetProps = {
    component?: Component;
};

const ComponentDatasheet: FC<ComponentDatasheetProps> = () => {
    return (
        <Datasheet initialMode={DatasheetMode.EDIT}>
            <Stack gap="xs" w="100%">
                <General />
                <Electrical />
                <Files />
                <Images />
            </Stack>
        </Datasheet>
    );
};

export { ComponentDatasheet };
