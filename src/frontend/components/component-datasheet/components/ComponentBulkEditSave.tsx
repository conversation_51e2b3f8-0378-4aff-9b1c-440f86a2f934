import { useWatch } from 'react-hook-form';

import { Button, ButtonProps } from '@mantine/core';

import { Component } from 'models';

import { useComponentBulkFields } from 'components/component-bulk-editor/ComponentBulkFieldsContext';
import { useDatasheetProductSeriesComponents } from 'components/component-datasheet/hooks/use-datasheet-product-series-components';

const ComponentBulkEditSave = (props: Omit<ButtonProps, 'onClick'>) => {
    const { bulkSave } = useComponentBulkFields();

    const component = useWatch() as Component;

    const { components: variants } = useDatasheetProductSeriesComponents();

    return (
        <Button {...props} onClick={() => bulkSave(component)}>
            Save all {variants?.length} variants
        </Button>
    );
};

export { ComponentBulkEditSave };
