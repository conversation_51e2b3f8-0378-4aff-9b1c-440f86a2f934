import Link from 'next/link';
import React, { FC } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import { Button, ButtonProps, Menu, Text } from '@mantine/core';
import { TbArchive, TbArchiveOff, Tb<PERSON><PERSON>cil } from 'react-icons/tb';
import { IoChevronDown, IoDuplicateOutline } from 'react-icons/io5';

import { getId } from 'helpers/getId';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { ComponentService } from 'services/ComponentService';

import { useSubmitForm } from 'components/forms/Form';
import { DatasheetMode, useDatasheetModeState } from 'components/datasheet';
import { useDatasheetProductSeriesComponents } from 'components/component-datasheet/hooks/use-datasheet-product-series-components';
import { useComponentContext } from '../hooks/use-component-context';

import { ReferenceDesignButton } from 'components/reference-design-button/ReferenceDesignButton';
import { ComponentBulkEditSave } from 'components/component-datasheet/components/ComponentBulkEditSave';
import { PublishButton } from 'components/component-signup/components/PublishButton';

const ComponentDatasheetActionsEdit: FC<{ buttonSize?: ButtonProps['size'] }> = ({ buttonSize = 'compact-sm' }) => {
    const { component } = useComponentContext();
    const [archivedAt, id, manufacturer, productSeries, visibility] = useWatch({
        name: ['archivedAt', 'id', 'manufacturer', 'productSeries', 'visibility'],
    });

    const { reset, setValue } = useFormContext();
    const values = useWatch();
    const submitForm = useSubmitForm();
    const [mode, setMode] = useDatasheetModeState();

    const { components: variants } = useDatasheetProductSeriesComponents();

    const isPublishable = visibility === 'private';

    const saveComponent = () => {
        submitForm()
            .then(() => {
                LocalNotificationService.showSuccess({
                    message: 'Product saved successfully!',
                });
            })
            .then(postSaveAction)
            .catch((error: Error) => {
                LocalNotificationService.showError({
                    message: error.message,
                });
            });
    };

    const postSaveAction =
        mode === DatasheetMode.EDIT
            ? () => {
                  setMode(DatasheetMode.VIEW);
              }
            : () => {};

    const editComponent = () => {
        setMode(DatasheetMode.EDIT);
    };

    const cancelEdit = () => {
        reset();
        setMode(DatasheetMode.VIEW);
    };

    const duplicateComponent = () => {
        ComponentService.navigate.duplicate(id);
    };

    const editProductSeries = () => {
        ComponentService.navigate.bulk(getId(manufacturer)!, productSeries);
    };

    const attemptValueUpdate = (key: string, newValue: any, successMessage = 'Component updated!') => {
        const oldValue = values[key];

        setValue(key, newValue);
        submitForm()
            .then(() => {
                LocalNotificationService.showSuccess({
                    message: successMessage,
                });
            })
            .catch(() => {
                setValue(key, oldValue);
            });
    };

    const archiveComponent = () => {
        attemptValueUpdate('archivedAt', new Date().toISOString(), 'Component archived successfully!');
    };

    const unarchiveComponent = () => {
        attemptValueUpdate('archivedAt', null, 'Component unarchived successfully!');
    };

    const makeComponentPublic = () => {
        attemptValueUpdate('visibility', 'public', 'Component is now public!');
    };

    const hasMissingRequiredFields = !values.name;

    if (mode === DatasheetMode.CREATE || mode === DatasheetMode.DUPLICATE) {
        return (
            <Button size={buttonSize} variant="filled" disabled={hasMissingRequiredFields} onClick={saveComponent}>
                Save new product
            </Button>
        );
    }

    if (mode === DatasheetMode.EDIT) {
        return (
            <>
                <Button size={buttonSize} variant="subtle" onClick={cancelEdit}>
                    Discard changes
                </Button>
                <Button size={buttonSize} variant="filled" onClick={saveComponent}>
                    Save changes
                </Button>
            </>
        );
    }

    if (mode === DatasheetMode.BULK) {
        return <ComponentBulkEditSave size={buttonSize} variant="filled" />;
    }

    return (
        <>
            <Menu trigger="click-hover" position="bottom-end">
                <Menu.Target>
                    <Button
                        size={buttonSize}
                        variant="subtle"
                        onClick={editComponent}
                        leftSection={<TbPencil size={12} />}
                        rightSection={<IoChevronDown size={12} />}
                    >
                        Edit
                    </Button>
                </Menu.Target>

                <Menu.Dropdown>
                    <Menu.Item onClick={editComponent} leftSection={<TbPencil size={12} />}>
                        Edit Product
                    </Menu.Item>

                    {!!variants?.length && (
                        <Menu.Item onClick={editProductSeries} leftSection={<TbPencil size={12} />}>
                            Edit Product Series
                        </Menu.Item>
                    )}

                    {component.type !== 'other' && (
                        <Menu.Item
                            component={Link}
                            href={`${ComponentHelpers.urls.generate(component.id)}`}
                            leftSection={
                                <Text mt={2} fw={900} fz={10} c="brand.4">
                                    AI
                                </Text>
                            }
                        >
                            <span className="gradient-text-default">Generate Datasheet</span>
                        </Menu.Item>
                    )}

                    {archivedAt ? (
                        <Menu.Item onClick={unarchiveComponent} leftSection={<TbArchiveOff size={12} />}>
                            Unarchive
                        </Menu.Item>
                    ) : (
                        <Menu.Item color="red" onClick={archiveComponent} leftSection={<TbArchive size={12} />}>
                            Archive
                        </Menu.Item>
                    )}
                </Menu.Dropdown>

                <Button
                    size={buttonSize}
                    variant="subtle"
                    onClick={duplicateComponent}
                    leftSection={<IoDuplicateOutline size={12} />}
                >
                    Add variant
                </Button>
            </Menu>

            <ReferenceDesignButton
                size={buttonSize}
                component={component}
                variant={isPublishable ? 'subtle' : 'filled'}
            />
            {isPublishable && <PublishButton size={buttonSize} handlePublish={makeComponentPublic} variant="filled" />}
        </>
    );
};

export { ComponentDatasheetActionsEdit };
