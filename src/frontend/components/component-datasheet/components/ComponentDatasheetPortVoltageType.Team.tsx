import React, { FC } from 'react';

import { Datasheet, DatasheetMode, useDatasheetMode } from 'components/datasheet';

import { Component, VoltageType } from 'models';

import { SwitchField } from 'components/forms/fields/SwitchField';
import { VoltageField } from 'components/component-fields/VoltageField';
import { CurrentField } from 'components/component-fields/CurrentField';
import { PowerField } from 'components/component-fields/PowerField';

import { useWatch } from 'react-hook-form';

type ComponentDatasheetPortVoltageTypeProps = {
    index: number;
    voltageType: VoltageType;
};

const ComponentDatasheetPortVoltageType: FC<ComponentDatasheetPortVoltageTypeProps> = ({ index, voltageType }) => {
    const electrical = useWatch({ name: 'electrical' }) as Component['electrical'];

    const mode = useDatasheetMode();

    const key = `electrical.ports.${index}.${voltageType}`;
    // @ts-ignore
    const values = electrical?.ports?.[index]?.[voltageType];

    return (
        <React.Fragment>
            {(mode === DatasheetMode.CREATE || mode === DatasheetMode.EDIT || mode === DatasheetMode.BULK) && (
                <Datasheet.TableRow noMetadata name={`${key}.enabled`} label="Enabled" voltageType={voltageType}>
                    <SwitchField name={`${key}.enabled`} size="xs" />
                </Datasheet.TableRow>
            )}
            {values?.enabled && (
                <React.Fragment>
                    {'voltage' in values && (
                        <Datasheet.TableRow name={`${key}.voltage`} label="Voltage" voltageType={voltageType}>
                            <VoltageField name={`${key}.voltage`} />
                        </Datasheet.TableRow>
                    )}
                    {'current' in values && (
                        <Datasheet.TableRow name={`${key}.current`} label="Current" voltageType={voltageType}>
                            <CurrentField name={`${key}.current`} />
                        </Datasheet.TableRow>
                    )}
                    {'power' in values && (
                        <Datasheet.TableRow name={`${key}.power`} label="Power" voltageType={voltageType}>
                            <PowerField name={`${key}.power`} />
                        </Datasheet.TableRow>
                    )}
                </React.Fragment>
            )}
        </React.Fragment>
    );
};

export { ComponentDatasheetPortVoltageType };
