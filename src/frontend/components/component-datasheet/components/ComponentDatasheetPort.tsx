import React, { FC, ReactNode } from 'react';

import { pick } from 'radash';

import { Box, Collapse, Group, Text } from '@mantine/core';
import { modals } from '@mantine/modals';
import { useDisclosure } from '@mantine/hooks';
import { HVACPortPurpose, PortPurpose, all, getComponentDefinition } from 'models';

import { Datasheet, DatasheetMode, useDatasheetMode } from 'components/datasheet';

import { ComponentDatasheetPortVoltageType } from './ComponentDatasheetPortVoltageType';
import { CapacitanceField } from 'components/component-fields/CapacitanceField';
import { InductanceField } from 'components/component-fields/InductanceField';
import { InrushCurrentField } from 'components/component-fields/InrushCurrentField';
import { PolesField } from 'components/component-fields/PolesField';
import { PortFeaturesField } from 'components/component-fields/PortFeaturesField';
import { PortIsolatedField } from 'components/component-fields/PortIsolatedField';
import { PowerFlowField } from 'components/component-fields/PowerFlowField';
import { ResidualCurrentDeviceTripLevelField } from 'components/component-fields/ResidualCurrentDeviceTripLevelField';
import { TripCurveField } from 'components/component-fields/TripCurveField';
import { IntegerField } from 'components/forms/fields/IntegerField';
import { VoltageTypeSwitchField } from 'components/component-fields/VoltageTypeSwitchField';
import { TextField } from 'components/forms/fields/TextField';
import { SelectField } from 'components/forms/fields/SelectField';
import { AiAssistantProductComponent } from './AiAssistantProductComponent';
import { useDatasheetPort } from 'components/component-datasheet/hooks/use-datasheet-port';
import { useComponentContext } from '../hooks/use-component-context';
import { useDatasheetElectrical } from 'components/component-datasheet/hooks/use-datasheet-electrical';
import { ComponentDatasheetPortDuplicateModal } from 'components/component-datasheet/components/ComponentDatasheetPort.DuplicateModal';
import { ComponentDatasheetRightSection } from './ComponentDatasheetPortRightSection';
import { useDatasheetPorts } from '../hooks/use-datasheet-ports';

const ComponentDatasheetPort: FC<{ index: number; label: ReactNode }> = ({ index, label }) => {
    const { component } = useComponentContext();
    const mode = useDatasheetMode();

    const [duplicateOpened, duplicateHandlers] = useDisclosure();

    const componentDefinition = getComponentDefinition(component.type);

    const { values, showAC, showDC } = useDatasheetPort(index);

    const electrical = useDatasheetElectrical();
    const { deletePort } = useDatasheetPorts();

    const canEdit = mode !== DatasheetMode.VIEW;
    const canDelete = index + 1 > componentDefinition.ports.min;
    const canAdd = electrical.ports.length < componentDefinition.ports.max;

    if (mode === DatasheetMode.VIEW && !showAC && !showDC) {
        return null;
    }

    if (componentDefinition.ports.displayAsSinglePort && index !== 0) {
        return null;
    }

    const openDeleteModal = () => {
        modals.openConfirmModal({
            centered: true,
            title: `Are you sure you want to delete ${label}?`,
            labels: { confirm: 'Yes, delete', cancel: "No, don't delete it" },
            confirmProps: { variant: 'light', color: 'red' },
            closeOnConfirm: false,
            onConfirm: handleDelete,
        });
    };

    const handleDelete = async () => {
        deletePort(index);
        modals.closeAll();
    };

    return (
        <>
            {duplicateOpened && (
                <ComponentDatasheetPortDuplicateModal
                    index={index}
                    label={label}
                    handleClose={duplicateHandlers.close}
                />
            )}

            <Datasheet.Table
                header={{
                    prefix: ['Electrical'],
                    label: (
                        <Group gap={4} align="center">
                            {label}
                            {values.powerFlowDirection && (
                                <Text tt="initial" fz="xs" c="gray.5">
                                    ({values.powerFlowDirection})
                                </Text>
                            )}
                        </Group>
                    ),
                    rightSection: (
                        <ComponentDatasheetRightSection
                            showAC={showAC}
                            showDC={showDC}
                            label={label}
                            onDuplicate={canAdd ? duplicateHandlers.open : undefined}
                            onDelete={canDelete ? openDeleteModal : undefined}
                        />
                    ),
                }}
            >
                {mode !== DatasheetMode.VIEW && (
                    <Datasheet.TableRow noMetadata name={`${index}.enabled`} label="This port supports">
                        <VoltageTypeSwitchField index={index} py={4} px="xs" />
                    </Datasheet.TableRow>
                )}

                <Collapse in={showAC}>
                    <ComponentDatasheetPortVoltageType index={index} label={label} voltageType="AC" />
                </Collapse>
                <Collapse in={showDC}>
                    <ComponentDatasheetPortVoltageType index={index} label={label} voltageType="DC" />
                </Collapse>

                <Datasheet.RowSpan
                    header={{
                        prefix: ['Electrical', label],
                        label: 'General',
                    }}
                >
                    {'description' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.description`} label="Description">
                            <TextField name={`electrical.ports.${index}.description`} />
                        </Datasheet.TableRow>
                    )}
                    {'purpose' in values && (
                        <Datasheet.TableRow
                            name={`electrical.ports.${index}.purpose`}
                            label="Purpose"
                            description="What can be connected to this port?"
                        >
                            <SelectField
                                name={`electrical.ports.${index}.purpose`}
                                data={Object.values(pick(all, getPortPurposeTypesForComponentType(component.type))).map(
                                    (component) => ({
                                        value: component.type,
                                        label: component.name,
                                    }),
                                )}
                            />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.purpose`} />
                        </Datasheet.TableRow>
                    )}
                    {'poles' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.poles`} label="Poles">
                            <PolesField name={`electrical.ports.${index}.poles`} />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.poles`} />
                        </Datasheet.TableRow>
                    )}
                    {'tripCurve' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.tripCurve`} label="Trip Curve">
                            <TripCurveField name={`electrical.ports.${index}.tripCurve`} />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.tripCurve`} />
                        </Datasheet.TableRow>
                    )}
                    {'powerFlowDirection' in values && (
                        <Datasheet.TableRow
                            name={`electrical.ports.${index}.powerFlowDirection`}
                            label="Power Flow Direction"
                        >
                            <Box pl="xs">
                                <PowerFlowField
                                    name={`electrical.ports.${index}.powerFlowDirection`}
                                    componentDefinition={componentDefinition}
                                    showAsLabel={!canEdit}
                                />
                            </Box>
                            <AiAssistantProductComponent name={`electrical.ports.${index}.powerFlowDirection`} />
                        </Datasheet.TableRow>
                    )}
                    {'isolated' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.isolated`} label="Isolated">
                            <Box pl="xs">
                                <PortIsolatedField name={`electrical.ports.${index}.isolated`} showAsLabel={!canEdit} />
                            </Box>
                            <AiAssistantProductComponent name={`electrical.ports.${index}.isolated`} />
                        </Datasheet.TableRow>
                    )}
                    {'capacitance' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.capacitance`} label="Capacitance">
                            <CapacitanceField name={`electrical.ports.${index}.capacitance`} />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.capacitance`} />
                        </Datasheet.TableRow>
                    )}
                    {'inductance' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.inductance`} label="Inductance">
                            <InductanceField name={`electrical.ports.${index}.inductance`} />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.inductance`} />
                        </Datasheet.TableRow>
                    )}
                    {'inrushCurrent' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.inrushCurrent`} label="Inrush Current">
                            <InrushCurrentField name={`electrical.ports.${index}.inrushCurrent`} />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.inrushCurrent`} />
                        </Datasheet.TableRow>
                    )}
                    {'parallelableCapacity' in values && (
                        <Datasheet.TableRow
                            name={`electrical.ports.${index}.parallelableCapacity`}
                            label="Parallelable Capacity"
                        >
                            <IntegerField name={`electrical.ports.${index}.parallelableCapacity`} />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.parallelableCapacity`} />
                        </Datasheet.TableRow>
                    )}
                    {'features' in values && (
                        <Datasheet.TableRow name={`electrical.ports.${index}.features`} label="Built-in Features">
                            <PortFeaturesField name={`electrical.ports.${index}.features`} />
                            <AiAssistantProductComponent name={`electrical.ports.${index}.features`} />
                        </Datasheet.TableRow>
                    )}
                    {'residualCurrentDeviceTripLevel' in values &&
                        'features' in values &&
                        values.features.includes('residual-current-detection') && (
                            <Datasheet.TableRow
                                name={`electrical.ports.${index}.residualCurrentDeviceTripLevel`}
                                label="Residual Current Device Trip Level"
                            >
                                <ResidualCurrentDeviceTripLevelField
                                    name={`electrical.ports.${index}.residualCurrentDeviceTripLevel`}
                                />
                                <AiAssistantProductComponent
                                    name={`electrical.ports.${index}.residualCurrentDeviceTripLevel`}
                                />
                            </Datasheet.TableRow>
                        )}
                </Datasheet.RowSpan>
            </Datasheet.Table>
        </>
    );
};

const getPortPurposeTypesForComponentType = (componentType: string) => {
    switch (componentType) {
        case 'hvac':
            return [...HVACPortPurpose.options];
        default:
            return [...PortPurpose.options];
    }
};

export { ComponentDatasheetPort };
