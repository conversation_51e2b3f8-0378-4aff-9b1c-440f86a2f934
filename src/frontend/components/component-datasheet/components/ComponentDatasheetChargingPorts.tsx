import { crush, uid } from 'radash';
import { useFormContext } from 'react-hook-form';
import { modals } from '@mantine/modals';
import { Box, Button } from '@mantine/core';
import { IoAdd } from 'react-icons/io5';

import { Datasheet, DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetRightSection } from './ComponentDatasheetPortRightSection';

import { ChargerConnectorField } from 'components/component-fields/ChargerConnectorField';
import { ChargerFeaturesField } from 'components/component-fields/ChargerFeaturesField';
import { ChargerVoltageTypeSwitchField } from 'components/component-fields/ChargerVoltageTypeSwitchField';
import { CurrentField } from 'components/component-fields/CurrentField';
import { PowerField } from 'components/component-fields/PowerField';
import { VoltageField } from 'components/component-fields/VoltageField';
import { useComponentBulkFields } from 'components/component-bulk-editor/ComponentBulkFieldsContext';

import { Charger, ChargerPayloadValidator } from 'models';

export const ComponentDatasheetChargingPorts = ({ chargingPorts }: { chargingPorts: any[] }) => {
    const { setValue } = useFormContext();
    const mode = useDatasheetMode();
    const { addOverride } = useComponentBulkFields();

    if (!chargingPorts || chargingPorts.length === 0) return null;

    const handleDelete = async (index: number) => {
        setValue(
            'electrical.chargingPorts',
            chargingPorts.filter((_, i) => i !== index),
        );
        modals.closeAll();
    };

    const addPort = () => {
        const newComponent = ChargerPayloadValidator.parse({ name: '' });
        const newPort = newComponent.electrical.chargingPorts[0];

        // save the new port when bulk editing
        if (mode === DatasheetMode.BULK) {
            const newPortValues = crush(newPort);

            Object.keys(newPortValues).forEach((key) => {
                addOverride(`electrical.chargingPorts.${chargingPorts.length}.${key}`);
            });
        }

        setValue('electrical.chargingPorts', [...chargingPorts, newPort]);
    };

    return (
        <>
            {chargingPorts.map((chargingPort, index) => (
                <ChargingPortRow
                    key={uid(10)}
                    chargingPort={chargingPort}
                    index={index}
                    handleDelete={chargingPorts.length > 1 ? () => handleDelete(index) : undefined}
                />
            ))}

            {mode === DatasheetMode.EDIT || mode === DatasheetMode.CREATE || mode === DatasheetMode.BULK ? (
                <Box mb="sm">
                    <Button onClick={addPort} size="xs" leftSection={<IoAdd />}>
                        Add Charging Port {chargingPorts.length + 1} Specifications
                    </Button>
                </Box>
            ) : null}
        </>
    );
};

const ChargingPortRow = ({
    chargingPort,
    index,
    handleDelete,
}: {
    chargingPort: Charger['electrical']['chargingPorts'][number];
    index: number;
    handleDelete?: () => void;
}) => {
    const mode = useDatasheetMode();

    const { voltageType } = chargingPort;
    const label = `Charging Port ${index + 1}`;
    const subLabel = voltageType ? `${chargingPort.voltageType} specifications` : `Specifications`;

    const openDeleteModal = () => {
        modals.openConfirmModal({
            centered: true,
            title: `Are you sure you want to delete ${label}?`,
            labels: { confirm: 'Yes, delete', cancel: "No, don't delete it" },
            confirmProps: { variant: 'light', color: 'red' },
            closeOnConfirm: false,
            onConfirm: handleDelete,
        });
    };

    return (
        <Datasheet.Table
            header={{
                prefix: ['Electrical'],
                label,
                rightSection: (
                    <ComponentDatasheetRightSection
                        showAC={voltageType === 'AC'}
                        showDC={voltageType === 'DC'}
                        label={label}
                        onDelete={openDeleteModal}
                    />
                ),
            }}
        >
            <Datasheet.RowSpan
                header={{ prefix: ['Electrical', label], label: subLabel }}
                color={voltageType ? `var(--mantine-color-${chargingPort.voltageType}-filled)` : undefined}
            >
                {'voltageType' in chargingPort && mode !== DatasheetMode.VIEW && (
                    <Datasheet.TableRow
                        name={`electrical.chargingPorts.${index}.voltageType`}
                        label="This connector supports"
                    >
                        <ChargerVoltageTypeSwitchField name={`electrical.chargingPorts.${index}.voltageType`} />
                    </Datasheet.TableRow>
                )}
                {'connector' in chargingPort && (
                    <Datasheet.TableRow name={`electrical.chargingPorts.${index}.connector`} label="Connector Type">
                        <ChargerConnectorField name={`electrical.chargingPorts.${index}.connector`} />
                    </Datasheet.TableRow>
                )}
                {'power' in chargingPort && (
                    <Datasheet.TableRow name={`electrical.chargingPorts.${index}.power`} label="Power">
                        <PowerField name={`electrical.chargingPorts.${index}.power`} fields={['min', 'nom', 'max']} />
                    </Datasheet.TableRow>
                )}
                {'voltage' in chargingPort && (
                    <Datasheet.TableRow name={`electrical.chargingPorts.${index}.voltage`} label="Voltage">
                        <VoltageField name={`electrical.chargingPorts.${index}.voltage`} fields={['min', 'max']} />
                    </Datasheet.TableRow>
                )}
                {'current' in chargingPort && (
                    <Datasheet.TableRow name={`electrical.chargingPorts.${index}.current`} label="Current">
                        <CurrentField name={`electrical.chargingPorts.${index}.current`} fields={['min', 'max']} />
                    </Datasheet.TableRow>
                )}
                {'features' in chargingPort && (
                    <Datasheet.TableRow name={`electrical.chargingPorts.${index}.features`} label="Features">
                        <ChargerFeaturesField name={`electrical.chargingPorts.${index}.features`} />
                    </Datasheet.TableRow>
                )}
            </Datasheet.RowSpan>
        </Datasheet.Table>
    );
};
