import React, { FC } from 'react';

import { Flex, Stack, Text, Title } from '@mantine/core';

import { CompanyLogo } from 'components/company-logo';

import { useCompanyProfile } from 'hooks';
import { useComponentContext } from '../hooks/use-component-context';
import { useDatasheetMeta } from '../hooks/use-datasheet-meta';

const ComponentDatasheetHeader: FC = () => {
    const { component } = useComponentContext();
    const { company: manufacturer } = useCompanyProfile(component.manufacturer);

    const { subtitle } = useDatasheetMeta(component);

    return (
        <Stack gap={0}>
            <Flex justify="space-between" gap="xs" style={{ flexGrow: 1 }}>
                <Stack gap={0}>
                    <Title order={2} size="h1" fw={700}>
                        {component.name}
                    </Title>
                    <Flex columnGap="xl" rowGap="xs">
                        <Text c="dimmed">{subtitle}</Text>
                    </Flex>
                </Stack>

                {manufacturer && <CompanyLogo logos={manufacturer.logos} width={80} />}
            </Flex>
        </Stack>
    );
};

export { ComponentDatasheetHeader };
