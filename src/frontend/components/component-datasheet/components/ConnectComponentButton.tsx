import { useState } from 'react';
import { Tooltip } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { Component } from 'models';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';
import { useCurrentUser } from 'hooks/use-current-user';
import { ModalService } from 'services/ModalService';
import { ConnectButton } from 'components/buttons/ConnectButton';
import { ConnectButtonState } from 'components/buttons/ConnectButton';
import { SelectCompatibleProductsDialog } from './SelectCompatibleProductsDialog';
import { CreateProfileDialog } from 'components/company-profile/components/CreateProfileDialog';

type Props = {
    component: Component;
};

export const ConnectComponentButton = ({ component }: Props) => {
    const [state, setState] = useState(ConnectButtonState.DEFAULT);

    const [showCompatibleProductsDialog, { open: openSelectProfileDialog, close: closeCompatibleProductsDialog }] =
        useDisclosure(false);
    const [showCreateProfileDialog, { open: openCreateProfileDialog }] = useDisclosure(false);

    const user = useCurrentUser();
    const { companies } = useCurrentTeamCompanies();

    const hasAccount = !!user;
    const hasProfile = Boolean(companies.length);

    const tagCompatibleProduct = async () => {
        if (!hasAccount) {
            // Login first
            ModalService.openLoginModal({
                message: 'Enter your email address to connect with this company.',
            });
            return;
        } else if (!hasProfile) {
            // Create a profile first
            openCreateProfileDialog();
            return;
        } else {
            openSelectProfileDialog();
        }
    };

    return (
        <>
            <Tooltip label="Click if you offer a product compatible with this product">
                <ConnectButton state={state} onClick={tagCompatibleProduct} />
            </Tooltip>
            <SelectCompatibleProductsDialog
                opened={showCompatibleProductsDialog}
                close={closeCompatibleProductsDialog}
                component={component}
                onSuccess={() => setState(ConnectButtonState.CONNECTED)}
            />
            <CreateProfileDialog opened={showCreateProfileDialog} close={closeCompatibleProductsDialog} />
        </>
    );
};
