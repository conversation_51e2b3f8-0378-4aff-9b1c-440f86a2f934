import React, { FC } from 'react';

import { Button, Group, Text } from '@mantine/core';

const ComponentDatasheetPortVoltageTypeSupport: FC<{
    label?: string;
    showAC: boolean;
    showDC: boolean;
}> = ({ label, showAC, showDC }) => {
    return (
        <Group gap={4}>
            {label && (
                <Text inherit mr={6}>
                    {label}
                </Text>
            )}

            {showAC && (
                <Button component="span" size="compact-xs" color="AC" c="white" radius={2}>
                    AC
                </Button>
            )}
            {showDC && (
                <Button component="span" size="compact-xs" color="DC" c="white" radius={2}>
                    DC
                </Button>
            )}
        </Group>
    );
};

export { ComponentDatasheetPortVoltageTypeSupport };
