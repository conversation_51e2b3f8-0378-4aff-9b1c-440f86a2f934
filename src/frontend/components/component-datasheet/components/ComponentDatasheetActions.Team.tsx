import React, { useState } from 'react';

import { Button } from '@mantine/core';
import { TbDeviceFloppy } from 'react-icons/tb';

import { LocalNotificationService } from 'services/LocalNotificationService';
import { useSubmitForm } from 'components/forms/Form';

const ComponentDatasheetActions = () => {
    const [loading, setLoading] = useState(false);

    const submitForm = useSubmitForm();

    const saveComponent = () => {
        setLoading(true);
        submitForm()
            .then(() => {
                LocalNotificationService.showSuccess({
                    message: 'Component saved successfully!',
                });
            })
            .then(() => setLoading(false));
    };

    return (
        <Button variant="filled" onClick={saveComponent} leftSection={<TbDeviceFloppy size={12} />} loading={loading}>
            Save
        </Button>
    );
};

export { ComponentDatasheetActions };
