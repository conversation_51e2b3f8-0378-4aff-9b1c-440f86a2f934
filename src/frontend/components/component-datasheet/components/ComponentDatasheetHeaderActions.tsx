import React from 'react';

import { useWindowScroll } from '@mantine/hooks';
import { Affix, Box, BoxProps, Group, Text, Transition } from '@mantine/core';

import { ComponentVisibility, PermissionComponent, PublishedStatus } from 'models';

import { useComponentPermissions } from 'hooks/use-component-permissions';
import { useDatasheetMeta } from 'components/component-datasheet/hooks/use-datasheet-meta';

import { PublishedBadge } from 'components/published-badge/PublishedBadge';
import { ComponentProgress } from 'components/component-progress/ComponentProgress';
import { ComponentDatasheetActionsEdit } from 'components/component-datasheet/components/ComponentDatasheetActions.Edit';
import { useComponentContext } from '../hooks/use-component-context';

import cx from './ComponentDatasheetHeaderActions.module.scss';

const ComponentDatasheetEditHeader = (props: BoxProps) => {
    const { component } = useComponentContext();
    const permissions = useComponentPermissions(component);
    const canEdit = permissions[PermissionComponent.EDIT];

    const { componentType, shortSubtitle } = useDatasheetMeta(component);

    if (!canEdit) {
        return null;
    }

    const showPublishedBadge = !component.archivedAt && !component.deletedAt;

    return (
        <Box className={cx.root} {...props}>
            <Group className={cx.leftContent}>
                <Text fz="sm" fw={600}>
                    {shortSubtitle}
                    <Text span inherit c="dimmed">
                        {' '}
                        ({componentType?.label})
                    </Text>
                </Text>

                {showPublishedBadge && (
                    <PublishedBadge
                        status={
                            component.visibility === ComponentVisibility.PUBLIC
                                ? PublishedStatus.PUBLISHED
                                : PublishedStatus.DRAFT
                        }
                    />
                )}
                <Group gap={0}>
                    <ComponentProgress component={component} />
                    <Text c="dimmed" fz="xs" fw={500}>
                        % complete
                    </Text>
                </Group>
            </Group>

            <Group gap={4}>
                <ComponentDatasheetActionsEdit buttonSize="compact-xs" />
            </Group>
        </Box>
    );
};

const ComponentDatasheetEditHeaderScroll = () => {
    const [scroll] = useWindowScroll();

    const { component } = useComponentContext();
    const permissions = useComponentPermissions(component);
    const canEdit = permissions[PermissionComponent.EDIT];

    if (!canEdit) {
        return null;
    }

    return (
        <Affix position={{ top: 0, left: 0, right: 0 }}>
            <Transition transition="slide-down" mounted={scroll.y > 200}>
                {(transitionStyles) => <ComponentDatasheetEditHeader style={transitionStyles} />}
            </Transition>
        </Affix>
    );
};

export { ComponentDatasheetEditHeader, ComponentDatasheetEditHeaderScroll };
