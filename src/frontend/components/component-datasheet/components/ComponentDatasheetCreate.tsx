import React from 'react';

import { Card, Stack } from '@mantine/core';

import { ComponentSection } from 'models';

import { Datasheet, DatasheetMode } from 'components/datasheet';

import { Communication, Electrical, Environmental, Images, Mechanical, Performance, Standards } from './sections';
import { FilesPerType } from './sections/FilesPerType';

const CreateComponentDatasheet = ({ showSections }: { showSections: ComponentSection[] }) => {
    if (showSections.includes(ComponentSection.FILES) || showSections.includes(ComponentSection.IMAGES)) {
        return (
            <Datasheet initialMode={DatasheetMode.CREATE}>
                {showSections.includes(ComponentSection.IMAGES) && <Images />}
                {showSections.includes(ComponentSection.FILES) && <FilesPerType />}
            </Datasheet>
        );
    }

    return (
        <Datasheet initialMode={DatasheetMode.CREATE}>
            <Card>
                <Stack gap="xl">
                    {showSections.includes(ComponentSection.ELECTRICAL) && <Electrical />}
                    {showSections.includes(ComponentSection.PERFORMANCE) && <Performance />}
                    {showSections.includes(ComponentSection.COMMUNICATION) && <Communication />}
                    {showSections.includes(ComponentSection.STANDARDS) && <Standards />}
                    {showSections.includes(ComponentSection.MECHANICAL) && <Mechanical />}
                    {showSections.includes(ComponentSection.ENVIRONMENTAL) && <Environmental />}
                </Stack>
            </Card>
        </Datasheet>
    );
};

export { CreateComponentDatasheet };
