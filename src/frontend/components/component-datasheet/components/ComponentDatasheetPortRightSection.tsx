import React, { FC, ReactNode } from 'react';

import { Button } from '@mantine/core';
import { IoCopyOutline, IoTrashOutline } from 'react-icons/io5';

import { DatasheetMode, useDatasheetMode } from 'components/datasheet';

import { ComponentDatasheetPortVoltageTypeSupport } from 'components/component-datasheet/components/ComponentDatasheetPort.VoltageTypeSupport';

type ComponentDatasheetRightSectionProps = {
    showAC: boolean;
    showDC: boolean;
    label: ReactNode;
    onDelete?: () => void;
    onDuplicate?: () => void;
};

const ComponentDatasheetRightSection: FC<ComponentDatasheetRightSectionProps> = ({
    showAC,
    showDC,
    label,
    onDelete,
    onDuplicate,
}) => {
    const mode = useDatasheetMode();

    const isEditMode = mode === DatasheetMode.EDIT || mode === DatasheetMode.CREATE || mode === DatasheetMode.DUPLICATE;
    const isViewMode = mode === DatasheetMode.VIEW;

    if (isViewMode) {
        return <ComponentDatasheetPortVoltageTypeSupport showAC={showAC} showDC={showDC} label="This port supports" />;
    }

    if (!isEditMode) {
        return null;
    }

    if (!(onDelete || onDuplicate)) {
        return null;
    }

    return (
        <>
            {onDuplicate && (
                <Button
                    size="compact-xs"
                    variant="transparent"
                    c="gray.4"
                    leftSection={<IoCopyOutline />}
                    onClick={onDuplicate}
                >
                    Duplicate
                </Button>
            )}
            {onDelete && (
                <Button
                    c="red.3"
                    size="compact-xs"
                    variant="transparent"
                    onClick={onDelete}
                    leftSection={<IoTrashOutline style={{ color: 'var(--mantine-color-red-3)' }} />}
                >
                    Remove {label}
                </Button>
            )}
        </>
    );
};

export { ComponentDatasheetRightSection };
