import React, { FC } from 'react';

import { SavedItemType } from 'models';

import { Button, Flex, Group, Tooltip } from '@mantine/core';

import { TbCopy } from 'react-icons/tb';
import { IoAddOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';
import { useComponentContext } from '../hooks/use-component-context';

import { CopyComponentButton } from 'components/component-datasheet/components/CopyComponentButton';
import { SaveButton } from 'components/save-button/SaveButton';
import { ProjectService } from 'services/ProjectService';
import { ShareButton } from 'components/share-button/ShareButton';
// import { ConnectComponentButton } from './ConnectComponentButton';

const ComponentDatasheetActionsPublic: FC = () => {
    const user = useCurrentUser();

    const { component } = useComponentContext();

    const createDesign = () => {
        ProjectService.navigate
            .create({
                referenceComponent: component.id,
            })
            .then();
    };

    return (
        <Flex justify="space-between" align="center" style={{ flex: 1 }}>
            <Group gap={4}>
                {component.id && <SaveButton id={component.id} name={component.name} type={SavedItemType.COMPONENT} />}
                {user && (
                    <Tooltip label="Create a new design around this component">
                        <Button
                            size="compact-xs"
                            variant="subtle"
                            onClick={createDesign}
                            leftSection={<IoAddOutline size={12} />}
                            visibleFrom="md"
                        >
                            Create design...
                        </Button>
                    </Tooltip>
                )}
                <CopyComponentButton
                    size="compact-xs"
                    variant="subtle"
                    leftSection={<TbCopy size={12} />}
                    component={component}
                    visibleFrom="md"
                />
                <ShareButton type="product" />
            </Group>
            {/* <Group>
                <ConnectComponentButton component={component} />
            </Group> */}
        </Flex>
    );
};

export { ComponentDatasheetActionsPublic };
