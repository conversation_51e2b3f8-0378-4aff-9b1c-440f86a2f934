import React, { FC, ReactNode } from 'react';

import { z } from 'zod';

import { Button, Modal, Space } from '@mantine/core';
import { getComponentDefinition } from 'models';

import { useComponentContext } from '../hooks/use-component-context';
import { useDatasheetPorts } from 'components/component-datasheet/hooks/use-datasheet-ports';
import { useDatasheetElectrical } from 'components/component-datasheet/hooks/use-datasheet-electrical';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { IntegerField } from 'components/forms/fields/IntegerField';

import { DatasheetMantineProviderReset } from 'components/datasheet/DatasheetMantineProviderReset';

const ComponentDatasheetPortDuplicateModal: FC<{
    index: number;
    label: ReactNode;
    handleClose: () => void;
}> = ({ index, label, handleClose }) => {
    const { component } = useComponentContext();

    const componentDefinition = getComponentDefinition(component.type);

    const electrical = useDatasheetElectrical();
    const { addPort } = useDatasheetPorts();

    const canAddMax = Math.min(componentDefinition.ports.max - electrical.ports.length, 99); // max 99, let's not go overboard ;D

    const zodSchema = z.object({
        numberOfCopies: z.number().int().min(1).max(canAddMax),
    });

    return (
        <Modal opened onClose={handleClose} withCloseButton={false}>
            <DatasheetMantineProviderReset>
                <Form
                    onSubmit={(values) => {
                        addPort(index, values.numberOfCopies);

                        handleClose();
                    }}
                    defaultValues={{ numberOfCopies: 1 }}
                    zodSchema={zodSchema}
                >
                    <IntegerField name="numberOfCopies" label="Number of copies" min={1} max={canAddMax} />

                    <Space h="md" />

                    <FormSubmit>Duplicate {label}</FormSubmit>
                    <Button variant="transparent" onClick={handleClose}>
                        Cancel
                    </Button>
                </Form>
            </DatasheetMantineProviderReset>
        </Modal>
    );
};

export { ComponentDatasheetPortDuplicateModal };
