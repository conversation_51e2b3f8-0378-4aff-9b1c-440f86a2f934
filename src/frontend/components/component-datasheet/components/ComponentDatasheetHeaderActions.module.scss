.root {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--mantine-spacing-sm);

    background-color: var(--mantine-color-white);
    padding: 4px var(--mantine-spacing-xs);

    box-shadow: var(--mantine-shadow-xs);

    .leftContent {
        gap: var(--mantine-spacing-xs);
    }

    @media (max-width: $mantine-breakpoint-md) {
        justify-content: end;

        padding: var(--mantine-spacing-xs);

        .leftContent {
            display: none;
        }

        :global(.mantine-Button-section):where([data-position='left']) {
            margin-inline-end: 4px;
        }
    }

    @media (max-width: $mantine-breakpoint-sm) {
        [data-reference-design-button] {
            display: none;
        }
    }
}
