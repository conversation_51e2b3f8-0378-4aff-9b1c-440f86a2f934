import React, { FC, ReactNode } from 'react';

import { Component, VoltageType } from 'models';

import { Datasheet } from 'components/datasheet';

import { PolesField } from 'components/component-fields/PolesField';
import { VoltageField } from 'components/component-fields/VoltageField';
import { CurrentField } from 'components/component-fields/CurrentField';
import { PowerField } from 'components/component-fields/PowerField';
import { PowerFactorField } from 'components/component-fields/PowerFactorField';
import { FrequencyField } from 'components/component-fields/FrequencyField';
import { PortConfigurationField } from 'components/component-fields/PortConfigurationField';
import { PortControlMethodsField } from 'components/component-fields/PortControlMethodsField';
import { EarthingConfigurationsField } from 'components/component-fields/EarthingConfigurationsField';
import { TimeField } from 'components/component-fields/TimeField';
import { AiAssistantProductComponent } from './AiAssistantProductComponent';

import { useWatch } from 'react-hook-form';

type ComponentDatasheetPortVoltageTypeProps = {
    index: number;
    label: ReactNode;
    voltageType: VoltageType;
};

const ComponentDatasheetPortVoltageType: FC<ComponentDatasheetPortVoltageTypeProps> = ({
    index,
    label,
    voltageType,
}) => {
    const [electrical] = useWatch({
        name: ['electrical'],
    }) as [Component['electrical']];

    const key = `electrical.ports.${index}.${voltageType}`;
    // @ts-ignore
    const values = electrical?.ports?.[index]?.[voltageType] ?? {};

    return (
        <Datasheet.RowSpan
            header={{
                prefix: ['Electrical', label],
                label: `${voltageType} specifications`,
            }}
            color={`var(--mantine-color-${voltageType}-filled)`}
        >
            {'poles' in values && (
                <Datasheet.TableRow name={`${key}.poles`} label="Poles" voltageType={voltageType}>
                    <PolesField name={`${key}.poles`} />
                    <AiAssistantProductComponent name={`${key}.poles`} />
                </Datasheet.TableRow>
            )}
            {'voltage' in values && (
                <Datasheet.TableRow name={`${key}.voltage`} label="Voltage" voltageType={voltageType}>
                    <VoltageField name={`${key}.voltage`} />
                    <AiAssistantProductComponent name={`${key}.voltage`} />
                </Datasheet.TableRow>
            )}
            {'maximumPowerPointVoltage' in values && (
                <Datasheet.TableRow
                    name={`${key}.maximumPowerPointVoltage`}
                    label="Voltage (MPP)"
                    voltageType={voltageType}
                >
                    <VoltageField name={`${key}.maximumPowerPointVoltage`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.maximumPowerPointVoltage`} />
                </Datasheet.TableRow>
            )}
            {'voltagePerPole' in values && (
                <Datasheet.TableRow name={`${key}.voltagePerPole`} label="Voltage per Pole" voltageType={voltageType}>
                    <VoltageField name={`${key}.voltagePerPole`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.voltagePerPole`} />
                </Datasheet.TableRow>
            )}
            {'current' in values && (
                <Datasheet.TableRow name={`${key}.current`} label="Current" voltageType={voltageType}>
                    <CurrentField
                        name={`${key}.current`}
                        // @ts-ignore
                        fields={Object.keys(values.current).filter((field) => field !== 'unit')}
                    />
                    <AiAssistantProductComponent name={`${key}.current`} />
                </Datasheet.TableRow>
            )}
            {'maximumPowerPointCurrent' in values && (
                <Datasheet.TableRow
                    name={`${key}.maximumPowerPointCurrent`}
                    label="Current (MPP)"
                    voltageType={voltageType}
                >
                    <CurrentField name={`${key}.maximumPowerPointCurrent`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.maximumPowerPointCurrent`} />
                </Datasheet.TableRow>
            )}
            {'power' in values && (
                <Datasheet.TableRow name={`${key}.power`} label="Power" voltageType={voltageType}>
                    <PowerField name={`${key}.power`} />
                    <AiAssistantProductComponent name={`${key}.power`} />
                </Datasheet.TableRow>
            )}
            {'frequency' in values && (
                <Datasheet.TableRow name={`${key}.frequency`} label="Frequency" voltageType={voltageType}>
                    <FrequencyField name={`${key}.frequency`} />
                    <AiAssistantProductComponent name={`${key}.frequency`} />
                </Datasheet.TableRow>
            )}
            {'powerFactor' in values && (
                <Datasheet.TableRow name={`${key}.powerFactor`} label="Power Factor" voltageType={voltageType}>
                    <PowerFactorField name={`${key}.powerFactor`} />
                    <AiAssistantProductComponent name={`${key}.powerFactor`} />
                </Datasheet.TableRow>
            )}
            {'openCircuitVoltage' in values && (
                <Datasheet.TableRow
                    name={`${key}.openCircuitVoltage`}
                    label="Open circuit voltage"
                    voltageType={voltageType}
                >
                    <VoltageField name={`${key}.openCircuitVoltage`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.openCircuitVoltage`} />
                </Datasheet.TableRow>
            )}
            {'shortCircuitCurrent' in values && (
                <Datasheet.TableRow
                    name={`${key}.shortCircuitCurrent`}
                    label="Short circuit current"
                    voltageType={voltageType}
                >
                    <CurrentField name={`${key}.shortCircuitCurrent`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.shortCircuitCurrent`} />
                </Datasheet.TableRow>
            )}
            {'instantaneousShortCircuitCurrentUL' in values && (
                <Datasheet.TableRow
                    name={`${key}.instantaneousShortCircuitCurrentUL`}
                    label="Instantaneous short-circuit current (UL)"
                    voltageType={voltageType}
                >
                    <CurrentField name={`${key}.instantaneousShortCircuitCurrentUL`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.instantaneousShortCircuitCurrentUL`} />
                </Datasheet.TableRow>
            )}
            {'serviceShortCircuitBreakingCapacityIEC' in values && (
                <Datasheet.TableRow
                    name={`${key}.serviceShortCircuitBreakingCapacityIEC`}
                    label="Service short-circuit breaking capacity (IEC)"
                    voltageType={voltageType}
                >
                    <CurrentField name={`${key}.serviceShortCircuitBreakingCapacityIEC`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.serviceShortCircuitBreakingCapacityIEC`} />
                </Datasheet.TableRow>
            )}
            {'ultimateShortCircuitBreakingCapacityIEC' in values && (
                <Datasheet.TableRow
                    name={`${key}.ultimateShortCircuitBreakingCapacityIEC`}
                    label="Ultimate short-circuit breaking capacity (IEC)"
                    voltageType={voltageType}
                >
                    <CurrentField name={`${key}.ultimateShortCircuitBreakingCapacityIEC`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.ultimateShortCircuitBreakingCapacityIEC`} />
                </Datasheet.TableRow>
            )}
            {'minimumBreakingCapacity' in values && (
                <Datasheet.TableRow
                    name={`${key}.minimumBreakingCapacity`}
                    label="Minimum breaking capacity"
                    voltageType={voltageType}
                >
                    <CurrentField name={`${key}.minimumBreakingCapacity`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.minimumBreakingCapacity`} />
                </Datasheet.TableRow>
            )}
            {'maximumTimeConstant' in values && (
                <Datasheet.TableRow
                    name={`${key}.maximumTimeConstant`}
                    label="Maximum L/R time constant"
                    voltageType={voltageType}
                >
                    <TimeField name={`${key}.maximumTimeConstant`} fields={['value']} />
                    <AiAssistantProductComponent name={`${key}.maximumTimeConstant`} />
                </Datasheet.TableRow>
            )}
            {'configuration' in values && (
                <Datasheet.TableRow name={`${key}.configuration`} voltageType={voltageType} label="Port configuration">
                    <PortConfigurationField name={`${key}.configuration`} voltageType={voltageType} />
                    <AiAssistantProductComponent name={`${key}.configuration`} />
                </Datasheet.TableRow>
            )}
            {'earthingConfigurations' in values && (
                <Datasheet.TableRow
                    name={`${key}.earthingConfigurations`}
                    voltageType={voltageType}
                    label="Supported grounding configurations"
                >
                    <EarthingConfigurationsField name={`${key}.earthingConfigurations`} voltageType={voltageType} />
                    <AiAssistantProductComponent name={`${key}.earthingConfigurations`} />
                </Datasheet.TableRow>
            )}
            {'controlMethods' in values && (
                <Datasheet.TableRow name={`${key}.controlMethods`} label="Control Methods" voltageType={voltageType}>
                    <PortControlMethodsField name={`${key}.controlMethods`} />
                    <AiAssistantProductComponent name={`${key}.controlMethods`} />
                </Datasheet.TableRow>
            )}
        </Datasheet.RowSpan>
    );
};

export { ComponentDatasheetPortVoltageType };
