import React from 'react';

import { Datasheet } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { TextField } from 'components/forms/fields/TextField';

const General = () => {
    return (
        <ComponentDatasheetSection title="General">
            <Datasheet.Table header={{ label: 'General' }}>
                <Datasheet.TableRow name="name" label="Name">
                    <TextField name="name" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="teamManufacturer" label="Manufacturer">
                    <TextField name="teamManufacturer" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="productIdentifier" label="Part Number">
                    <TextField name="productIdentifier" />
                </Datasheet.TableRow>
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};

export { General };
