import React, { FC } from 'react';
import { Component } from 'models';

import { Flex, Group, Tooltip } from '@mantine/core';

import { CE, UL, CurrentOS, ODCA, EmergeAlliance } from 'components/icons';

import { useWatch } from 'react-hook-form';

const compliances: Record<
    string,
    {
        icon: React.ReactNode;
        tooltip?: React.ReactNode;
        compact?: boolean;
    }
> = {
    CE: {
        icon: <CE size={14} />,
        compact: true,
    },
    UL: {
        icon: <UL size={14} />,
        compact: true,
    },
    currentOS: {
        icon: <CurrentOS size={10} />,
        tooltip: 'We are a member of CurrentOS',
    },
    ODCA: {
        icon: <ODCA size={16} />,
        tooltip: 'We are a member of ODCA',
    },
    emergeAlliance: {
        icon: <EmergeAlliance size={16} />,
        tooltip: 'We are a member of EMerge Alliance',
    },
};

const Compliances: FC = () => {
    const compliance = useWatch({
        name: 'compliance',
    }) as Component['compliance'];

    if (Object.values(compliance).filter(Boolean).length === 0) {
        return null;
    }

    return (
        <Group align="stretch" gap={4}>
            {Object.entries(compliance)
                .filter(([, value]) => value)
                .map(([key]) => (
                    <Compliance tooltip={compliances[key]?.tooltip} compact={compliances[key]?.compact} key={key}>
                        {compliances[key]?.icon}
                    </Compliance>
                ))}
        </Group>
    );
};

const Compliance = ({
    children,
    tooltip,
    compact = false,
}: {
    children: React.ReactNode;
    tooltip?: React.ReactNode;
    compact?: boolean;
}) => {
    return (
        <Tooltip label={tooltip} disabled={!tooltip}>
            <Flex
                align="center"
                style={{
                    padding: compact ? '6px 7px' : '6px 12px',
                    backgroundColor: 'var(--mantine-color-primary-light)',
                    borderRadius: 99,
                    color: 'var(--mantine-color-primary-light-color)',
                }}
            >
                {children}
            </Flex>
        </Tooltip>
    );
};

export { Compliances };
