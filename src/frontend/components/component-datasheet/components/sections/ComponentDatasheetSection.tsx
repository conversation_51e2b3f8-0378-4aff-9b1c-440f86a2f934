import { FC } from 'react';
import { Datasheet } from 'components/datasheet';
import {
    useComponentSectionVisbility,
    ComponentSectionVisibility,
} from 'components/component-datasheet/hooks/use-component-section-visibility';

const ComponentDatasheetSection: FC<Parameters<typeof Datasheet.Section>[0] & { alwaysShow?: boolean }> = ({
    alwaysShow,
    ...props
}) => {
    const sectionVisibility = useComponentSectionVisbility(props.id);

    if (!alwaysShow && sectionVisibility === ComponentSectionVisibility.HIDDEN) {
        return null;
    }

    return <Datasheet.Section {...props} />;
};

export { ComponentDatasheetSection };
