import React from 'react';

import { ComponentSection } from 'models';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { Datasheet } from 'components/datasheet';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { AltitudeField } from 'components/component-fields/AltitudeField';
import { CoolingMethodField } from 'components/component-fields/CoolingMethodField';
import { HumidityField } from 'components/component-fields/HumidityField';
import { IngressProtectionField } from 'components/component-fields/IngressProtectionField';
import { TemperatureField } from 'components/component-fields/TemperatureField';
import { AiAssistantProductComponent } from '../AiAssistantProductComponent';
import { EnvironmentalCable } from 'components/component-datasheet/components/sections/Environmental.Cable';

import { useComponentContext } from '../../hooks/use-component-context';

export const Environmental = () => {
    const { component } = useComponentContext();

    if (component.type === 'cable') {
        return <EnvironmentalCable />;
    }

    return (
        <ComponentDatasheetSection
            id={ComponentSection.ENVIRONMENTAL}
            title="Environmental"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.ENVIRONMENTAL} />}
        >
            <Datasheet.Table header={{ label: 'Environmental' }}>
                <Datasheet.TableRow name="environmental.operatingTemperature" label="Operating Temperature">
                    <TemperatureField name="environmental.operatingTemperature" />
                    <AiAssistantProductComponent name={`environmental.operatingTemperature`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="environmental.storageTemperature" label="Storage Temperature">
                    <TemperatureField name="environmental.storageTemperature" />
                    <AiAssistantProductComponent name={`environmental.storageTemperature`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="environmental.operatingHumidity" label="Operating Humidity">
                    <HumidityField name="environmental.operatingHumidity" />
                    <AiAssistantProductComponent name={`environmental.operatingHumidity`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="environmental.storageHumidity" label="Storage Humidity">
                    <HumidityField name="environmental.storageHumidity" />
                    <AiAssistantProductComponent name={`environmental.storageHumidity`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="environmental.ingressProtection_IP" label="Ingress Protection (IP)">
                    <IngressProtectionField name="environmental.ingressProtection_IP" type="IP" />
                    <AiAssistantProductComponent name={`environmental.ingressProtection_IP`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="environmental.ingressProtection_NEMA" label="Ingress Protection (NEMA)">
                    <IngressProtectionField name="environmental.ingressProtection_NEMA" type="NEMA" />
                    <AiAssistantProductComponent name={`environmental.ingressProtection_NEMA`} />
                </Datasheet.TableRow>
                {'coolingMethod' in (component.environmental ?? {}) && (
                    <Datasheet.TableRow name="environmental.coolingMethod" label="Cooling Method">
                        <CoolingMethodField name="environmental.coolingMethod" />
                        <AiAssistantProductComponent name={`environmental.coolingMethod`} />
                    </Datasheet.TableRow>
                )}
                <Datasheet.TableRow name="environmental.maximumOperatingAltitude" label="Maximum Operating Altitude">
                    <AltitudeField name="environmental.maximumOperatingAltitude" />
                    <AiAssistantProductComponent name={`environmental.maximumOperatingAltitude`} />
                </Datasheet.TableRow>
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};
