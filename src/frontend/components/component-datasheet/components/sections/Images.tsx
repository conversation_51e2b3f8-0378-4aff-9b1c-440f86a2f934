import React, { FC } from 'react';

import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';
import { ComponentImagesField } from 'components/component-fields/ComponentImagesField';
import { useWatch } from 'react-hook-form';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { ComponentSection } from 'models';

const Images: FC = () => {
    const mode = useDatasheetMode();

    const editMode = mode !== DatasheetMode.VIEW;

    const [images = []] = useWatch({ name: ['images'] });
    const hasImages = !!images?.length;

    if (!hasImages && !editMode) return null;

    return (
        <ComponentDatasheetSection
            title="Images"
            id={ComponentSection.IMAGES}
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.IMAGES} />}
        >
            <ComponentImagesField columns={{ base: 2, sm: 4 }} editable={editMode} />
        </ComponentDatasheetSection>
    );
};

export { Images };
