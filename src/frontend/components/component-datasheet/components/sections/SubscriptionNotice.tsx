import React, { <PERSON> } from 'react';
import { Component } from 'models';

import { CompanySubscriptionNotice } from 'components/subscriptions/subscription-notice/CompanySubscriptionNotice';

import { useCompanyProfile } from 'hooks/use-company-profile';

const SubscriptionNotice: FC<{ component: Component }> = ({ component }) => {
    const { company } = useCompanyProfile(component.manufacturer || null);

    return company ? <CompanySubscriptionNotice company={company} /> : null;
};

export { SubscriptionNotice };
