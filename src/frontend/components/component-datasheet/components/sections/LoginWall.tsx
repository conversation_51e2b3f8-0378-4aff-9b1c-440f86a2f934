import { FC } from 'react';
import { Component } from 'models';

import { <PERSON>ge, <PERSON>ton, Card, CardProps, Stack, Text, Title } from '@mantine/core';

import { ModalService } from 'services/ModalService';

import cx from './LoginWall.module.scss';

const LoginWall: FC<
    CardProps & {
        component?: Component;
    }
> = ({ component, ...rest }) => {
    const showLoginModal = () => {
        ModalService.openLoginModal();
    };

    return (
        <Card shadow="xl" className={cx.card} {...rest}>
            <Stack align="flex-start" justify="center" p="xl">
                {component?.specificationsSummary && (
                    <>
                        <Badge variant="gradient" radius="xs">
                            AI Summary
                        </Badge>
                        <Text>{component.specificationsSummary}</Text>
                    </>
                )}

                <Title>Unlock Full Specifications</Title>
                <Text fz="lg" c="dimmed">
                    Sign up for a free account to access all specifications, contact manufacturers directly, leverage AI
                    -powered product searches, and more!
                </Text>

                <Button size="md" variant="gradient" onClick={showLoginModal} style={{ pointerEvents: 'all' }}>
                    Login
                </Button>
            </Stack>
        </Card>
    );
};

export { LoginWall };
