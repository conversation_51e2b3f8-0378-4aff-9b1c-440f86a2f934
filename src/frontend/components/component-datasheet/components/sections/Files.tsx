import React, { FC } from 'react';

import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { useWatch } from 'react-hook-form';
import { useComponentChat } from 'components/component-chat/hooks/useComponentChat';

import { ComponentFilesField } from 'components/component-fields/ComponentFilesField';
import { ComponentDatasheetSectionProgress } from 'components/component-datasheet/components/ComponentDatasheetSectionProgress';
import { ComponentSection } from 'models';
import { ComponentChatButton } from 'components/component-chat';

const Files: FC = () => {
    const mode = useDatasheetMode();
    const editMode = mode !== DatasheetMode.VIEW;
    const { toggleChatOpen } = useComponentChat();

    const [files = []] = useWatch({ name: ['files'] });
    const hasFiles = !!files?.length;

    if (!hasFiles && !editMode) return null;

    return (
        <ComponentDatasheetSection
            title="Files"
            id={ComponentSection.FILES}
            afterTitle={
                <>
                    <ComponentDatasheetSectionProgress section={ComponentSection.FILES} />
                    {hasFiles && (
                        <ComponentChatButton
                            toggleChatOpen={toggleChatOpen}
                            size="xs"
                            style={{
                                pointerEvents: 'all',
                            }}
                        />
                    )}
                </>
            }
        >
            <ComponentFilesField editable={editMode} />
        </ComponentDatasheetSection>
    );
};

export { Files };
