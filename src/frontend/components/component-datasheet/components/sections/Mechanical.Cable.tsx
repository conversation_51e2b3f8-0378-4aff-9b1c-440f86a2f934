import { ComponentSection } from 'models';

import { useComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { Datasheet } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';
import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';

import { InsulationMaterialField } from 'components/component-fields/InsulationMaterialField';
import { CableScreenField } from 'components/component-fields/CableScreenField';
import { ConductorMaterialField } from 'components/component-fields/ConductorMaterialField';
import { ConductorFlexibilityField } from 'components/component-fields/ConductorFlexibilityField';

export const MechanicalCable = () => {
    const { component } = useComponentContext();

    return (
        <ComponentDatasheetSection
            id={ComponentSection.MECHANICAL}
            title="Mechanical"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.MECHANICAL} />}
        >
            <Datasheet.Table header={{ label: 'Mechanical' }}>
                {[
                    {
                        label: 'Conductor Material',
                        name: 'conductorMaterial',
                        placeholder: 'Choose conductor material',
                        Component: ConductorMaterialField,
                    },
                    {
                        label: 'Conductor Flexibility',
                        name: 'conductorFlexibility',
                        placeholder: 'Choose conductor flexibility',
                        Component: ConductorFlexibilityField,
                    },
                    {
                        label: 'Insulation Material',
                        name: 'insulationMaterial',
                        placeholder: 'Choose insulation material',
                        Component: InsulationMaterialField,
                    },
                    {
                        label: 'Screen',
                        name: 'screen',
                        placeholder: 'Choose screen',
                        Component: CableScreenField,
                    },
                ]
                    .filter(({ name }) => name in component.mechanical)
                    .map((row) => (
                        <Datasheet.TableRow name={`mechanical.${row.name}`} label={row.label}>
                            <row.Component name={`mechanical.${row.name}`} placeholder={row.placeholder} />
                        </Datasheet.TableRow>
                    ))}
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};
