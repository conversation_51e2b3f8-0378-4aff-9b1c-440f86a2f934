import React, { useState } from 'react';

import { ComponentSection, PermissionComponent } from 'models';

import { Box, Button, Group, MantineProvider, Text } from '@mantine/core';
import { IoAdd } from 'react-icons/io5';

import { getId } from 'helpers/getId';

import { useReferenceDesignsByComponent } from 'hooks/use-reference-designs-by-component';
import { useComponentPermissions } from 'hooks/use-component-permissions';
import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ProjectFinder } from 'components/project-finder/ProjectFinder';
import { CarouselSection } from 'components/section/CarouselSection';
import { CheckboxButton } from 'components/checkbox-button/CheckboxButton';
import { DesignLibraryTeaser } from 'components/design-library-teaser/DesignLibraryTeaser';
import { ReferenceDesignButton } from 'components/reference-design-button/ReferenceDesignButton';

import { ProjectService } from 'services/ProjectService';

import { useComponentContext } from '../../hooks/use-component-context';

const ReferenceDesigns = () => {
    const [showCommunityDesigns, setShowCommunityDesigns] = useState(false);

    const { component } = useComponentContext();

    const mode = useDatasheetMode();

    const permissions = useComponentPermissions(component);
    const canEdit = permissions[PermissionComponent.EDIT];

    const { projects: referenceDesigns } = useReferenceDesignsByComponent(component.id, canEdit);

    if (!referenceDesigns.length && !canEdit) return null;

    if (mode !== DatasheetMode.VIEW) {
        return null;
    }

    const communityDesigns = referenceDesigns.filter(
        (project) => getId(project.template.profile) !== getId(component.manufacturer),
    );

    const shownReferenceDesigns = showCommunityDesigns
        ? referenceDesigns
        : referenceDesigns.filter((project) => getId(project.template.profile) === getId(component.manufacturer));

    return (
        // override DatasheetMantineProvider
        <MantineProvider
            theme={{
                components: {
                    Checkbox: {
                        defaultProps: {
                            disabled: false,
                        },
                    },
                    Button: {
                        styles: () => ({}),
                    },
                    Pill: {
                        styles: () => ({}),
                    },
                },
            }}
        >
            <ComponentDatasheetSection
                alwaysShow
                title="Reference Designs"
                id={ComponentSection.PROJECTS}
                afterTitle={
                    <Group gap="xs" style={{ flexGrow: 1 }}>
                        <ReferenceDesignButton
                            variant="transparent"
                            size="compact-xs"
                            component={component}
                            label={canEdit ? 'Create a Reference Design' : 'Start Designing'}
                        />
                        {canEdit && (
                            <ProjectFinder
                                onSelect={(project) => {
                                    ProjectService.navigate.editor(project.id, 'publish').then();
                                }}
                            >
                                {(open) => (
                                    <Button
                                        variant="transparent"
                                        size="compact-xs"
                                        onClick={open}
                                        leftSection={<IoAdd />}
                                    >
                                        Select an existing project
                                    </Button>
                                )}
                            </ProjectFinder>
                        )}
                        <Box style={{ marginLeft: 'auto', marginRight: 0 }}>
                            {!!communityDesigns.length && (
                                <CheckboxButton
                                    label="Show designs by our community"
                                    checked={showCommunityDesigns}
                                    setChecked={(checked) => setShowCommunityDesigns(checked)}
                                    style={{ alignSelf: 'flex-start' }}
                                />
                            )}
                        </Box>
                    </Group>
                }
            >
                {!!shownReferenceDesigns.length && (
                    <CarouselSection loop nbCols={2}>
                        {shownReferenceDesigns.map((project) => (
                            <DesignLibraryTeaser
                                isLight
                                showPublishedStatus={canEdit}
                                project={project}
                                key={project.id}
                            />
                        ))}
                    </CarouselSection>
                )}

                {!referenceDesigns.length && <Text c="dimmed">No reference designs yet.</Text>}

                {referenceDesigns.length && !shownReferenceDesigns.length ? (
                    <Text c="dimmed">No reference designs by the manufacturer yet.</Text>
                ) : null}
            </ComponentDatasheetSection>
        </MantineProvider>
    );
};

export { ReferenceDesigns };
