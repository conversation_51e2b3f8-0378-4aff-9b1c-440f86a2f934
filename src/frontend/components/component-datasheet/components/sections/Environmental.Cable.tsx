import React from 'react';

import { ComponentSection } from 'models';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { Datasheet } from 'components/datasheet';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { TemperatureField } from 'components/component-fields/TemperatureField';
import { CableUseField } from 'components/component-fields/CableUseField';

import { useComponentContext } from '../../hooks/use-component-context';

export const EnvironmentalCable = () => {
    const { component } = useComponentContext();

    return (
        <ComponentDatasheetSection
            id={ComponentSection.ENVIRONMENTAL}
            title="Environmental"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.ENVIRONMENTAL} />}
        >
            <Datasheet.Table header={{ label: 'Environmental' }}>
                {'installationTemperature' in component.environmental && (
                    <Datasheet.TableRow name="environmental.installationTemperature" label="Installation Temperature">
                        <TemperatureField name="environmental.installationTemperature" />
                    </Datasheet.TableRow>
                )}
                {'use' in component.environmental && (
                    <Datasheet.TableRow name="environmental.use" label="Use">
                        <CableUseField name="environmental.use" />
                    </Datasheet.TableRow>
                )}
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};
