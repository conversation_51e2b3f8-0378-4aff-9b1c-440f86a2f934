import { Datasheet } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { DimensionsField } from 'components/component-fields/DimensionsField';
import { WeightField } from 'components/component-fields/WeightField';
import { MountingTypeField } from 'components/component-fields/MountingTypeField';
import { AiAssistantProductComponent } from '../AiAssistantProductComponent';
import { ComponentSection } from 'models';
import { useComponentContext } from 'components/component-datasheet/hooks/use-component-context';
import { MechanicalCable } from 'components/component-datasheet/components/sections/Mechanical.Cable';

export const Mechanical = () => {
    const { component } = useComponentContext();

    if (component.type === 'cable') {
        return <MechanicalCable />;
    }

    return (
        <ComponentDatasheetSection
            id={ComponentSection.MECHANICAL}
            title="Mechanical"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.MECHANICAL} />}
        >
            <Datasheet.Table header={{ label: 'Mechanical' }}>
                <Datasheet.TableRow name="mechanical.dimensions" label="Dimensions">
                    <DimensionsField name="mechanical.dimensions" />
                    <AiAssistantProductComponent name={`mechanical.dimensions`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="mechanical.weight" label="Weight">
                    <WeightField name="mechanical.weight" />
                    <AiAssistantProductComponent name={`mechanical.weight`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="mechanical.mountingType" label="Mounting">
                    <MountingTypeField name="mechanical.mountingType" />
                    <AiAssistantProductComponent name={`mechanical.mountingType`} />
                </Datasheet.TableRow>
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};
