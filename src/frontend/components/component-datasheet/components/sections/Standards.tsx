import { Box } from '@mantine/core';

import { Datasheet, DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { StandardsField } from 'components/forms/fields/StandardsField';
import { AiAssistantProductComponent } from '../AiAssistantProductComponent';
import { ComponentSection } from 'models';

export const Standards = () => {
    const mode = useDatasheetMode();

    return (
        <ComponentDatasheetSection
            id={ComponentSection.STANDARDS}
            title="Standards"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.STANDARDS} />}
        >
            <Datasheet.Table
                header={{
                    label: 'Standards',
                }}
            >
                <Datasheet.TableRow name="standards" label="Standards">
                    <Box py={4} px={mode === DatasheetMode.VIEW ? 'xs' : 0}>
                        <StandardsField name="standards" readOnly={mode === DatasheetMode.VIEW} />
                    </Box>
                    <AiAssistantProductComponent name={`standards`} />
                </Datasheet.TableRow>
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};
