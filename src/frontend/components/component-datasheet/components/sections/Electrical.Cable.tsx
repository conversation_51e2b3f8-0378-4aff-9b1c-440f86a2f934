import { FC } from 'react';

import { Box, Flex } from '@mantine/core';

import { ComponentSection, getComponentDefinition } from 'models';

import { Datasheet } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';

import { useComponentContext } from '../../hooks/use-component-context';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import { useDatasheetElectrical } from 'components/component-datasheet/hooks/use-datasheet-electrical';
import { WireSizeField } from 'components/component-fields/WireSizeField';
import { ConnectionCoresField } from 'components/component-fields/ConnectionCores';
import { VoltageField } from 'components/component-fields/VoltageField';
import { NumberField } from 'components/forms/fields/NumberField';
import { CPRField } from 'components/component-fields/CPRField';
import { ResistancePerLengthField } from 'components/component-fields/ResistancePerLengthField';
import { InductancePerLengthField } from 'components/component-fields/InductancePerLengthField';
import { CheckboxField } from 'components/forms/fields/CheckboxField';
import { VoltageTypesField } from 'components/component-fields/VoltageTypesField';
import { IntegerField } from 'components/forms/fields/IntegerField';

const ElectricalCable: FC = () => {
    const { component } = useComponentContext();
    const electrical = useDatasheetElectrical();

    const componentDefinition = getComponentDefinition(component.type);

    if (!componentDefinition || !ComponentHelpers.componentTypeHasElectrical(component.type)) {
        return null;
    }

    return (
        <ComponentDatasheetSection
            id={ComponentSection.ELECTRICAL}
            title="Electrical"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.ELECTRICAL} />}
        >
            <Datasheet.Table header={{ prefix: ['Electrical'], label: 'General' }}>
                {'wireSize' in electrical && (
                    <Datasheet.TableRow label="Wire Size" name="electrical.wireSize">
                        <WireSizeField name="electrical.wireSize" />
                    </Datasheet.TableRow>
                )}

                {'cores' in electrical && (
                    <Datasheet.TableRow label="Cores" name="electrical.cores">
                        <ConnectionCoresField name="electrical.cores" />
                    </Datasheet.TableRow>
                )}

                {'numberOfConductors' in electrical && (
                    <Datasheet.TableRow label="Number of Conductors" name="electrical.numberOfConductors">
                        <IntegerField
                            name="electrical.numberOfConductors"
                            max={4}
                            min={1}
                            clampBehavior="strict"
                            placeholder="1, 2, 3, or 4?"
                        />
                    </Datasheet.TableRow>
                )}

                {'hasPE' in electrical && (
                    <Datasheet.TableRow label="Has PE" name="electrical.hasPE">
                        <Box px="xs">
                            <CheckboxField name="electrical.hasPE" label="Includes PE conductor" />
                        </Box>
                    </Datasheet.TableRow>
                )}

                {'voltageTypes' in electrical && (
                    <Datasheet.TableRow label="Voltage Types" name="electrical.voltageTypes">
                        <VoltageTypesField name="electrical.voltageTypes" />
                    </Datasheet.TableRow>
                )}

                {'operatingVoltage' in electrical && (
                    <Datasheet.TableRow label="Operating Voltage" name="electrical.operatingVoltage">
                        <Flex align="center">
                            <VoltageField
                                placeholder="To Earth"
                                name="electrical.operatingVoltage.toEarth"
                                fields={['value']}
                            />
                            /
                            <VoltageField
                                placeholder="Between Lines"
                                name="electrical.operatingVoltage.betweenLines"
                                fields={['value']}
                            />
                        </Flex>
                    </Datasheet.TableRow>
                )}

                {'intertrippingWireSize' in electrical && (
                    <Datasheet.TableRow label="Intertripping Wire Size" name="electrical.intertrippingWireSize">
                        <NumberField
                            name="electrical.intertrippingWireSize"
                            allowNegative={false}
                            rightSection="mm²"
                            pr="xs"
                        />
                    </Datasheet.TableRow>
                )}

                {'cpr' in electrical && (
                    <Datasheet.TableRow label="CPR" name="electrical.cpr">
                        <CPRField name="electrical.cpr" />
                    </Datasheet.TableRow>
                )}

                {'resistancePerLength' in electrical && (
                    <Datasheet.TableRow label="Resistance per length" name="electrical.resistancePerLength">
                        <ResistancePerLengthField name="electrical.resistancePerLength" />
                    </Datasheet.TableRow>
                )}

                {'inductancePerLength' in electrical && (
                    <Datasheet.TableRow label="Inductance per length" name="electrical.inductancePerLength">
                        <InductancePerLengthField name="electrical.inductancePerLength" />
                    </Datasheet.TableRow>
                )}
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};

export { ElectricalCable };
