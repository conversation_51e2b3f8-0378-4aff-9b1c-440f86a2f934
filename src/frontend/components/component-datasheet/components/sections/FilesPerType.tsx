import React, { FC, useState } from 'react';

import { Anchor, Group, Stack, Title } from '@mantine/core';
import { IoAddSharp } from 'react-icons/io5';

import { ComponentFileType, ComponentFileTypes, ComponentSection } from 'models';

import { ComponentDatasheetSection } from './ComponentDatasheetSection';
import { ComponentFilesOverview, ComponentFilesUploadButton } from 'components/component-fields/ComponentFilesField';
import { ComponentDatasheetSectionProgress } from 'components/component-datasheet/components/ComponentDatasheetSectionProgress';

const FilesPerType: FC<{ shownTypes?: ComponentFileType[] }> = ({ shownTypes }) => {
    const [showAll, setShowAll] = useState(!shownTypes);

    const shownOptions = showAll
        ? ComponentFileTypes.options
        : ComponentFileTypes.options.filter((option) => shownTypes?.includes(option.value));

    return (
        <ComponentDatasheetSection
            title="Files"
            id={ComponentSection.FILES}
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.FILES} />}
        >
            <Stack gap="lg">
                {shownOptions.map(({ label, value }) => (
                    <Stack gap={4} align="flex-start" key={value}>
                        <Group gap={8}>
                            <Title order={3}>{label}</Title>
                            <ComponentFilesUploadButton type="icon" fileType={value} size="xs" variant="outline">
                                <IoAddSharp size={12} />
                            </ComponentFilesUploadButton>
                        </Group>
                        <ComponentFilesOverview showEmpty editable fileType={value} />
                    </Stack>
                ))}
                {!showAll && <Anchor onClick={() => setShowAll(true)}>Show all file types</Anchor>}
            </Stack>
        </ComponentDatasheetSection>
    );
};

export { FilesPerType };
