import React from 'react';

import { Datasheet } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { CommunicationInterfaces, CommunicationProtocols, ComponentSection } from 'models';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { MultiSelectField } from 'components/forms/fields/MultiSelectField';
import { AiAssistantProductComponent } from '../AiAssistantProductComponent';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { useComponentContext } from 'components/component-datasheet/hooks/use-component-context';

const Communication = () => {
    const { component } = useComponentContext();

    if (!ComponentHelpers.componentTypeHasCommunication(component.type)) {
        return null;
    }

    return (
        <ComponentDatasheetSection
            id={ComponentSection.COMMUNICATION}
            title="Communication"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.COMMUNICATION} />}
        >
            <Datasheet.Table header={{ label: 'Communication' }}>
                <Datasheet.TableRow name="communication.protocols" label="Communication protocols">
                    <MultiSelectField name="communication.protocols" data={CommunicationProtocols.options} />
                    <AiAssistantProductComponent name={`communication.protocols`} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="communication.interfaces" label="Communication interfaces">
                    <MultiSelectField name="communication.interfaces" data={CommunicationInterfaces.options} />
                    <AiAssistantProductComponent name={`communication.interfaces`} />
                </Datasheet.TableRow>
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};

export { Communication };
