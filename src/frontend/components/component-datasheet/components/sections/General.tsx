import React from 'react';

import { useWatch } from 'react-hook-form';

import { Text } from '@mantine/core';

import { Datasheet, DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { TextField } from 'components/forms/fields/TextField';
import { NumberField } from 'components/forms/fields/NumberField';
import { MultilineTextField } from 'components/forms/fields/MultilineTextField';
import { ManufacturerField } from 'components/forms/fields/ManufacturerField';
import { DateField } from 'components/forms/fields/DateField';
import { URLField } from 'components/forms/fields/URLField';
import { CheckboxField } from 'components/forms/fields/CheckboxField';
import { RegionAvailabilityField } from 'components/component-fields/RegionAvailability';
import { Component, ComponentSection } from 'models';
import { PriceField } from 'components/forms/fields/PriceField';
import { ApplicationField } from 'components/component-fields/ApplicationField';
import { NumberHelpers } from 'helpers/NumberHelpers';
import { InAppSupportBadge } from 'components/badges/InAppSupportBadge';

import { DateService } from 'services/DateService';
import { useComponentContext } from 'components/component-datasheet/hooks/use-component-context';
import { useCompanyProfile } from 'hooks/use-company-profile';

const General = () => {
    const mode = useDatasheetMode();
    const { component } = useComponentContext();
    const { company: manufacturer } = useCompanyProfile(component.manufacturer);

    const compliance = useWatch({
        name: 'compliance',
    }) as Component['compliance'];

    if (mode === DatasheetMode.VIEW) {
        return (
            <ComponentDatasheetSection
                id={ComponentSection.GENERAL}
                title="Pricing and Availability"
                afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.GENERAL} />}
                alwaysShow
            >
                <Datasheet.Table header={{ label: 'General' }}>
                    <Datasheet.TableRow
                        name="msrp"
                        label="Avg. Price"
                        description="Price shown is an estimate only for budgeting purposes. Actual price will vary. Please contact manufacturer for exact pricing for your application."
                    >
                        <Datasheet.TableValue>
                            {component?.msrp
                                ? NumberHelpers.formatPrice(component?.msrp)
                                : manufacturer && (
                                      <InAppSupportBadge
                                          company={manufacturer}
                                          component={component}
                                          label={`Contact ${manufacturer.name}`}
                                          style={{ pointerEvents: 'all' }}
                                      />
                                  )}
                        </Datasheet.TableValue>
                    </Datasheet.TableRow>
                    <Datasheet.TableRow name="leadTime" label="Typ. Lead Time">
                        <Datasheet.TableValue>
                            {component?.leadTime ? (
                                DateService.formatNumberOfDays(component.leadTime)
                            ) : (
                                <Text inherit span c="dimmed">
                                    Not specified
                                </Text>
                            )}
                        </Datasheet.TableValue>
                    </Datasheet.TableRow>
                    <Datasheet.TableRow name="regionAvailability" label="Region Availability">
                        <RegionAvailabilityField name="regionAvailability" />
                    </Datasheet.TableRow>
                </Datasheet.Table>
            </ComponentDatasheetSection>
        );
    }

    return (
        <ComponentDatasheetSection
            id={ComponentSection.GENERAL}
            title="General"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.GENERAL} />}
            alwaysShow
        >
            <Datasheet.Table header={{ label: 'General' }}>
                <Datasheet.TableRow name="name" label="Name">
                    <TextField name="name" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="description" label="Description">
                    <MultilineTextField name="description" autosize minRows={1} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="manufacturer" label="Manufacturer">
                    <ManufacturerField name="manufacturer" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="productIdentifier" label="Part Number">
                    <TextField name="productIdentifier" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="productSeries" label="Product Series">
                    <TextField name="productSeries" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="msrp" label="Avg. Price">
                    <PriceField name="msrp" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="leadTime" label="Typ. Lead Time (in days)">
                    <NumberField name="leadTime" allowNegative={false} />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="lifecycle.release" label="Release Date">
                    <DateField name="lifecycle.release" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="lifecycle.endOfLife" label="End of Life Date">
                    <DateField name="lifecycle.endOfLife" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="website" label="Website">
                    <URLField name="website" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="regionAvailability" label="Region Availability">
                    <RegionAvailabilityField name="regionAvailability" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="application" label="Application">
                    <ApplicationField name="application" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="compliance.CE" label="CE Compliance">
                    <CheckboxField name="compliance.CE" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="compliance.UL" label="UL Compliance">
                    <CheckboxField name="compliance.UL" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="compliance.currentOS" label="CurrentOS">
                    <CheckboxField name="compliance.currentOS" label="We are a member of CurrentOS" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="compliance.emergeAlliance" label="EMerge Alliance">
                    <CheckboxField name="compliance.emergeAlliance" label="We are a member of EMerge Alliance" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="compliance.ODCA" label="ODCA">
                    <CheckboxField name="compliance.ODCA" label="We are a member of ODCA" />
                </Datasheet.TableRow>
                <Datasheet.TableRow name="compliance.other" label="Other">
                    <CheckboxField
                        name="compliance.other"
                        label="We are a member of another DC Microgrid Organization"
                    />
                </Datasheet.TableRow>

                {compliance?.other && (
                    <Datasheet.TableRow name="compliance.otherInput" label="Specify other">
                        <TextField name="compliance.otherInput" />
                    </Datasheet.TableRow>
                )}
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};

export { General };
