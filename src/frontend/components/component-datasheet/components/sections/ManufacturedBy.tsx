import React, { FC } from 'react';

import { Box, Stack } from '@mantine/core';

import { PublishedStatus } from 'models';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { useComponentContext } from '../../hooks/use-component-context';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { BoothNumber } from 'components/booth-number/BoothNumber';
import { CompanyButton } from 'components/company-button/CompanyButton';

const ManufacturedBy: FC = () => {
    const { component } = useComponentContext();
    const { company } = useCompanyProfile(component.manufacturer);

    if (!company) {
        return null;
    }

    const url = company.status === PublishedStatus.PUBLISHED ? CompanyProfileHelpers.urls.view(company.slug) : '';

    return (
        <Stack gap={4}>
            <BoothNumber company={company} size="xs" showEventName />
            <Box w={100} h={100} bg="white">
                <CompanyButton
                    tooltip={company.status === PublishedStatus.PUBLISHED ? 'View profile' : ''}
                    href={url}
                    company={company}
                />
            </Box>
        </Stack>
    );
};

export { ManufacturedBy };
