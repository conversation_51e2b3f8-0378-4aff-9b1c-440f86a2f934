import React, { FC, ReactNode } from 'react';

import { useWatch } from 'react-hook-form';

import { Box } from '@mantine/core';

import { Component, getComponentDefinition } from 'models';

import { Datasheet } from 'components/datasheet';

import { ComponentDatasheetPortVoltageType } from './ComponentDatasheetPortVoltageType.Team';
import { PortIsolatedField } from 'components/component-fields/PortIsolatedField';
import { PowerFlowField } from 'components/component-fields/PowerFlowField';
import { useDatasheetPort } from 'components/component-datasheet/hooks/use-datasheet-port';

const ComponentDatasheetPort: FC<{ index: number; label: ReactNode }> = ({ index, label }) => {
    const type = useWatch<Pick<Component, 'type'>>({
        name: 'type',
    });

    const definition = getComponentDefinition(type);
    const { values, showAC, showDC } = useDatasheetPort(index);

    return (
        <Datasheet.Table header={{ label }}>
            {showAC && <ComponentDatasheetPortVoltageType index={index} voltageType="AC" />}
            {showDC && <ComponentDatasheetPortVoltageType index={index} voltageType="DC" />}
            {'powerFlowDirection' in values && (
                <Datasheet.TableRow name={`electrical.ports.${index}.powerFlowDirection`} label="Power Flow">
                    <Box pl="xs">
                        <PowerFlowField
                            name={`electrical.ports.${index}.powerFlowDirection`}
                            componentDefinition={definition}
                        />
                    </Box>
                </Datasheet.TableRow>
            )}
            {'isolated' in values && (
                <Datasheet.TableRow name={`electrical.ports.${index}.isolated`} label="Isolated">
                    <Box pl="xs">
                        <PortIsolatedField name={`electrical.ports.${index}.isolated`} />
                    </Box>
                </Datasheet.TableRow>
            )}
        </Datasheet.Table>
    );
};

export { ComponentDatasheetPort };
