import { Component, ComponentSection, getFilterFunction, getSectionProgressValues, PermissionComponent } from 'models';

import { useWatch } from 'react-hook-form';

import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentSectionProgressRing } from 'components/component-progress/ComponentProgressRing';
import { useComponentPermissions } from 'hooks/use-component-permissions';

function ComponentDatasheetSectionProgress({ section }: { section: ComponentSection }) {
    const mode = useDatasheetMode();
    const component = useWatch() as Component;

    const permissions = useComponentPermissions(component);
    const canEdit = permissions[PermissionComponent.EDIT];

    if (mode === DatasheetMode.CREATE || canEdit) {
        return (
            <ComponentSectionProgressRing
                values={getSectionProgressValues(component, section)}
                filter={getFilterFunction(section)}
            />
        );
    }

    return null;
}

export { ComponentDatasheetSectionProgress };
