import { FC, useEffect } from 'react';
import { useRouter } from 'next/router';
import { LocalStorageService } from 'services/LocalStorageService';

export const SaveAccessTokenToLocalStorage: FC = () => {
    const router = useRouter();
    const { access } = router.query;

    useEffect(() => {
        if (access) {
            LocalStorageService.store('environmentLockAccess', access as string);
        }
    }, [access]);

    return null;
};
