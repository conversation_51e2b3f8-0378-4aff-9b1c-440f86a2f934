import React, { useEffect } from 'react';

import { AppShell, BoxProps, Flex } from '@mantine/core';

import TagManager from 'react-gtm-module';

import { useRouter } from 'next/router';

import { publicConfig } from '@public-config';

import { useSidebarNav } from 'hooks/use-sidebar-nav';
import { useCurrentUser } from 'hooks/use-current-user';

import { BreadcrumbProps, Breadcrumbs } from 'components/breadcrumbs/Breadcrumbs';
import { ScrollToTop } from 'components/scroll-to-top/ScrollToTop';
import { ErrorBoundary } from 'components/error-boundary/ErrorBoundary';
import { SIDEBAR_NAV_WIDTH, SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { MobileNav, MobileNavProps } from 'components/mobile-nav/MobileNav';

import { List } from './List';
import { Title } from './Title';
import { Footer } from './Footer';
import { Content, WideContent } from './Content';
import { Metadata } from './Metadata';
import { EditableTitle } from './EditableTitle';
import { LoadingOverlay } from './LoadingOverlay';
import { CenteredContent } from './CenteredContent';
import { FullScreenContent } from './FullScreenContent';
import { Hero, HeroComponentIcon } from './Hero';
import { InternalTrackingService } from 'services/InternalTrackingService';

export type PageProps = {
    title?: string;
    hideHeader?: boolean;
    breadcrumbs?: BreadcrumbProps & {
        type?: 'fullWidth' | 'floating' | 'floating.fullWidth';
        wrapperProps?: BoxProps;
    };
    mobileNavProps?: MobileNavProps;
    showBackground?: boolean;
    hideFooter?: boolean;
    hideSidebarNav?: boolean;
    hideBreadcrumbs?: boolean;
    hideLicenseAgreement?: boolean;
    showScrollToTop?: boolean;
    children: React.ReactNode;
};

export const Page = ({
    title,
    breadcrumbs: { wrapperProps, ...breadcrumbs } = {
        type: 'fullWidth',
    },
    mobileNavProps,
    hideFooter,
    showBackground,
    showScrollToTop,
    hideSidebarNav,
    hideBreadcrumbs,
    children,
}: PageProps) => {
    const sidebarNav = useSidebarNav();
    const user = useCurrentUser();
    const router = useRouter();

    const { projectId, designId, diagramId } = router.query;

    useEffect(() => {
        if (publicConfig.environment !== 'production') return;

        TagManager.dataLayer({
            dataLayer: {
                testUser: user?.adminPanel || user?.developer || user?.internal,
                userId: user?.id,
                project: projectId,
                design: designId,
                diagram: diagramId,
            },
        });
    }, [user, projectId, designId, diagramId]);

    return (
        <AppShell
            transitionDuration={300}
            navbar={{
                width: sidebarNav.isFloating ? 0 : SIDEBAR_NAV_WIDTH,
                breakpoint: 'md',
                collapsed: {
                    desktop: hideSidebarNav || !sidebarNav.isOpen,
                    mobile: hideSidebarNav || !sidebarNav.isMobileOpen,
                },
            }}
            id="app_shell"
        >
            <AppShell.Navbar>
                <SidebarNav />
            </AppShell.Navbar>
            <AppShell.Main>
                <Flex
                    direction="column"
                    style={(theme) => ({
                        minHeight: '100dvh',
                        backgroundColor: showBackground ? theme.colors.gray[0] : 'transparent',
                    })}
                >
                    {title && <Metadata title={title} />}
                    <LoadingOverlay />

                    {!hideBreadcrumbs &&
                        (breadcrumbs.type === 'floating' || breadcrumbs.type === 'floating.fullWidth' ? (
                            <Breadcrumbs.Floating
                                fullWidth={breadcrumbs.type === 'floating.fullWidth'}
                                title={title}
                                {...breadcrumbs}
                                {...wrapperProps}
                            />
                        ) : (
                            <Breadcrumbs title={title} {...breadcrumbs} {...wrapperProps} />
                        ))}

                    <ErrorBoundary onError={() => InternalTrackingService.track('error.boundary.page')}>
                        {children}
                    </ErrorBoundary>
                    {!hideFooter && <Page.Footer />}
                    {showScrollToTop && <ScrollToTop />}
                </Flex>

                <MobileNav {...mobileNavProps} />
            </AppShell.Main>
        </AppShell>
    );
};

Page.List = List;
Page.Content = Content;
Page.WideContent = WideContent;
Page.CenteredContent = CenteredContent;
Page.Footer = Footer;
Page.FullScreenContent = FullScreenContent;
Page.Title = Title;
Page.EditableTitle = EditableTitle;
Page.Hero = Hero;
Page.HeroComponentIcon = HeroComponentIcon;
