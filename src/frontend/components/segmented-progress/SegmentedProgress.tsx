import { FC } from 'react';
import { GroupProps, Flex, Group, Progress, Text, Tooltip } from '@mantine/core';

export const SegmentedProgress: FC<
    {
        value: number;
        segments?: number;
        max?: number;
        tooltip?: string;
        label?: React.ReactNode;
        rightSection?: React.ReactNode;
    } & GroupProps
> = ({
    value,
    segments = 4,
    max = 100,
    tooltip = 'This is a measure of completeness',
    label,
    rightSection,
    ...props
}) => {
    const unit = max / segments;
    const segmentActive = Math.round(value / unit);

    return (
        <Tooltip label={tooltip}>
            <Flex gap={'sm'} w="100%" align="center">
                {label && (
                    <Text size="sm" c="dimmed">
                        {label}
                    </Text>
                )}
                <Group grow gap={2} style={{ flexGrow: 1 }} {...props}>
                    {[...Array(segments)].map((_, index) => (
                        <Progress
                            key={index}
                            h={3}
                            value={0}
                            bg={index < segmentActive ? 'primary' : undefined}
                            radius="xs"
                        />
                    ))}
                </Group>
                {rightSection}
            </Flex>
        </Tooltip>
    );
};
