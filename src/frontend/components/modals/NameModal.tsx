import React from 'react';

import { Stack } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';

import { ModalTitle } from 'components/modals/ModalTitle';
import { Form } from 'components/forms/Form';
import { TextField } from 'components/forms/fields/TextField';
import { FormSubmit } from 'components/forms/FormSubmit';

type Props = {
    title: string;
    label: string;
    placeholder?: string;
    value: string;
    onChange: (value: string) => Promise<void>;
    submitLabel?: string;
};

type Values = {
    value: string;
};

const NameModal = ({ context, id, innerProps }: ContextModalProps<Props>) => {
    const { title, label, placeholder, value, onChange, submitLabel } = innerProps;

    const rename = async (values: Values) => {
        await onChange(values.value);
        context.closeModal(id);
    };

    return (
        <Stack gap="xs" p="sm">
            <ModalTitle>{title}</ModalTitle>
            <Form<Values> defaultValues={{ value }} onSubmit={rename}>
                <Stack gap="xs">
                    <TextField name="value" label={label} placeholder={placeholder} required />
                    <FormSubmit>{submitLabel ?? 'Rename'}</FormSubmit>
                </Stack>
            </Form>
        </Stack>
    );
};

export { NameModal };
