import React from 'react';
import { Group, Stack, Text } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';

import { MeasurementSystem, MeasurementSystemOptions, Project, WireSizingStandardOptions } from 'models';

import { MeasurementSystemService } from 'services/MeasurementSystemService';
import { ProjectService } from 'services/ProjectService';

import { useCurrentProject } from 'hooks/use-current-project';
import { useMeasurementSystem } from 'hooks/use-measurement-system';

import { Form } from 'components/forms/Form';
import { SelectField, SelectFieldProps } from 'components/forms/fields/SelectField';
import { FormSubmit } from 'components/forms/FormSubmit';

type FieldValues = {
    standard: Project['wireSizing']['standard'];
    voltageDrop: string;
    measurementSystem?: MeasurementSystem;
};

const WireSizeSettingsModal = ({ context }: ContextModalProps) => {
    const project = useCurrentProject();
    const measurementSystem = useMeasurementSystem();

    if (!project) {
        return null;
    }

    const defaultValues: FieldValues = {
        standard: project.wireSizing.standard,
        voltageDrop: project.wireSizing.voltageDrop?.toString(),
        measurementSystem,
    };

    const save = async (values: FieldValues) => {
        const { standard, voltageDrop, measurementSystem: units } = values;

        await ProjectService.update(project.id, {
            wireSizing: {
                ...project.wireSizing,
                standard,
                voltageDrop,
            },
        });

        if (units) {
            MeasurementSystemService.set(units);
        }

        context.closeAll();
    };

    return (
        <Form<FieldValues> defaultValues={defaultValues} onSubmit={save}>
            <Stack gap="md">
                <Group justify="space-between" align="center">
                    <Text size="sm" fw={500}>
                        Standard
                    </Text>
                    <StandardField />
                </Group>

                <Group justify="space-between" align="center">
                    <Text size="sm" fw={500}>
                        Fallback Voltage Drop
                    </Text>
                    <VoltageDropField />
                </Group>

                <Group justify="space-between" align="center">
                    <Text size="sm" fw={500}>
                        Measurement System
                    </Text>
                    <MeasurementSystemField />
                </Group>

                <FormSubmit disableIfClean>Save settings</FormSubmit>
            </Stack>
        </Form>
    );
};

const StandardField = (props: Partial<SelectFieldProps>) => {
    return <SelectField inline {...props} name="standard" data={WireSizingStandardOptions} />;
};

const VoltageDropField = (props: Partial<SelectFieldProps>) => {
    return (
        <SelectField
            inline
            {...props}
            name="voltageDrop"
            data={[
                { value: '0.01', label: '1%' },
                { value: '0.02', label: '2%' },
                { value: '0.03', label: '3%' },
                { value: '0.04', label: '4%' },
                { value: '0.05', label: '5%' },
            ]}
        />
    );
};

const MeasurementSystemField = (props: Partial<SelectFieldProps>) => {
    return <SelectField inline {...props} name="measurementSystem" data={MeasurementSystemOptions} />;
};

export { WireSizeSettingsModal };
