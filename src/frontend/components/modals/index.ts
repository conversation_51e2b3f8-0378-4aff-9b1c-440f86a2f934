import { AIAccessModal } from 'components/modals/AIAccessModal';
import { AIInspirationModal } from '@diagram/modals/AIInspirationModal';
import { CompanySuggestionsModal } from 'components/modals/CompanySuggestionsModal';
import { CreateChatChannel } from '@diagram/modals/CreateChatChannel';
import { CreateReusableComponent } from '@diagram/modals/CreateReusableComponent';
import { CreateTeamModal } from 'components/modals/CreateTeamModal';
import { DeviceTypeModal } from 'components/modals/DeviceTypeModal';
import { ExportDiagramModal } from '@diagram/modals/ExportDiagramModal';
import { FeatureLimitTrackerModal } from 'components/subscriptions/FeatureLimitTrackerModal';
import { ForceRefreshModal } from '@diagram/modals/ForceRefreshModal';
import { InfoModal } from 'components/modals/InfoModal';
import { InstallationMethodInfoModal } from 'components/component-fields/InstallationMethodInfoModal';
import { LoginModal } from 'components/modals/LoginModal';
import { PublishModal } from '@diagram/components/diagram-sidebar/sidebars/share-diagram/components/PublishModal';
import { NameModal } from 'components/modals/NameModal';
import { RequestAccessModal } from '@diagram/modals/RequestAccessModal';
import { RequestInAppSupportModal } from '@diagram/modals/RequestInAppSupportModal';
import { SimpleSubscriptionModal } from 'components/subscriptions/SimpleSubscriptionModal';
import { SwitchTeamModal as GenericSwitchTeamModal } from 'components/modals/SwitchTeamModal';
import { TeamSuggestionsModal } from 'components/modals/TeamSuggestionsModal';
import { TeamNameRequiredModal } from './TeamNameRequiredModal';
import { UpdateChatChannel } from '@diagram/modals/UpdateChatChannel';
import { ZoneDroopCurvesModal } from '@diagram/modals/ZoneDroopCurvesModal';
import { companyProfileModals } from 'components/company-profile/modals';
import { WireSizeSettingsModal } from 'components/modals/WireSizeSettingsModal';

const modals = {
    aiAccess: AIAccessModal,
    aiInspiration: AIInspirationModal,
    companySuggestions: CompanySuggestionsModal,
    createChatChannel: CreateChatChannel,
    createReusableComponent: CreateReusableComponent,
    createTeam: CreateTeamModal,
    deviceType: DeviceTypeModal,
    exportDiagram: ExportDiagramModal,
    featureLimitTracker: FeatureLimitTrackerModal,
    forceRefresh: ForceRefreshModal,
    genericSwitchTeam: GenericSwitchTeamModal,
    infoModal: InfoModal,
    installationMethodInfo: InstallationMethodInfoModal,
    login: LoginModal,
    publishDesign: PublishModal,
    name: NameModal,
    requestAccess: RequestAccessModal,
    requestInAppSupport: RequestInAppSupportModal,
    simpleSubscriptionModal: SimpleSubscriptionModal,
    teamNameRequired: TeamNameRequiredModal,
    teamSuggestions: TeamSuggestionsModal,
    updateChatChannel: UpdateChatChannel,
    zoneDroopCurves: ZoneDroopCurvesModal,
    ...companyProfileModals,
    wireSizeSettings: WireSizeSettingsModal,
};

export { modals };
