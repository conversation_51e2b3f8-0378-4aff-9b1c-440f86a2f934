import { ComponentVisibility } from 'models';

import { useProducts } from 'hooks/use-products';
import { useCurrentTeam } from 'hooks/use-current-team';

import { Page } from 'components/page';
import { ComponentOverviewHits } from 'components/component-overview';

const ManageTeamComponents = () => {
    const team = useCurrentTeam();

    const { products, isLoading } = useProducts(
        {
            team: team?.id,
            noManufacturer: true,
            visibility: ComponentVisibility.PRIVATE,
        },
        <PERSON><PERSON><PERSON>(team),
    );

    return (
        <Page showBackground title="Team components">
            <Page.WideContent>
                <ComponentOverviewHits hits={products} isLoading={isLoading} emptyMessage="No team components found." />
            </Page.WideContent>
        </Page>
    );
};

export { ManageTeamComponents };
