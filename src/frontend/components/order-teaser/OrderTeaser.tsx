import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { An<PERSON>, Badge, Box, Card, Flex, Group, Stack, Text } from '@mantine/core';
import { Tb<PERSON>encil, TbUser } from 'react-icons/tb';
import { IoListSharp } from 'react-icons/io5';

import { Order } from 'models';
import { useOrderMeta } from 'hooks/use-order-meta';
import { OrderStatusBadge } from 'components/order-status/OrderStatusBadge';
import { NumberHelpers } from 'helpers/NumberHelpers';

const OrderTeaser: FC<{
    order: Order;
    isActive?: boolean;
}> = ({ order, isActive }) => {
    const { url, totalComponentCount, uniqueComponentCount, createdAt, createdBy } = useOrderMeta(order);

    return (
        <Card
            p={0}
            style={() => ({
                display: 'flex',
                flexDirection: 'column',

                minHeight: 200,
                height: '100%',
            })}
        >
            <Link href={url} legacyBehavior>
                <Box
                    component="a"
                    style={(theme) => ({
                        flexGrow: 1,

                        display: 'block',
                        padding: theme.spacing.md,

                        cursor: 'pointer',
                    })}
                >
                    <Flex align="center" gap="xl">
                        <Text size="lg" fw={600}>
                            {order.name}
                        </Text>
                        {isActive && (
                            <Badge
                                color="primary"
                                variant="filled"
                                size="xs"
                                p="xs"
                                style={{
                                    flexGrow: 0,
                                    flexShrink: 0,
                                    marginLeft: 'auto',
                                    marginBottom: 'auto',
                                }}
                            >
                                Active
                            </Badge>
                        )}
                    </Flex>

                    <OrderMetaItem
                        label={`${totalComponentCount} items (${uniqueComponentCount} unique)`}
                        icon={<IoListSharp size={14} strokeWidth={1.5} />}
                    />

                    {createdBy && <OrderMetaItem label={createdBy} icon={<TbUser size={14} strokeWidth={1.5} />} />}

                    {createdAt && <OrderMetaItem label={createdAt} icon={<TbPencil size={14} strokeWidth={1.5} />} />}
                </Box>
            </Link>

            <Flex
                style={(theme) => ({
                    padding: theme.spacing.xs,
                })}
            >
                {order.status ? <OrderStatusBadge status={order.status} /> : <span />}
            </Flex>
        </Card>
    );
};

const OrderTeaserLight: FC<{
    order: Order;
}> = ({ order }) => {
    const { url, totalComponentCount, createdAt } = useOrderMeta(order);

    const meta = [`${totalComponentCount} products`, `${createdAt}`];

    if (order.totalPrice) {
        meta.push(`Total: ${NumberHelpers.formatPrice(order.totalPrice)}`);
    }

    return (
        <Stack gap={8}>
            <Anchor component={Link} href={url} fw={600}>
                {order.name}
            </Anchor>

            <OrderMetaItem label={meta.join(' • ')} icon={<OrderStatusBadge status={order.status} />} />
        </Stack>
    );
};

const OrderMetaItem = ({ icon, label }: { icon?: React.ReactNode; label: string }) => {
    return (
        <Group
            gap={4}
            align="center"
            style={(theme) => ({
                color: theme.colors.gray[6],
                fontSize: 12,
                fontWeight: 500,
            })}
        >
            {icon}
            {label}
        </Group>
    );
};

export { OrderTeaser, OrderTeaserLight };
