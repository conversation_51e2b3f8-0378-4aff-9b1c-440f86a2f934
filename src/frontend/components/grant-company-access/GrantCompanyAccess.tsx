import React, { useEffect } from 'react';

import { useRouter } from 'next/router';

import { <PERSON><PERSON>, Card, SimpleGrid } from '@mantine/core';

import { CompanyProfile } from 'models';

import { getId } from 'helpers/getId';

import { useUser } from 'hooks/use-user';
import { useTeam } from 'hooks/use-team';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { UserService } from 'services/UserService';

import { Page } from 'components/page';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';
import { GrantCompanyAccessChoose } from 'components/grant-company-access/GrantCompanyAccess.Choose';

import cx from 'components/grant-access/GrantAccess.module.scss';

const GrantCompanyAccess = ({ company }: { company: CompanyProfile }) => {
    const router = useRouter();

    const { userId, teamId, userTeamId } = router.query as {
        userId: string;
        teamId: string;
        userTeamId: string;
    };

    const currentUser = useCurrentUser();
    const currentTeam = useCurrentTeam();

    const { user: requestUser } = useUser(userId);
    const { team: requestTeam } = useTeam(userTeamId);

    // switch current team to the team of the company
    useEffect(() => {
        if (!currentUser) return;
        if (!teamId) return;
        if (teamId === currentTeam?.id) return;

        UserService.switchTeam(teamId).then((result) => {
            if (result?.success) {
                router.reload();
            }
        });
    }, [currentUser, currentTeam, router, teamId]);

    if (!requestUser || !requestTeam || !currentTeam) {
        return null;
    }

    const noAccessToTeam = (currentTeam && teamId !== currentTeam?.id) || getId(company.team) !== currentTeam?.id;

    const userIsAlreadyOnTeam =
        teamId === currentTeam.id && currentTeam.users.find((user) => getId(user.user) === userId);

    return (
        <Page showBackground title="Grant access">
            <Page.CenteredContent maxWidth={900}>
                <SimpleGrid
                    pos="relative"
                    cols={{
                        base: 1,
                        md: 2,
                    }}
                    spacing={8}
                    w="100%"
                >
                    <CompanyProfileTeaser company={company} />

                    <Card withBorder className={cx.root}>
                        {userIsAlreadyOnTeam && (
                            <Alert color="green">{requestUser.email} has been invited to your team.</Alert>
                        )}

                        {noAccessToTeam && (
                            <Alert color="red">You do not have access to this profile&apos;s team.</Alert>
                        )}

                        {!userIsAlreadyOnTeam && !noAccessToTeam && (
                            <GrantCompanyAccessChoose
                                company={company}
                                requestUser={requestUser}
                                requestTeam={requestTeam}
                            />
                        )}
                    </Card>
                </SimpleGrid>
            </Page.CenteredContent>
        </Page>
    );
};

export { GrantCompanyAccess };
