import React, { useState } from 'react';

import { z } from 'zod';

import { <PERSON><PERSON>, Stack, Title } from '@mantine/core';

import { CompanyProfile, Team, User } from 'models';

import { useCurrentTeam } from 'hooks/use-current-team';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { MultilineTextField } from 'components/forms/fields/MultilineTextField';

import { GrantAccessGoBack } from 'components/grant-access/GoBack';

const GrantCompanyAccessDenyAccess = ({
    company,
    goBack,
    requestUser,
    requestTeam,
}: {
    company: CompanyProfile;
    goBack: () => void;
    requestUser: User;
    requestTeam: Team;
}) => {
    const [message, setMessage] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);

    const currentTeam = useCurrentTeam();

    if (!currentTeam) {
        return null;
    }

    const denyAccess = async (message: string) => {
        try {
            const result: {
                success: boolean;
                components: number;
                designs: number;
            } = await CompanyProfileService.denyAccess(company.id, {
                message,
                userId: requestUser.id,
                userTeamId: requestTeam.id,
            });

            if (result.success) {
                setMessage('You have successfully denied access to the user. Feel free to close this window.');
            }
        } catch {
            setError('An error occurred while denying access. Please try again.');
        }
    };

    return (
        <Form<{ message: string }>
            zodSchema={z.object({ message: z.string() })}
            onSubmit={async (values) => {
                await denyAccess(values.message);
            }}
        >
            <Stack>
                <GrantAccessGoBack goBack={goBack} />

                <Title size="h2">Deny access to {requestUser.email ?? 'user'}</Title>

                <MultilineTextField
                    name="message"
                    label="Reason"
                    rows={7}
                    placeholder="Please enter a reason for denying access."
                    required
                    disabled={!!message}
                />

                <FormSubmit color="red" disabled={!!message}>
                    Confirm Deny Access
                </FormSubmit>

                {message && <Alert color="green">{message}</Alert>}

                {error && <Alert color="red">{error}</Alert>}
            </Stack>
        </Form>
    );
};

export { GrantCompanyAccessDenyAccess };
