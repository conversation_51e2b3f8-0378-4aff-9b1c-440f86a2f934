import React, { useState } from 'react';

import { Alert, Group, List, Stack, Text, Title } from '@mantine/core';

import { CompanyProfile, User } from 'models';

import { useCurrentTeam } from 'hooks/use-current-team';

import { TeamUsersInvite } from 'components/team/components/TeamUsersInvite';
import { TeamAndUsersButton } from 'components/team-users/TeamAndUsersButton';
import { GrantAccessGoBack } from 'components/grant-access/GoBack';

const GrantCompanyAccessInviteUser = ({
    company,
    goBack,
    requestUser,
}: {
    company: CompanyProfile;
    goBack: () => void;
    requestUser: User;
}) => {
    const [error, setError] = useState<string | null>(null);

    const currentTeam = useCurrentTeam();

    if (!currentTeam) {
        return null;
    }

    return (
        <Stack>
            <GrantAccessGoBack goBack={goBack} />
            <Title size="h2">
                <span>
                    Invite <u>{requestUser.email ?? 'user'}</u>
                </span>
                <Group component="span" gap={4} mt={4}>
                    to <TeamAndUsersButton size="compact-sm" team={currentTeam} />
                </Group>
            </Title>
            <Text>The user will be able to:</Text>

            <List>
                <List.Item>Add new and edit existing products</List.Item>
                <List.Item>Add new and edit existing design templates</List.Item>
                <List.Item>Edit company profile</List.Item>
                <List.Item>See team info and members</List.Item>
            </List>

            <TeamUsersInvite
                disableEmail
                email={requestUser.email}
                onError={(error: string) => setError(error)}
                extraSubmitData={{
                    companyId: company.id,
                }}
            />

            {error && <Alert color="red">{error}</Alert>}
        </Stack>
    );
};

export { GrantCompanyAccessInviteUser };
