import React, { FC } from 'react';

import { <PERSON>chor, Button, Card, Grid, Group, Kbd, Stack, Text } from '@mantine/core';
import {
    IoBriefcaseOutline,
    IoCalendarOutline,
    IoChatbubblesOutline,
    IoPersonOutline,
    IoShapesOutline,
} from 'react-icons/io5';

import { DiagramChatChannel, DiagramChatMessage } from 'models';

import { useUser } from 'hooks/use-user';
import { useProject } from 'hooks/use-project';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useChatMessages } from '@diagram/hooks/use-chat-messages';

import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { DateService } from 'services/DateService';

import { IconWithText } from 'components/IconWithText';
import { TipTapViewer } from 'components/tiptap/TipTapViewer';

const DiagramChatChannelItem = ({ channel, openChannel }: { channel: DiagramChatChannel; openChannel: () => void }) => {
    const { messages } = useChatMessages(channel.id);

    const { project } = useProject(channel.project);

    const { user } = useUser(channel.createdBy);
    const { company } = useCompanyProfile(channel.access.company);

    return (
        <Card>
            <Grid gutter="xl" w="100%" columns={10}>
                <Grid.Col span={2}>
                    <Stack gap="md" align="start">
                        <Stack gap={0}>
                            <Kbd>
                                {channel.name} ({channel.type})
                            </Kbd>

                            <IconWithText icon={<IoCalendarOutline />} text={DateService.format(channel.createdAt)} />

                            <IconWithText icon={<IoPersonOutline />} text={`${user?.name} (${user?.email})`} />

                            {project && (
                                <IconWithText
                                    icon={<IoShapesOutline />}
                                    text={
                                        <Anchor href={ProjectHelpers.urls.editor(project.id)} fw={600} target="_blank">
                                            {project.name}
                                        </Anchor>
                                    }
                                />
                            )}

                            {company && (
                                <IconWithText
                                    icon={<IoBriefcaseOutline />}
                                    text={
                                        <Anchor
                                            href={CompanyProfileHelpers.urls.view(company.slug)}
                                            fw={600}
                                            target="_blank"
                                        >
                                            {company.name}
                                        </Anchor>
                                    }
                                />
                            )}

                            <IconWithText icon={<IoChatbubblesOutline />} text={`${messages.length} message(s)`} />
                        </Stack>
                    </Stack>
                </Grid.Col>

                <Grid.Col span={6}>
                    <DiagramChatChannelMessages channel={channel} />

                    <Button size="xs" variant="outline" onClick={openChannel} mt="md">
                        View full conversation
                    </Button>
                </Grid.Col>
            </Grid>
        </Card>
    );
};

const DiagramChatChannelMessages: FC<{ channel: DiagramChatChannel }> = ({ channel }) => {
    const { messages } = useChatMessages(channel.id);

    const latest3Messages = messages.slice(-3);
    const hasMoreMessages = messages.length > 3;

    return (
        <Stack gap="md">
            {hasMoreMessages && (
                <Text c="dimmed" fz="xs">
                    +{messages.length - 3} older messages
                </Text>
            )}

            {latest3Messages.map((message) => (
                <DiagramChatChannelMessage key={message.id} message={message} />
            ))}
        </Stack>
    );
};

const DiagramChatChannelMessage: FC<{ message: DiagramChatMessage }> = ({ message }) => {
    const { user } = useUser(message.createdBy);
    const { company } = useCompanyProfile(message.createdByCompany);

    return (
        <Stack gap={4}>
            <Group gap="xs" fw={600}>
                {company?.name || user?.name}
                <Text span c="dimmed" fz="xs">
                    {DateService.format(message.createdAt)}
                </Text>
            </Group>

            <TipTapViewer content={JSON.parse(message.content[0].content)} files={message.content[0].files} />
        </Stack>
    );
};

export { DiagramChatChannelItem };
