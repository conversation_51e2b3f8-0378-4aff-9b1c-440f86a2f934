import React, { FC } from 'react';

import { Badge, Card, Group, Modal, Stack } from '@mantine/core';

import { DiagramChatChannel, DiagramChatMessage } from 'models';

import { useUser } from 'hooks/use-user';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useChatMessages } from '@diagram/hooks/use-chat-messages';

import { DateService } from 'services/DateService';

import { TipTapViewer } from 'components/tiptap/TipTapViewer';

const DiagramChatChannelModal: FC<{ channel: DiagramChatChannel; closeChannel: () => void }> = ({
    channel,
    closeChannel,
}) => {
    const { messages } = useChatMessages(channel.id);

    return (
        <Modal opened onClose={closeChannel} size="xl" withCloseButton={false}>
            <Stack gap="md">
                {messages.map((message) => (
                    <DiagramChatChannelMessage key={message.id} message={message} />
                ))}
            </Stack>
        </Modal>
    );
};

const DiagramChatChannelMessage: FC<{ message: DiagramChatMessage }> = ({ message }) => {
    const { user } = useUser(message.createdBy);
    const { company } = useCompanyProfile(message.createdByCompany);

    const renderName = () => {
        if (company) {
            return (
                <>
                    <Badge>{`${company.name}`}</Badge> {user?.name} ∙ {user?.email}
                </>
            );
        } else if (user) {
            return (
                <>
                    <Badge variant="outline">{`${user.name}`}</Badge> {user?.email}
                </>
            );
        }
    };

    return (
        <Card withBorder bg="gray.0" radius="xs">
            <Group gap={6} fz="xs" c="dimmed" mb="xs">
                {renderName()} ∙ {DateService.format(message.createdAt)}
            </Group>
            <TipTapViewer content={JSON.parse(message.content[0].content)} files={message.content[0].files} />
        </Card>
    );
};

export { DiagramChatChannelModal };
