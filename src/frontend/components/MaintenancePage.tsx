import { Box } from '@mantine/core';
import { ReactNode } from 'react';
import { Page } from './page';
import { useGeneralGlobals } from 'hooks/use-general-globals';

const MaintenancePageWrapper = ({ children }: { children: ReactNode }) => {
    const { data: { maintenanceMessage } = { maintenanceMessage: undefined } } = useGeneralGlobals();

    if (maintenanceMessage) {
        return (
            <Page showBackground hideHeader>
                <Page.CenteredContent title={maintenanceMessage} subtitle="We apologize for the inconvenience.">
                    <Box
                        component="img"
                        width="256"
                        height="256"
                        src="https://img.icons8.com/emoji/256/construction-emoji.png"
                        alt="construction-emoji"
                        style={{
                            width: 128,
                            height: 'auto',
                        }}
                    />
                </Page.CenteredContent>
            </Page>
        );
    }

    return <>{children}</>;
};

export { MaintenancePageWrapper };
