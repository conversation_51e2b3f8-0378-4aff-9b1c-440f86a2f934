import { FC, useState } from 'react';

import { Flex, NumberInput, Select } from '@mantine/core';

const TimeIntervals = ['Days', 'Weeks', 'Months'] as const;

type TimeInterval = (typeof TimeIntervals)[number];

const TimeIntervalMultipliers = {
    Days: 1,
    Weeks: 7,
    Months: 30,
} as const;

type LeadTimeFieldProps = {
    leadTime?: number;
    onChange: (newLeadTime: number | '') => void;
};
const LeadTimeField: FC<LeadTimeFieldProps> = ({ leadTime, onChange }) => {
    const [timeInterval, setTimeInterval] = useState(getDefaultTimeInterval(leadTime));
    const [previousTimeInterval, setPreviousTimeInterval] = useState(timeInterval);

    if (timeInterval !== previousTimeInterval) {
        if (leadTime) {
            onChange(
                Math.round(
                    (leadTime * TimeIntervalMultipliers[previousTimeInterval]) / TimeIntervalMultipliers[timeInterval],
                ),
            );
        }

        setPreviousTimeInterval(timeInterval);
        return null;
    }

    return (
        <Flex gap={4}>
            <NumberInput
                hideControls
                min={0}
                step={1}
                placeholder="0"
                value={leadTime}
                onChange={(value) => {
                    if (value !== '') {
                        onChange(+value * TimeIntervalMultipliers[timeInterval]);
                    } else {
                        onChange(value);
                    }
                }}
                size="xs"
                styles={{
                    input: {
                        width: 40,
                    },
                }}
            />
            <Select
                data={TimeIntervals as any}
                value={timeInterval}
                onChange={(value) => {
                    if (!value) return;

                    setTimeInterval(value as TimeInterval);
                }}
                size="xs"
            />
        </Flex>
    );
};

const getDefaultTimeInterval = (leadTime?: number): TimeInterval => {
    if (!leadTime) return 'Days';

    return TimeIntervals.find((interval) => leadTime / TimeIntervalMultipliers[interval] > 1) ?? 'Days';
};

export { LeadTimeField };
