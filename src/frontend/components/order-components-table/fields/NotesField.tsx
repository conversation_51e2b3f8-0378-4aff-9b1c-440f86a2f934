import { TextInput } from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import { FC, useEffect, useState } from 'react';

type NotesFieldProps = {
    notes?: string;
    onChange: (newNote: string) => void;
};
const NotesField: FC<NotesFieldProps> = ({ notes: incomingNote, onChange }) => {
    const [notes, setNotes] = useState(incomingNote);
    const [debounced] = useDebouncedValue(notes, 1000);

    useEffect(() => {
        if (debounced && debounced !== incomingNote) {
            onChange(debounced);
        }
    }, [debounced, incomingNote, onChange]);

    return (
        <TextInput
            label="Notes"
            value={notes ?? ''}
            onChange={(event) => {
                setNotes(event.currentTarget.value);
            }}
            size="xs"
            styles={(theme) => ({
                label: {
                    fontSize: theme.fontSizes.sm,
                    fontWeight: 600,
                },
            })}
        />
    );
};

export { NotesField };
