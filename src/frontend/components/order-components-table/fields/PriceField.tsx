import { NumberInput } from '@mantine/core';
import { TbCurrencyDollar } from 'react-icons/tb';
import { FC } from 'react';

type PriceFieldProps = {
    price?: number;
    onChange: (newPrice: number | '') => void;
};
const PriceField: FC<PriceFieldProps> = ({ price, onChange }) => {
    return (
        <NumberInput
            min={0}
            step={0.01}
            placeholder="0"
            leftSection={<TbCurrencyDollar size={14} />}
            value={price ?? ''}
            onChange={(value) => {
                onChange(value === '' ? '' : +value);
            }}
            decimalScale={2}
            hideControls
            size="xs"
            styles={{
                input: {
                    width: 80,
                },
            }}
        />
    );
};

export { PriceField };
