import { FC } from 'react';

import { Select } from '@mantine/core';

import { OrderAvailability, OrderAvailabilityOptions } from 'models';

type AvailabilityFieldProps = {
    availability?: OrderAvailability;
    onChange: (newAvailability: OrderAvailability | null) => void;
};
const AvailabilityField: FC<AvailabilityFieldProps> = ({ availability, onChange }) => {
    return (
        <Select
            placeholder="Availability"
            data={OrderAvailabilityOptions as any}
            value={availability ?? null}
            onChange={(value) => {
                onChange(value as OrderAvailability);
            }}
            size="xs"
        ></Select>
    );
};

export { AvailabilityField };
