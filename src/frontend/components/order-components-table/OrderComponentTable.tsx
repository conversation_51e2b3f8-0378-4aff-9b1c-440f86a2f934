import { FC, useEffect } from 'react';

import Link from 'next/link';

import { Box, Flex, Group, Loader, Stack, Table, Text, Title } from '@mantine/core';
import { TbPencil } from 'react-icons/tb';

import { OrderController, useComponentMeta } from 'hooks';
import { Component } from 'models';
import { Order, OrderAvailability, OrderAvailabilityOptions, OrderStatus } from 'models';
import { ComponentThumbnail } from '../thumbnail';

import { AvailabilityField, LeadTimeField, PriceField, QuantityField } from './fields';
import { NotesField } from './fields/NotesField';

import { RouterHelpers } from 'helpers/RouterHelpers';

const statusesReviewerCanEdit = ['quoteRequested', 'orderSubmitted', 'orderAcknowledged', 'orderInProgress'];

const OrderComponentTable: FC<{ orderController: OrderController }> = ({ orderController }) => {
    useEffect(() => {
        if (typeof orderController.order.components?.[0]?.component === 'string') {
            orderController.mutate();
        }
    }, [orderController, orderController.order.components]);

    if (orderController.isEmpty) {
        return (
            <Text>
                This quote is empty. Go to <Link href={RouterHelpers.urls.search()}>the product catalog</Link> to add
                some.
            </Text>
        );
    }

    const status = orderController.order.status;

    const RowsElement = getOrderComponentRowElement(status);

    if (typeof orderController.order.components?.[0].component === 'string') return <Loader size="sm" />;

    return (
        <Table verticalSpacing="xs" horizontalSpacing={8} withRowBorders={false}>
            <thead>
                <Table.Tr>
                    <Table.Th>Product</Table.Th>
                    <Table.Th>Qty</Table.Th>
                    {status !== 'draft' && (
                        <>
                            <Table.Th>Unit price</Table.Th>
                            <Table.Th>Lead Time</Table.Th>
                            <Table.Th>Availability</Table.Th>
                        </>
                    )}
                </Table.Tr>
            </thead>
            <Table.Tbody>
                {orderController.order.components?.map((orderComponent, index) => (
                    <RowsElement
                        key={(orderComponent.component as Component).id}
                        index={index}
                        component={orderComponent.component as Component}
                        data={orderComponent}
                        controller={orderController}
                    />
                ))}
                {!!orderController.order.totalPrice && (
                    <Table.Tr style={{ borderTop: '1px solid var(--mantine-color-gray-2)' }}>
                        <Table.Td colSpan={2}>
                            <Text fw={700}>Total price</Text>
                        </Table.Td>
                        <Table.Td colSpan={3}>
                            <Text fw={700}>${orderController.order.totalPrice}</Text>
                        </Table.Td>
                    </Table.Tr>
                )}
            </Table.Tbody>
        </Table>
    );
};

type OrderComponentRowProps = {
    index: number;
    component: Component;
    data: Required<Order>['components'][number];
    controller: OrderController;
};

const getOrderComponentRowElement = (status: OrderStatus): FC<OrderComponentRowProps> => {
    switch (status) {
        case 'draft':
            return BasicOrderComponentRow;

        case 'quoteRequested':
            return ReviewFieldsOrderComponentRow;

        default:
            return ReviewFieldsOrderComponentRow;
    }
};

const BasicOrderComponentRow: FC<OrderComponentRowProps> = ({ index, component, data: { quantity }, controller }) => {
    const editable = controller.order.status === 'draft' && controller.order.permissions?.includes('order.all');

    const removeComponentFromOrder = async () => {
        await controller.removeComponent(component.id);
    };

    const onQuantityChange = async (newQuantity: number) => {
        await controller.updateComponent(component.id, { quantity: newQuantity });
    };

    const Quantity = editable ? (
        <QuantityField quantity={quantity} onChange={onQuantityChange} onRemove={removeComponentFromOrder} />
    ) : (
        quantity
    );

    return (
        <Table.Tr
            style={{
                borderTop: '1px solid var(--mantine-color-gray-2)',
                backgroundColor: index % 2 === 0 ? 'var(--mantine-color-gray-0)' : 'transparent',
            }}
        >
            <Table.Td width="100%">
                <OrderComponentInfo component={component} />
            </Table.Td>
            <Table.Td>{Quantity}</Table.Td>
        </Table.Tr>
    );
};

const OrderComponentInfo: FC<{ component: Component }> = ({ component }) => {
    const { uniqueName, subtitle } = useComponentMeta(component);

    return (
        <Flex gap="md" align="center">
            <Box style={{ flexShrink: 0 }}>
                <ComponentThumbnail component={component} style={{ width: 60, minHeight: 'auto' }} />
            </Box>
            <Stack gap={4}>
                <Title order={3} fw={600}>
                    {uniqueName}
                </Title>
                <Text c="gray.5" fz="xs" fw={600} lh={1.2}>
                    {subtitle}
                </Text>
            </Stack>
        </Flex>
    );
};

const ReviewFieldsOrderComponentRow: FC<OrderComponentRowProps> = ({
    index,
    component,
    data: { quantity, price, leadTime, availability, notes },
    controller,
}) => {
    const editable =
        statusesReviewerCanEdit.includes(controller.order.status) &&
        !!controller.order.permissions?.includes('order.review');

    const updatePrice = async (price: number | '') => {
        if (price === '') return;

        await controller.updateComponent(component.id, { price });
    };

    const updateLeadTime = async (leadTime: number | '') => {
        if (leadTime === '') return;

        await controller.updateComponent(component.id, { leadTime });
    };

    const updateAvailability = async (availability: OrderAvailability | null) => {
        if (!availability) return;

        await controller.updateComponent(component.id, { availability });
    };

    const updateNotes = async (notes: string) => {
        await controller.updateComponent(component.id, { notes });
    };

    const Price = editable ? <PriceField price={price} onChange={updatePrice} /> : price && `$${price}`;
    const LeadTime = editable ? (
        <LeadTimeField leadTime={leadTime} onChange={updateLeadTime} />
    ) : (
        leadTime && `${leadTime} days`
    );
    const Availability = editable ? (
        <AvailabilityField availability={availability} onChange={updateAvailability} />
    ) : (
        OrderAvailabilityOptions.find(({ value }) => value === availability)?.label
    );
    const Notes = editable ? (
        <NotesField notes={notes} onChange={updateNotes} />
    ) : (
        <>
            {notes && (
                <Group
                    align="center"
                    gap={4}
                    style={(theme) => ({
                        padding: theme.spacing.xs,
                        borderRadius: theme.radius.xs,
                    })}
                >
                    <TbPencil size={12} strokeWidth={1.5} />
                    <Text span fw={600}>
                        Note from reviewer:
                    </Text>
                    <Text>{notes}</Text>
                </Group>
            )}
        </>
    );

    return (
        <>
            <Table.Tr
                style={{
                    borderTop: '1px solid var(--mantine-color-gray-2)',
                    backgroundColor: index % 2 === 0 ? 'var(--mantine-color-gray-0)' : 'transparent',
                }}
            >
                <Table.Td>
                    <OrderComponentInfo component={component} />
                </Table.Td>
                <Table.Td>{quantity}</Table.Td>
                <Table.Td>{Price}</Table.Td>
                <Table.Td>{LeadTime}</Table.Td>
                <Table.Td>{Availability}</Table.Td>
            </Table.Tr>
            <Table.Tr style={{ backgroundColor: index % 2 === 0 ? 'var(--mantine-color-gray-0)' : 'transparent' }}>
                {editable && <Table.Td colSpan={2}></Table.Td>}
                <Table.Td colSpan={editable ? 3 : 5} style={{ paddingTop: 0 }}>
                    {Notes}
                </Table.Td>
            </Table.Tr>
        </>
    );
};

export { OrderComponentTable };
