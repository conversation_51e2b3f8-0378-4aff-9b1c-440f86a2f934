import { createContext, Dispatch, ReactNode, SetStateAction, useContext, useEffect, useState } from 'react';

const ValidChildrenCountContext = createContext(0);
const ValidChildrenDispatchContext = createContext<Dispatch<SetStateAction<number>>>(() => {});

export const HideWhenNoValidChildrenWrapper = ({ children }: { children: ReactNode }) => {
    const [validChildren, setValidChildren] = useState(0);

    return (
        <ValidChildrenDispatchContext.Provider value={setValidChildren}>
            <ValidChildrenCountContext.Provider value={validChildren}>
                <div style={{ display: validChildren === 0 ? 'none' : undefined }}>{children}</div>
            </ValidChildrenCountContext.Provider>
        </ValidChildrenDispatchContext.Provider>
    );
};

export const useIncrementValidChildrenCount = () => {
    const setValidChildren = useContext(ValidChildrenDispatchContext);

    useEffect(() => {
        setValidChildren((prev) => prev + 1);

        return () => {
            setValidChildren((prev) => prev - 1);
        };
    }, [setValidChildren]);
};
