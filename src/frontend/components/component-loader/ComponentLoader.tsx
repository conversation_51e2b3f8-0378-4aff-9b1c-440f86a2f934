import React, { FC, useEffect } from 'react';

import { useComponent } from 'hooks/use-component';
import { useProgressBar } from 'hooks/use-progress-bar';
import { LocalNotificationService } from 'services/LocalNotificationService';

type ComponentLoaderProps = {
    id: string;
    children: (props: Omit<ReturnType<typeof useComponent>, 'isLoading' | 'error'>) => React.ReactNode;
};

const ComponentLoader: FC<ComponentLoaderProps> = ({ id, children: Child }) => {
    const { isLoading, error, ...passedProps } = useComponent(id);

    useProgressBar(isLoading);

    useEffect(() => {
        if (error) {
            LocalNotificationService.showError({
                message: 'Something went wrong while loading your component',
            });
        }
    }, [error]);

    if (passedProps.component) {
        return <>{Child(passedProps)}</>;
    }

    return null;
};

export { ComponentLoader };
