import React, { <PERSON> } from 'react';

import { useCurrentUser } from 'hooks/use-current-user';
import { Button, Tooltip } from '@mantine/core';

const InternalScoreBadge: FC<{
    item: {
        aggregateScore?: number;
        scores?: {
            [k: string]: number;
        };
    };
}> = ({ item }) => {
    const currentUser = useCurrentUser();
    const scores = Object.entries(item.scores || {});

    const isDevMode = process.env.NODE_ENV === 'development';
    const show = isDevMode || currentUser?.developer;

    return show ? (
        <Tooltip
            label={scores.map((score) => (
                <div key={score[0]}>
                    {score[0]}: {round(score[1])}
                </div>
            ))}
        >
            <Button size="compact-xs" variant="outline" onClick={() => {}}>
                Score: {round(item.aggregateScore)}
            </Button>
        </Tooltip>
    ) : null;
};

const round = (number?: number) => {
    if (!number) {
        return 0;
    }

    return Math.round(number * 100) / 100;
};

export { InternalScoreBadge };
