import { useState } from 'react';

import Link from 'next/link';
import { Anchor, Button, Modal, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { MeasurementSystem, MeasurementSystemOptions } from 'models';
import { SimpleButton, SimpleButtonGroup } from 'components/buttons';

import { useCurrentUser } from 'hooks/use-current-user';
import { useDiagramEmpty } from '@diagram/hooks/use-diagram-empty';

import { MeasurementSystemService } from 'services/MeasurementSystemService';

const MeasurementSystemModal = () => {
    const [opened, handlers] = useDisclosure(true);
    const currentUser = useCurrentUser();
    const diagramIsEmpty = useDiagramEmpty();

    const [selected, setSelected] = useState<MeasurementSystem | null>(null);

    if (!currentUser || currentUser.measurementSystem || diagramIsEmpty) {
        return null;
    }

    const handleChange = (measurementSystem: MeasurementSystem) => {
        setSelected(measurementSystem);
    };

    const handleSave = () => {
        if (selected) {
            MeasurementSystemService.set(selected);
            handlers.close();
        }
    };

    return (
        <Modal
            size="xs"
            opened={opened}
            onClose={() => {}}
            closeOnEscape={false}
            withCloseButton={false}
            closeOnClickOutside={false}
        >
            <Stack gap="sm" align="center">
                <Stack gap={0} align="center">
                    <Text fw={500}>What is your preferred measurement system?</Text>

                    <Text ta="center" fz="xs" c="dimmed">
                        You can adjust this in your{' '}
                        <Anchor inherit component={Link} href="/account" target="_blank">
                            account settings
                        </Anchor>
                    </Text>
                </Stack>

                <SimpleButtonGroup>
                    {MeasurementSystemOptions.map((option) => (
                        <SimpleButton
                            key={option.value}
                            onClick={() => {
                                handleChange(option.value);
                            }}
                            bg={selected === option.value ? 'white' : 'gray.1'}
                        >
                            {option.label}
                        </SimpleButton>
                    ))}
                </SimpleButtonGroup>

                <Button size="xs" onClick={handleSave} disabled={!selected}>
                    Save my preference
                </Button>
            </Stack>
        </Modal>
    );
};

export { MeasurementSystemModal };
