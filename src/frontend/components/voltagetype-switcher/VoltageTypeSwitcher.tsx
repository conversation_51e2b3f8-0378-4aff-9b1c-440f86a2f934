import React, { FC } from 'react';
import { Flex, Text } from '@mantine/core';
import { VoltageType } from 'models';
import { VoltageTypeSwitch } from 'components/buttons';

export const VoltageTypeSwitcher: FC<{
    showDC?: boolean;
    showAC?: boolean;
    voltageType: VoltageType | null;
    setVoltageType: (type: VoltageType) => void;
    hideLabel?: boolean;
    shortLabel?: boolean;
    showAmpersand?: boolean;
    variant?: 'outline' | 'filled';
}> = ({ showAC, showDC, voltageType, setVoltageType, hideLabel, shortLabel, showAmpersand, variant }) => {
    const voltageTypes = [showAC ? 'AC' : null, showDC ? 'DC' : null].filter(Boolean) as VoltageType[];

    return (
        <Flex gap={4} align="center" c="gray.5">
            {!hideLabel && (
                <Text tt="initial" fw={500} inherit>
                    {`${shortLabel ? '' : 'This port '}supports: `}
                </Text>
            )}
            <VoltageTypeSwitch
                voltageType={voltageType}
                voltageTypes={voltageTypes}
                onChange={setVoltageType}
                variant={variant}
                showAmpersand={showAmpersand}
            />
        </Flex>
    );
};
