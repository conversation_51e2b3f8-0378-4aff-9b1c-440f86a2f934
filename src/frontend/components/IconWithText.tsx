import { FC } from 'react';

import { Box, BoxProps, Flex, FlexProps } from '@mantine/core';

const IconWithText: FC<{
    icon: React.ReactNode;
    iconColor?: BoxProps['c'];
    text: React.ReactNode;
    textProps?: FlexProps;
    wordBreak?: 'normal' | 'break-all' | 'keep-all' | 'break-word';
}> = ({ icon, iconColor = 'primary', text, textProps, wordBreak = 'break-all' }) => {
    return (
        <Flex gap={6} style={{ wordBreak }} align="center" {...textProps}>
            <Box mt={4} c={iconColor}>
                {icon}
            </Box>
            {text}
        </Flex>
    );
};

export { IconWithText };
