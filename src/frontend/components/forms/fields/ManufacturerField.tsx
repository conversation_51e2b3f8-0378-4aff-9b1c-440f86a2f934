import React from 'react';
import { useController } from 'react-hook-form';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { useCompanyProfiles } from 'hooks/use-company-profiles';
import { ManufacturerInput, ManufacturerInputProps } from 'components/inputs/ManufacturerInput';

type ManufacturerFieldProps = Partial<ManufacturerInputProps> & {
    name: string;
    filterManufacturers?: string[];
    disabled?: boolean;
};

const ManufacturerField = ({ name, filterManufacturers, ...props }: ManufacturerFieldProps) => {
    const {
        field,
        fieldState: { error },
    } = useController({ name });

    const { mutate } = useCompanyProfiles();

    return (
        <ManufacturerInput
            {...props}
            name={name}
            value={field.value}
            filterManufacturers={filterManufacturers}
            error={error?.message}
            onOptionSubmit={async (value, query) => {
                if (value === '$new') {
                    const { doc } = await CompanyProfileService.create({
                        name: query,
                    });

                    field.onChange(doc.id);
                    await mutate();
                } else {
                    field.onChange(value);
                }
            }}
            onBlur={() => field.onBlur()}
        />
    );
};

export { ManufacturerField };
