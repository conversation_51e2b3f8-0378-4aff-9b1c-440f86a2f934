import React, { <PERSON> } from 'react';

import { Box, Combobox, useCombobox } from '@mantine/core';

type Suggestion = {
    value: string;
    label: string;
    [key: string]: any;
};

const FieldSuggestions: FC<{
    suggestions: Suggestion[];
    onSelect: (option: Suggestion) => void;
    children: React.ReactNode;
}> = ({ suggestions, onSelect, children }) => {
    const combobox = useCombobox();

    return (
        <Combobox
            store={combobox}
            onOptionSubmit={(value, option) => {
                const selected = suggestions.find((suggestion) => suggestion.value === value);

                if (selected) {
                    onSelect(selected);
                }

                combobox.closeDropdown();
            }}
        >
            <Combobox.Target>
                <Box onFocus={() => combobox.openDropdown()} onBlur={() => combobox.closeDropdown()}>
                    {children}
                </Box>
            </Combobox.Target>
            {suggestions.length > 0 && (
                <Combobox.Dropdown>
                    <Combobox.Options>
                        {suggestions.map((suggestion) => (
                            <Combobox.Option value={suggestion.value} key={suggestion.value}>
                                {suggestion.label}
                            </Combobox.Option>
                        ))}
                    </Combobox.Options>
                </Combobox.Dropdown>
            )}
        </Combobox>
    );
};

export { FieldSuggestions };
