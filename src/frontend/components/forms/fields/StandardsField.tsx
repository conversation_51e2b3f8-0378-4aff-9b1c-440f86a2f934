import { use<PERSON><PERSON>roller } from 'react-hook-form';
import { PillsInputFieldProps } from '@mantine/core';

import { useStandards } from 'hooks/use-standards';
import { StandardService } from 'services/StandardService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { CreatableMultiSelectInput } from 'components/inputs/CreateableMultiSelectInput';

type StandardsFieldProps = { name: string; readOnly?: boolean; inputProps?: PillsInputFieldProps };

const StandardsField = ({ name, readOnly = false, inputProps = {} }: StandardsFieldProps) => {
    const {
        field,
        fieldState: { isTouched, error },
    } = useController({ name, defaultValue: [] });

    const { standards, mutate } = useStandards();

    const addStandard = (query: string) => {
        StandardService.create({ name: query })
            .then((result) => {
                const standard = result.doc;

                field.onChange([...field.value, standard.id]);
                mutate().then();
            })
            .catch((response) => {
                for (const error of response?.errors ?? []) {
                    LocalNotificationService.showError({ title: error?.message, message: error?.data?.[0]?.message });
                }
            });
    };

    return (
        <CreatableMultiSelectInput
            {...field}
            onAdd={addStandard}
            error={isTouched && error?.message}
            data={standards.map((standard: any) => ({
                value: standard.id,
                label: standard.name,
            }))}
            disabled={readOnly}
            inputProps={inputProps}
        />
    );
};

export { StandardsField };
