import React from 'react';

import Link from 'next/link';

import { Anchor, Group, Tooltip } from '@mantine/core';
import { IoArrowForward } from 'react-icons/io5';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useGeneralGlobals } from 'hooks/use-general-globals';

import cx from './ExampleProfileLink.module.scss';
import cxTooltip from 'components/sidebar-nav/components/SidebarNav.Tooltip.module.scss';

const ExampleProfileLink = ({ label = 'See an example profile', hash }: { label?: string; hash?: string }) => {
    const { globals: { exampleProfile } = { exampleProfile: undefined } } = useGeneralGlobals();

    if (!exampleProfile) return null;

    return (
        <Tooltip.Floating
            classNames={cxTooltip}
            label={
                <Group gap={4}>
                    Open profile of {exampleProfile.name}
                    <IoArrowForward size={10} />
                </Group>
            }
            position="top"
        >
            <Anchor
                classNames={cx}
                className="gradient-text-default"
                component={Link}
                target="_blank"
                href={CompanyProfileHelpers.urls.view(exampleProfile.slug, hash)}
            >
                {label}
            </Anchor>
        </Tooltip.Floating>
    );
};

export { ExampleProfileLink };
