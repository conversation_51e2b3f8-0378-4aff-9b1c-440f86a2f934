import React, { <PERSON> } from 'react';

import { IoFolderOutline, IoGlobeOutline, IoSearch } from 'react-icons/io5';
import { TbSparkles } from 'react-icons/tb';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { SidebarNavItemProps } from 'components/sidebar-nav/components/SidebarNav.Item';
import { BsPersonVcard } from 'react-icons/bs';
import { SearchPlanHelpers } from 'components/search-plan/SearchPlanHelpers';
import { RouterHelpers } from 'helpers/RouterHelpers';

const items: SidebarNavItemProps[] = [
    {
        children: 'Get Matched',
        value: RouterHelpers.urls.searchAssistant(),
        icon: <TbSparkles />,
        color: 'primary',
        onClick: () => {
            SearchPlanHelpers.switchPage('plan');
        },
    },
    {
        children: 'Product Catalog',
        value: RouterHelpers.urls.searchTab('overview'),
        icon: <IoSearch />,
        onClick: () => {
            SearchPlanHelpers.switchPage('search', 'overview');
        },
    },
    {
        children: 'Company Profiles',
        value: RouterHelpers.urls.searchTab('profiles'),
        icon: <BsPersonVcard />,
        onClick: () => {
            SearchPlanHelpers.switchPage('search', 'profiles');
        },
    },
    {
        children: 'Reference Designs',
        value: RouterHelpers.urls.searchTab('designs'),
        icon: <IoFolderOutline />,
        onClick: () => {
            SearchPlanHelpers.switchPage('search', 'designs');
        },
    },
    {
        children: 'DC Microgrid Map',
        href: '/map',
        icon: <IoGlobeOutline />,
    },
];

const SidebarNavExplore: FC = () => {
    return (
        <SidebarNav.Section>
            <SidebarNav.Title icon={<IoGlobeOutline />}>Discover</SidebarNav.Title>
            {items.map((item) => (
                <SidebarNav.Item key={item.href || item.value} {...item} />
            ))}
        </SidebarNav.Section>
    );
};

export { SidebarNavExplore };
