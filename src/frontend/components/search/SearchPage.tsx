import { Stack } from '@mantine/core';

import { Page } from 'components/page/Page';
import { Tabs } from 'components/search/components/Tabs';
import { Title } from 'components/search/components/Title';
import { SearchBar } from 'components/component-overview/components/SearchBar';
import { TabContent } from 'components/search/components/TabContent';
import { ComponentLanding } from 'components/component-landing';

import { useSearchInitialContent } from 'components/search/hooks/useSearchInitialContent';

export const SearchPage = () => {
    const { recentCompanies, highlightedCompanies, featuredProducts, recentProducts } = useSearchInitialContent();

    const initialContent = (
        <Stack gap={60}>
            <ComponentLanding.Events />
            <ComponentLanding.Companies
                isCarousel
                companies={highlightedCompanies}
                title="Discover Profiles"
                compact={false}
            />
            <ComponentLanding.Companies
                isCarousel
                companies={recentCompanies}
                title="Recently Joined"
                subtitle="Discover the latest companies to join RE+Source PRO"
                compact={false}
            />
            <ComponentLanding.Products
                isCarousel
                products={featuredProducts}
                title="Discover Products"
                subtitle="Explore the latest and most innovative products in the power system industry"
            />
            <ComponentLanding.Products
                isCarousel
                products={recentProducts}
                title="Recently Added"
                subtitle="Discover the latest products added to RE+Source PRO"
            />
        </Stack>
    );

    return (
        <>
            <Page.Hero pb={0}>
                <Stack gap="xl">
                    <Title />
                    <SearchBar showAIButton />
                    <Tabs initialContent={initialContent} />
                </Stack>
            </Page.Hero>

            <Page.WideContent id="search-content" pt="lg">
                <TabContent initialContent={initialContent} />
            </Page.WideContent>
        </>
    );
};
