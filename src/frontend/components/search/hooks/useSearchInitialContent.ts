import useSWRImmutable from 'swr/immutable';

import { useGeneralGlobals } from 'hooks/use-general-globals';

import { ComponentService } from 'services/ComponentService';
import { CompanyProfileService } from 'services/CompanyProfileService';

const useSearchInitialContent = () => {
    const { data: { docs: recentCompanies = [] } = {} } = useSWRImmutable('recentCompanies', () =>
        CompanyProfileService.getRecentlyJoined(6),
    );

    const { data: { highlights: { profiles: highlightedCompanies = [] } = { profiles: [] } } = {} } =
        useGeneralGlobals();

    const { data: featuredProducts = [] } = useSWRImmutable('featuredProducts', () =>
        ComponentService.getFeaturedProducts(),
    );
    const { data: { docs: recentProducts = [] } = {} } = useSWRImmutable('recentProducts', () =>
        ComponentService.getRecentlyAdded(8),
    );

    return {
        recentCompanies,
        highlightedCompanies,
        featuredProducts,
        recentProducts,
    };
};

export { useSearchInitialContent };
