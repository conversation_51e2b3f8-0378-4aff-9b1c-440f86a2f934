import React, { FC, useEffect } from 'react';

import { Box, Collapse, Group, UnstyledButton } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoChevronForward } from 'react-icons/io5';

import { LocalStorageService } from 'services/LocalStorageService';

export const CollapsibleSection: FC<{
    storageKey: string;
    title: React.ReactNode;
    initialOpened?: boolean;
    children: React.ReactNode;
}> = ({ storageKey, title, children, initialOpened = true }) => {
    const [opened, handlers] = useDisclosure(initialOpened);

    useEffect(() => {
        const collapsableSections = LocalStorageService.get('collapsable-sections') || {};

        const savedCollapsed = collapsableSections[storageKey];

        if (savedCollapsed === true) {
            handlers.close();
        }

        if (savedCollapsed === false) {
            handlers.open();
        }
    }, []);

    const toggleCollapsed = () => {
        handlers.toggle();

        const collapsableSections = LocalStorageService.get('collapsable-sections') || {};

        collapsableSections[storageKey] = opened;
        LocalStorageService.store('collapsable-sections', collapsableSections);
    };

    return (
        <Box>
            <UnstyledButton
                component={Group}
                gap={4}
                onClick={toggleCollapsed}
                style={{
                    userSelect: 'none',
                }}
            >
                {title}
                <IoChevronForward
                    size={10}
                    style={{
                        color: 'var(--mantine-color-gray-6)',
                        transform: opened ? 'rotate(90deg)' : 'rotate(0)',
                        transition: 'transform 0.2s ease',
                    }}
                />
            </UnstyledButton>

            <Collapse in={opened} mt="xs" transitionTimingFunction="ease-in-out">
                {children}
            </Collapse>
        </Box>
    );
};
