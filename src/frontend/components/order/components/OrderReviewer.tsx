import { Group, Text } from '@mantine/core';

import Link from 'next/link';

import { useCompanyProfile } from 'hooks/use-company-profile';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { CompanyLogo } from 'components/company-logo';

const OrderReviewer = ({
    manufacturer: manufacturerId,
    distributor: distributorId,
}: {
    manufacturer?: string;
    distributor?: string;
}) => {
    const { company } = useCompanyProfile(manufacturerId || distributorId);

    if (!company) return null;

    return (
        <Group gap={8}>
            <Text fz="sm" fw={500} c="dimmed">
                This quote is reviewed by
            </Text>
            <Link href={CompanyProfileHelpers.urls.view(company.slug)} target="_blank">
                <CompanyLogo logos={company.logos} height={12} />
            </Link>
        </Group>
    );
};

export { OrderReviewer };
