import React, { FC } from 'react';

import { useOrder } from 'hooks/use-order';
import { Order } from 'models';
import { getOnDelete, AddFileFormOnAdd, FilesPage, AddFileForm, FilesTable } from 'components/files';

const OrderFiles: FC<{ order: Order }> = ({ order: serverSideOrder }) => {
    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 2 });

    if (orderController.order === null) {
        return null;
    }

    const deleteFile = getOnDelete({
        collection: 'order',
        id: orderController.order.id,
        mutate: orderController.mutate,
    });

    const onAdd: AddFileFormOnAdd = (updatedCollection) => orderController.mutate(updatedCollection);

    return (
        <FilesPage
            top={<AddFileForm collection="order" id={orderController.order.id} onAdd={onAdd} />}
            // @ts-ignore
            bottom={<FilesTable files={orderController.order.files} deleteFile={deleteFile} />}
        />
    );
};

export { OrderFiles };
