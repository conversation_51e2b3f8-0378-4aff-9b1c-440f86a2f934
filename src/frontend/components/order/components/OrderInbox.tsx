import React, { FC, useEffect, useMemo, useState } from 'react';

import { <PERSON>chor, <PERSON>ton, Card, Stack, Text, Textarea, Timeline } from '@mantine/core';

import { Avatar } from 'components/avatar/Avatar';
import { orderStatuses } from 'components/order-status';
import { useOrder } from 'hooks/use-order';
import { useCurrentUser } from 'hooks/use-current-user';
import { alphabetical } from 'radash';
import { DateService } from 'services/DateService';
import { Order, OrderStatus } from 'models';

type Events = Required<Order>['events'];
type Messages = Required<Order>['messages'];
type InboxItem = Events[number] | Messages[number];

const updateMessages: Record<OrderStatus, React.ReactNode> = {
    draft: (
        <>
            Set quote to{' '}
            <Text span fw={700}>
                draft
            </Text>
        </>
    ),
    quoteRequested: (
        <>
            Requested a{' '}
            <Text span fw={700}>
                quote
            </Text>
        </>
    ),
    quoteSubmitted: (
        <>
            Submitted a{' '}
            <Text span fw={700}>
                quote
            </Text>
        </>
    ),
    orderSubmitted: (
        <>
            Approved quote and{' '}
            <Text span fw={700}>
                placed purchase order
            </Text>
        </>
    ),
    orderAcknowledged: (
        <>
            <Text span fw={700}>
                Acknowledged
            </Text>{' '}
            the order
        </>
    ),
    orderCancelled: (
        <>
            <Text span fw={700}>
                Cancelled
            </Text>{' '}
            the order
        </>
    ),
    orderInProgress: (
        <>
            Set order{' '}
            <Text span fw={700}>
                in progress
            </Text>
        </>
    ),
    orderCompleted: (
        <>
            Set order as{' '}
            <Text span fw={700}>
                completed
            </Text>
        </>
    ),
    archived: 'Archived the quote',
    deleted: 'Deleted the quote',
};

const OrderInbox: FC<{ order: Order }> = ({ order: serverSideOrder }) => {
    const user = useCurrentUser();

    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 1 });

    const inboxItems = useMemo(() => {
        if (orderController.order === null) return [];

        return alphabetical(
            [...orderController.order.events, ...(orderController.order.messages ?? [])],
            (item) => item.date,
            'asc',
        );
    }, [orderController.order]);

    const [shownItems, setShownItems] = useState<InboxItem[]>([]);

    useEffect(() => {
        setShownItems(
            inboxItems.length > 15
                ? [inboxItems[0], ...inboxItems.slice(inboxItems.length - 10, inboxItems.length + 1)]
                : inboxItems,
        );
    }, [inboxItems]);

    const expandItems = () => {
        setShownItems(inboxItems);
    };

    const shownAll = shownItems.length === inboxItems.length;

    if (orderController.order === null) {
        return null;
    }

    return (
        <Card p="xl" shadow="sm">
            <Stack>
                <Timeline bulletSize={26} lineWidth={2}>
                    {shownItems.map((item, index) => {
                        const event = item as unknown as Events[number];

                        let timelineItem = <OrderEvent key={index} event={event} />;
                        let bullet = orderStatuses[event.type]?.icon;

                        if ('message' in item) {
                            const message = item as unknown as Messages[number];
                            timelineItem = <OrderMessage message={message} />;

                            bullet = typeof message.user === 'string' ? null : <Avatar user={message.user} />;
                        }

                        const showExpand = index === 0 && !shownAll;

                        return (
                            <Timeline.Item key={index} bullet={bullet} lineVariant={showExpand ? 'dashed' : 'solid'}>
                                {timelineItem}
                                {showExpand && (
                                    <Anchor component="button" type="button" onClick={expandItems} mt="xl">
                                        Expand...
                                    </Anchor>
                                )}
                            </Timeline.Item>
                        );
                    })}
                    <Timeline.Item bullet={user && <Avatar user={user} />}>
                        <AddOrderMessage order={serverSideOrder} />
                    </Timeline.Item>
                </Timeline>
            </Stack>
        </Card>
    );
};

const OrderEvent: FC<{ event: Events[number] }> = ({ event }) => {
    const displayName = typeof event.user === 'string' ? null : event.user ? event.user.name : null;

    return (
        <React.Fragment>
            <Text>{updateMessages[event.type]}</Text>
            <Text c="dimmed" size="xs">
                {displayName} • {DateService.formatDistanceToNow(event.date)}
            </Text>
        </React.Fragment>
    );
};

const OrderMessage: FC<{ message: Messages[number] }> = ({ message }) => {
    if (typeof message.user === 'string') return null;

    const displayName = message.user.name ?? message.user.email;

    return (
        <Card
            style={(theme) => ({
                border: `1px solid ${theme.colors.gray[2]}`,
            })}
            p="xs"
        >
            <Text>{message.message}</Text>
            <Text c="dimmed" size="xs">
                {displayName} • {DateService.formatDistanceToNow(message.date)}
            </Text>
        </Card>
    );
};

const AddOrderMessage: FC<{ order: Order }> = ({ order: serverSideOrder }) => {
    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 1 });

    const [message, setMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    if (orderController.order === null) {
        return null;
    }

    const addMessage = async () => {
        setIsLoading(true);
        await orderController.addMessage(message);
        setMessage('');
        setIsLoading(false);
    };

    return (
        <Stack gap={8}>
            <Textarea
                placeholder="Send a message"
                value={message}
                onChange={(event) => setMessage(event.currentTarget.value)}
                style={{ width: '100%' }}
                onKeyUp={async (event) => {
                    if (event.key === 'Enter' && !event.shiftKey) {
                        addMessage();
                    }
                }}
                disabled={isLoading}
            />
            <Button onClick={addMessage} loading={isLoading} style={{ alignSelf: 'flex-start' }}>
                Send message
            </Button>
        </Stack>
    );
};

export { OrderInbox };
