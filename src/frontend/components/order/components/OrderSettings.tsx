import { FC } from 'react';

import { Card, Group, Stack, Title } from '@mantine/core';
import { TbArchive, TbArchiveOff } from 'react-icons/tb';
import { IoTrashOutline } from 'react-icons/io5';

import { useOrder } from 'hooks/use-order';

import { Order } from 'models';
import { AsyncButton } from 'components/async-button/AsyncButton';

const OrderSettings: FC<{ order: Order }> = ({ order: serverSideOrder }) => {
    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 1 });

    if (orderController.order === null) {
        return null;
    }

    return (
        <Card p="xl" shadow="sm">
            <Stack>
                <Title order={2}>Actions</Title>
                <Group gap={4}>
                    {orderController.order.status !== 'deleted' ? (
                        <>
                            {orderController.order.status === 'archived' ? (
                                <AsyncButton
                                    onClick={orderController.eventTriggers.draft}
                                    color="primary"
                                    variant="light"
                                    leftSection={<TbArchiveOff size={14} strokeWidth={1.5} />}
                                >
                                    Unarchive order
                                </AsyncButton>
                            ) : (
                                <AsyncButton
                                    onClick={orderController.eventTriggers.archived}
                                    color="primary"
                                    variant="light"
                                    leftSection={<TbArchive size={14} strokeWidth={1.5} />}
                                >
                                    Archive order
                                </AsyncButton>
                            )}

                            <AsyncButton
                                onClick={orderController.eventTriggers.deleted}
                                color="red"
                                variant="light"
                                leftSection={<IoTrashOutline size={14} />}
                            >
                                Delete order
                            </AsyncButton>
                        </>
                    ) : (
                        <AsyncButton
                            onClick={orderController.eventTriggers.draft}
                            color="red"
                            variant="light"
                            leftSection={<IoTrashOutline size={14} />}
                        >
                            Undelete order
                        </AsyncButton>
                    )}
                </Group>
            </Stack>
        </Card>
    );
};

export { OrderSettings };
