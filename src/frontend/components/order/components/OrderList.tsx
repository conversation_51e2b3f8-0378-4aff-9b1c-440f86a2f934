import React, { <PERSON> } from 'react';

import { useOrder } from 'hooks/use-order';
import { Order } from 'models';
import { OrderComponentTable } from 'components/order-components-table';
import { Card, Stack } from '@mantine/core';
import { MainOrderAction } from 'components/order/components/OrderStatus';

const OrderList: FC<{ order: Order }> = ({ order: serverSideOrder }) => {
    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 1 });

    if (orderController.order === null) {
        return null;
    }

    return (
        <Card p="xl" shadow="sm" style={{ overflow: 'visible' }}>
            <Stack align="flex-start">
                <OrderComponentTable orderController={orderController} />
                <MainOrderAction controller={orderController} size="sm" />
            </Stack>
        </Card>
    );
};

export { OrderList };
