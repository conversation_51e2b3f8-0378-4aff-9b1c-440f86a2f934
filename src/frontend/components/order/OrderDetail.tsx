import { FC } from 'react';

import { <PERSON><PERSON>, <PERSON>ton, Group } from '@mantine/core';
import { TbArrowLeft, TbFiles, TbInbox } from 'react-icons/tb';
import { IoListSharp } from 'react-icons/io5';

import Link from 'next/link';

import { OrderFiles, OrderInbox, OrderList, OrderStatus } from 'components/order/components';
import { useOrder } from 'hooks/use-order';
import { useOrderMeta } from 'hooks/use-order-meta';
import { Order } from 'models';
import { HorizontalTabs } from 'components/horizontal-tabs/HorizontalTabs';
import { Page } from 'components/page/Page';
import { useProjectByDesign } from 'hooks/use-project-by-design';
import { OrderReviewer } from 'components/order/components/OrderReviewer';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

const OrderDetail: FC<{ order: Order }> = ({ order: serverSideOrder }) => {
    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 1 });
    const { totalComponentCount } = useOrderMeta(orderController.order);

    const iconProps = {
        size: 16,
        strokeWidth: 1.5,
    };

    const { project } = useProjectByDesign(orderController.order?.design, { depth: 1 });

    if (orderController.order === null) {
        return null;
    }

    const renameOrder = async (newName: string) => {
        await orderController.renameOrder(newName);
    };

    return (
        <Page showBackground title={orderController.order.name}>
            <Page.Content
                title={
                    (
                        <div>
                            {project && (
                                <Button
                                    size="compact-xs"
                                    component={Link}
                                    href={ProjectHelpers.urls.editor(project.id)}
                                    variant="outline"
                                    leftSection={<TbArrowLeft size={12} />}
                                    mb="xs"
                                >
                                    Back to project
                                </Button>
                            )}
                            <Page.EditableTitle title={orderController.order.name} onSave={renameOrder} />
                        </div>
                    ) as any
                }
            >
                <HorizontalTabs
                    topSection={
                        <Group justify="space-between" ml="lg">
                            <OrderReviewer {...orderController.orderCompany} />
                            <OrderStatus order={serverSideOrder} />
                        </Group>
                    }
                    tabs={[
                        {
                            value: 'items',
                            label: (
                                <Group gap={4}>
                                    Items{' '}
                                    <Badge size="xs" variant="filled">
                                        {totalComponentCount}
                                    </Badge>
                                </Group>
                            ),
                            icon: <IoListSharp {...iconProps} />,
                            content: <OrderList order={serverSideOrder} />,
                        },
                        {
                            value: 'inbox',
                            label: 'Inbox',
                            icon: <TbInbox {...iconProps} />,
                            content: <OrderInbox order={serverSideOrder} />,
                        },
                        {
                            value: 'files',
                            label: 'Files',
                            icon: <TbFiles {...iconProps} />,
                            content: <OrderFiles order={serverSideOrder} />,
                        },
                        // {
                        //     value: 'settings',
                        //     label: 'Settings',
                        //     icon: <TbSettings {...iconProps} />,
                        //     content: <OrderSettings order={serverSideOrder} />,
                        // },
                    ]}
                />
            </Page.Content>
        </Page>
    );
};

export { OrderDetail };
