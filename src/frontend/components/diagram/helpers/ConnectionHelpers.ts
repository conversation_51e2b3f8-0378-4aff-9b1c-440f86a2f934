import {
    Diagram,
    DiagramConnection,
    DiagramComponentInstance,
    VoltageClass,
    VoltageType,
    PowerFlowDirection,
    ComponentQuery,
} from 'models';

import { isNumber, uid } from 'radash';

import { PortHelpers } from '@diagram/helpers/PortHelpers';

import { ComponentInstanceService } from '@diagram/services';

const ConnectionHelpers = {
    __validateHelperArguments: (
        connection: DiagramConnection,
        fromComponentInstance: DiagramComponentInstance,
        toComponentInstance: DiagramComponentInstance,
    ) => {
        if (
            connection.from.componentInstanceId !== fromComponentInstance.id ||
            connection.to.componentInstanceId !== toComponentInstance.id
        ) {
            console.error('ConnectionHelpers.mismatch', {
                connection,
                fromComponentInstance,
                toComponentInstance,
            });

            throw new Error(
                "Bro, did you mix your from & to components? (components don't match connection endpoints)",
            );
        }
    },

    generateId: () => {
        return `connection-${uid(8)}`;
    },

    getAvailablePowerFlowDirections: (
        connection: DiagramConnection,
        fromComponentInstance: DiagramComponentInstance,
        toComponentInstance: DiagramComponentInstance,
    ): {
        from: PowerFlowDirection[];
        to: PowerFlowDirection[];
        relative: ('load' | 'bidirectional' | 'generate')[];
    } => {
        ConnectionHelpers.__validateHelperArguments(connection, fromComponentInstance, toComponentInstance);

        const { from, to } = connection;

        let fromPowerFlowDirections: PowerFlowDirection[] = [];
        let toPowerFlowDirections: PowerFlowDirection[] = [];

        if (isNumber(from.port) && fromComponentInstance) {
            fromPowerFlowDirections = PortHelpers.getPowerFlowDirectionOptions(fromComponentInstance, from.port);
        }

        if (isNumber(to.port) && toComponentInstance) {
            toPowerFlowDirections = PortHelpers.getPowerFlowDirectionOptions(toComponentInstance, to.port);
        }

        fromPowerFlowDirections = fromPowerFlowDirections.filter((powerFlowDirection) => {
            if (powerFlowDirection === 'input') {
                return toPowerFlowDirections.includes('output') || toPowerFlowDirections.includes('bidirectional');
            }

            if (powerFlowDirection === 'output') {
                return toPowerFlowDirections.includes('input') || toPowerFlowDirections.includes('bidirectional');
            }

            if (powerFlowDirection === 'bidirectional') {
                return toPowerFlowDirections.includes('bidirectional');
            }

            return false;
        });

        toPowerFlowDirections = toPowerFlowDirections.filter((powerFlowDirection) => {
            if (powerFlowDirection === 'input') {
                return fromPowerFlowDirections.includes('output') || fromPowerFlowDirections.includes('bidirectional');
            }

            if (powerFlowDirection === 'output') {
                return fromPowerFlowDirections.includes('input') || fromPowerFlowDirections.includes('bidirectional');
            }

            if (powerFlowDirection === 'bidirectional') {
                return fromPowerFlowDirections.includes('bidirectional');
            }

            return false;
        });

        const relative: ('load' | 'bidirectional' | 'generate')[] = [];

        if (
            fromPowerFlowDirections.includes('input') &&
            (toPowerFlowDirections.includes('bidirectional') || toPowerFlowDirections.includes('output'))
        ) {
            relative.push('load');
        }

        if (fromPowerFlowDirections.includes('bidirectional') && toPowerFlowDirections.includes('bidirectional')) {
            relative.push('bidirectional');
        }

        if (
            fromPowerFlowDirections.includes('output') &&
            (toPowerFlowDirections.includes('input') || toPowerFlowDirections.includes('bidirectional'))
        ) {
            relative.push('generate');
        }

        return {
            from: fromPowerFlowDirections,
            to: toPowerFlowDirections,
            relative,
        };
    },

    getAvailableVoltageTypes: (
        connection: DiagramConnection,
        fromComponentInstance: DiagramComponentInstance,
        toComponentInstance: DiagramComponentInstance,
    ): VoltageType[] => {
        ConnectionHelpers.__validateHelperArguments(connection, fromComponentInstance, toComponentInstance);

        const { from, to } = connection;

        let fromVoltageTypes: VoltageType[] = ['AC', 'DC'];
        let toVoltageTypes: VoltageType[] = ['AC', 'DC'];

        if (isNumber(from.port)) {
            fromVoltageTypes = PortHelpers.getVoltageTypeOptions(fromComponentInstance, from.port);
        }

        if (isNumber(to.port)) {
            toVoltageTypes = PortHelpers.getVoltageTypeOptions(toComponentInstance, to.port);
        }

        return fromVoltageTypes.filter((value) => toVoltageTypes.includes(value));
    },

    getEndpointOrder: (
        fromComponentInstance: DiagramComponentInstance,
        toComponentInstance: DiagramComponentInstance,
    ):
        | {
              start: 'from';
              end: 'to';
          }
        | {
              start: 'to';
              end: 'from';
          } => {
        if (
            fromComponentInstance &&
            toComponentInstance &&
            fromComponentInstance.position.x + fromComponentInstance.position.y >
                toComponentInstance.position.x + toComponentInstance.position.y
        ) {
            return {
                start: 'to',
                end: 'from',
            };
        }

        return {
            start: 'from',
            end: 'to',
        };
    },

    getConnectionStartAndEnd: ({
        connection,
        from,
        to,
    }: {
        connection: DiagramConnection;
        from: DiagramComponentInstance;
        to: DiagramComponentInstance;
    }) => {
        const componentInstances = {
            [from.id]: from,
            [to.id]: to,
        };

        const order = ConnectionHelpers.getEndpointOrder(from, to);

        const start = componentInstances[connection[order.start].componentInstanceId];
        const end = componentInstances[connection[order.end].componentInstanceId];

        return { start, end };
    },

    getOrientation: (
        connection: DiagramConnection,
        fromComponentInstance: DiagramComponentInstance,
        toComponentInstance: DiagramComponentInstance,
    ) => {
        ConnectionHelpers.__validateHelperArguments(connection, fromComponentInstance, toComponentInstance);

        const { from, to } = connection;

        const { x: _fromX, y: _fromY } = fromComponentInstance.position;
        const { x: _toX, y: _toY } = toComponentInstance.position;

        const fromXOffset =
            from.edge === 'top' || from.edge === 'bottom'
                ? from.offset
                : from.edge === 'right'
                  ? fromComponentInstance.colSpan - 1
                  : 0;
        const fromYOffset =
            from.edge === 'left' || from.edge === 'right'
                ? from.offset
                : from.edge === 'bottom'
                  ? fromComponentInstance.rowSpan - 1
                  : 0;

        const toXOffset =
            to.edge === 'top' || to.edge === 'bottom'
                ? to.offset
                : to.edge === 'right'
                  ? toComponentInstance.colSpan - 1
                  : 0;
        const toYOffset =
            to.edge === 'left' || to.edge === 'right'
                ? to.offset
                : to.edge === 'bottom'
                  ? toComponentInstance.rowSpan - 1
                  : 0;

        const fromX = _fromX + fromXOffset;
        const fromY = _fromY + fromYOffset;
        const toX = _toX + toXOffset;
        const toY = _toY + toYOffset;

        if (fromX === toX) {
            if (fromY < toY) {
                return 's';
            }

            if (fromY > toY) {
                return 'n';
            }
        }

        if (fromX < toX) {
            if (fromY === toY) {
                return 'e';
            }

            if (fromY < toY) {
                return 'se';
            }

            if (fromY > toY) {
                return 'ne';
            }
        }

        if (fromX > toX) {
            if (fromY === toY) {
                return 'w';
            }

            if (fromY < toY) {
                return 'sw';
            }

            if (fromY > toY) {
                return 'nw';
            }
        }
    },

    getLinesLabel: (connection: DiagramConnection, voltageType: VoltageType | null) => {
        const parts: string[] = [];
        let PE = false;

        if (voltageType === 'AC') {
            const lines = connection.lines.AC;

            if (lines.L1 && lines.L2 && lines.L3) {
                parts.push('L1⋯3');
            } else if (lines.L1 && lines.L2) {
                parts.push('L1⋯2');
            } else if (lines.L1 && lines.L3) {
                parts.push('L1 L3');
            } else if (lines.L2 && lines.L3) {
                parts.push('L2⋯3');
            } else if (lines.L1) {
                parts.push('L1');
            } else if (lines.L2) {
                parts.push('L2');
            } else if (lines.L3) {
                parts.push('L3');
            }

            if (lines.N) {
                parts.push('N');
            }

            if (lines.PE) {
                PE = true;
            }
        }

        if (voltageType === 'DC') {
            const lines = connection.lines.DC;

            if (lines['L+']) {
                parts.push('L+');
            }

            if (lines['L-']) {
                parts.push('L-');
            }

            if (lines.M) {
                parts.push('M');
            }

            if (lines.PE) {
                PE = true;
            }
        }

        return {
            lines: parts.join(' ❘ '),
            PE,
        };
    },

    getRelativePowerFlowDirection: (
        connection: DiagramConnection,
        fromComponentInstance: DiagramComponentInstance,
        toComponentInstance: DiagramComponentInstance,
    ) => {
        ConnectionHelpers.__validateHelperArguments(connection, fromComponentInstance, toComponentInstance);

        const { from, to } = connection;

        if (!isNumber(from.port) || !isNumber(to.port)) {
            return null;
        }

        const fromPowerFlowDirection = fromComponentInstance.configuration.ports[from.port]?.powerFlowDirection;
        const toPowerFlowDirection = toComponentInstance.configuration.ports[to.port]?.powerFlowDirection;

        if (fromPowerFlowDirection === 'bidirectional' && toPowerFlowDirection === 'bidirectional') {
            return 'bidirectional';
        }

        if (
            (fromPowerFlowDirection === 'bidirectional' && toPowerFlowDirection === 'input') ||
            (fromPowerFlowDirection === 'output' && toPowerFlowDirection === 'input') ||
            (toPowerFlowDirection === 'bidirectional' && fromPowerFlowDirection === 'output') ||
            (toPowerFlowDirection === 'input' && fromPowerFlowDirection === 'output')
        ) {
            return 'generate';
        }

        if (
            (fromPowerFlowDirection === 'bidirectional' && toPowerFlowDirection === 'output') ||
            (fromPowerFlowDirection === 'input' && toPowerFlowDirection === 'output') ||
            (toPowerFlowDirection === 'bidirectional' && fromPowerFlowDirection === 'input') ||
            (toPowerFlowDirection === 'output' && fromPowerFlowDirection === 'input')
        ) {
            return 'load';
        }

        return null;
    },

    getVoltageType: (
        connection: DiagramConnection,
        fromComponentInstance: DiagramComponentInstance,
        toComponentInstance: DiagramComponentInstance,
    ) => {
        ConnectionHelpers.__validateHelperArguments(connection, fromComponentInstance, toComponentInstance);

        const { from, to } = connection;

        if (isNumber(from.port) && isNumber(to.port)) {
            const fromVoltageType = fromComponentInstance?.configuration.ports?.[from.port]?.voltageType;
            const toVoltageType = toComponentInstance?.configuration.ports?.[to.port]?.voltageType;

            return fromVoltageType === toVoltageType ? fromVoltageType : null;
        }

        return null;
    },

    getVoltageClass: (
        connection: DiagramConnection,
        voltageType: VoltageType | null,
        voltageClasses: Diagram['voltageClasses'],
    ): VoltageClass | null => {
        if (voltageType === null) {
            return null;
        }

        const voltage = connection.requirements.voltage.value ?? 0;

        if (voltage === 0) {
            return null;
        }

        return (
            voltageClasses[voltageType].findLast((voltageClass) => {
                return voltage >= voltageClass.min && voltage <= voltageClass.max;
            }) || null
        );
    },

    getFallbackVoltageClassColor: (voltageType: VoltageType | null) => {
        return voltageType
            ? {
                  AC: '#d9480f',
                  DC: '#1864ab',
              }[voltageType]
            : '#868e96';
    },

    getNumberOfConductors: (connection: DiagramConnection, voltageType: VoltageType | null) => {
        const { lines } = connection;

        let numberOfConductors = 0;

        if (voltageType === 'DC') {
            numberOfConductors = [lines.DC['L+'], lines.DC['L-'], lines.DC.M].filter(Boolean).length;
        } else if (voltageType === 'AC') {
            numberOfConductors = [lines.AC.L1, lines.AC.L2, lines.AC.L3, lines.AC.N].filter(Boolean).length;
        }

        if (numberOfConductors === 1) {
            // Always at least 2 conductors are required to circulate current
            return 2;
        }

        return numberOfConductors;
    },

    calculateResistance: (material: 'aluminum' | 'copper', wireSize: number, length: number) => {
        const resistivity = material === 'aluminum' ? 2.65e-8 : 1.68e-8;

        return +((resistivity * length) / (wireSize * 1e-6)).toFixed(4);
    },

    calculateResistanceFromResistancePerLength: (length: number, resistancePerLength: number) => {
        return +(resistancePerLength * length).toFixed(4);
    },

    getConnectionForComponentInstance: (
        diagram: Diagram,
        componentInstanceId: DiagramComponentInstance['id'],
        port: DiagramConnection['from' | 'to']['port'],
    ): DiagramConnection | undefined => {
        const { connections } = diagram;

        const isConnection = (connection: DiagramConnection) => {
            return (
                (connection.from.componentInstanceId === componentInstanceId && connection.from.port === port) ||
                (connection.to.componentInstanceId === componentInstanceId && connection.to.port === port)
            );
        };

        return Object.values(connections).find((connection) => isConnection(connection));
    },

    getOtherEndpoint: (
        diagram: Diagram,
        componentInstanceId: DiagramComponentInstance['id'],
        port: DiagramConnection['from' | 'to']['port'],
    ): DiagramConnection['from' | 'to'] | undefined => {
        const connection = ConnectionHelpers.getConnectionForComponentInstance(diagram, componentInstanceId, port);

        if (!connection) {
            return undefined;
        }

        const { from, to } = connection;

        return from.componentInstanceId === componentInstanceId && from.port === port ? to : from;
    },

    generateDefaultValues: (connection: DiagramConnection): Partial<ComponentQuery> => {
        const fromComponentInstance = ComponentInstanceService.getById(connection.from.componentInstanceId);
        const toComponentInstance = ComponentInstanceService.getById(connection.to.componentInstanceId);

        const voltageType = ConnectionHelpers.getVoltageType(connection, fromComponentInstance, toComponentInstance);

        return {
            type: 'cable',
            cable: {
                voltageType: voltageType ?? undefined,
                lines: connection.lines,
                voltage: {
                    max: connection.requirements.voltage.value ?? undefined,
                },
                wireSize: connection.wireSize,
            },
        };
    },
};

export { ConnectionHelpers };
