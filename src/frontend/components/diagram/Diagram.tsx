import React, { Suspense, FC, useEffect } from 'react';
import dynamic from 'next/dynamic';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { DiagramErrorBoundary } from '@diagram/components/diagram-error-boundry/DiagramErrorBoundry';
import { DiagramGridBackground } from '@diagram/components/diagram-grid/DiagramGridBackground';
import { Page } from 'components/page';

import { Diagram as DiagramType, DiagramSchema } from 'models';
import { Pointer, set } from 'sync-engine';

import { DiagramService } from '@diagram/services/DiagramService';
import { DiagramSyncService } from '@diagram/services/DiagramSyncService';
import { DiagramAutofixService } from './services/DiagramAutofixService';

import { loadingState } from 'state/loading';
import { initializedState } from '@diagram/state/initialized';
import { diagram as diagramState } from '@diagram/state/diagram';

const LazyDiagram = dynamic(() => import('./Diagram.lazy'), {
    ssr: false,
    loading: () => <DiagramFallback />,
});

const DiagramFallback: FC = () => {
    useEffect(() => {
        loadingState.suspense = true;

        return () => {
            loadingState.suspense = false;
        };
    }, []);

    return (
        <Page hideFooter title="Loading">
            <Page.FullScreenContent>
                <DiagramGridBackground />
            </Page.FullScreenContent>
        </Page>
    );
};

const WrappedDiagram: FC<{
    preload?: DiagramType;
    pointer?: Pointer;
}> = ({ preload, pointer }) => {
    const initialized = useSnapshot(initializedState);

    useEffect(() => {
        initializedState.diagram = false;

        const diagram = preload || DiagramSchema.parse({});

        DiagramSyncService.initialize(diagram as DiagramType, pointer);
        DiagramAutofixService.autofix();

        const parsed = DiagramSchema.safeParse(diagramState);

        if (parsed.success) {
            DiagramSyncService.save();

            initializedState.diagram = true;
        } else {
            /**
             * Things are NOT okay, just show a message to the user.
             */
            DiagramService.openForceRefreshModal('loading');
            console.error('Failed to validate diagram', JSON.stringify(parsed?.error?.issues), diagram.id);
        }

        return () => {
            initializedState.diagram = false;
        };
    }, [preload, pointer]);

    return initialized.diagram ? (
        <DiagramErrorBoundary>
            <Suspense fallback={<DiagramFallback />}>
                <LazyDiagram />
            </Suspense>
        </DiagramErrorBoundary>
    ) : (
        <DiagramFallback />
    );
};

export { WrappedDiagram as Diagram };
