import React, { FC } from 'react';

import { Button, Checkbox, HoverCard, Stack, Text, Tooltip } from '@mantine/core';

import { MdOutlineGrid3X3 } from 'react-icons/md';
import { IoPricetagOutline } from 'react-icons/io5';

import { MeasurementSystem } from 'models';

import { useCanvas } from '@diagram/hooks/use-canvas';
import { useMeasurementSystem } from 'hooks/use-measurement-system';

import { canvasState } from '@diagram/state/canvas';

import { get, set } from 'radash';
import { MeasurementSystemService } from 'services/MeasurementSystemService';

const DiagramLayersControl: FC = () => {
    const canvas = useCanvas();
    const measurementSystem = useMeasurementSystem();

    const setGrid = (grid: boolean) => {
        canvasState.grid = grid;
    };

    return (
        <Button.Group orientation="vertical">
            <HoverCard withArrow position="right" shadow="xl">
                <HoverCard.Target>
                    <Button
                        variant="default"
                        styles={{
                            root: {
                                width: '32px',
                                height: '32px',
                                padding: 0,
                            },
                        }}
                    >
                        <IoPricetagOutline size={12} />
                    </Button>
                </HoverCard.Target>
                <HoverCard.Dropdown w={200}>
                    <Stack gap={8}>
                        <Text size="xs" fw="600">
                            Component
                        </Text>
                        {[{ label: 'All', key: 'components.all' }].map((checkbox) => (
                            <Checkbox
                                key={checkbox.label}
                                size="xs"
                                label={checkbox.label}
                                checked={get(canvas.labels, checkbox.key)}
                                onChange={() => {
                                    set(canvasState.labels, checkbox.key, !get(canvas.labels, checkbox.key));
                                }}
                            />
                        ))}
                        <Text size="xs" fw="600">
                            Component Port
                        </Text>
                        {[
                            { label: 'Voltage', key: 'ports.voltage' },
                            { label: 'Current', key: 'ports.current' },
                            { label: 'Power', key: 'ports.power' },
                            { label: 'Others', key: 'ports.others' },
                        ].map((checkbox) => (
                            <Checkbox
                                key={checkbox.label}
                                size="xs"
                                label={checkbox.label}
                                checked={get(canvas.labels, checkbox.key)}
                                onChange={() => {
                                    set(canvasState.labels, checkbox.key, !get(canvas.labels, checkbox.key));
                                }}
                            />
                        ))}
                        <Text size="xs" fw="600">
                            Connection
                        </Text>
                        {[
                            { label: 'Voltage', key: 'connections.voltage' },
                            { label: 'Current', key: 'connections.current' },
                            { label: 'Wire Size', key: 'connections.wireSize' },
                            { label: 'Lines', key: 'connections.lines' },
                            { label: 'Others', key: 'connections.others' },
                        ].map((checkbox) => (
                            <Checkbox
                                key={checkbox.label}
                                size="xs"
                                label={checkbox.label}
                                checked={get(canvas.labels, checkbox.key)}
                                onChange={() => {
                                    set(canvasState.labels, checkbox.key, !get(canvas.labels, checkbox.key));
                                }}
                            />
                        ))}
                    </Stack>
                </HoverCard.Dropdown>
            </HoverCard>
            <Tooltip label="Toggle Grid" position="right" withArrow>
                <Button
                    variant="default"
                    styles={{
                        root: {
                            width: '32px',
                            height: '32px',
                            padding: 0,
                        },
                    }}
                    onClick={() => {
                        setGrid(!canvas.grid);
                    }}
                >
                    <MdOutlineGrid3X3 size={12} opacity={canvas.grid ? 1 : 0.25} />
                </Button>
            </Tooltip>
            <Tooltip label="Switch between metric and imperial measurement units" position="right" withArrow>
                <Button
                    variant="default"
                    styles={{
                        root: {
                            width: '32px',
                            height: '32px',
                            padding: 0,

                            fontSize: 9,
                            fontWeight: 600,
                        },
                    }}
                    onClick={MeasurementSystemService.toggle}
                >
                    {measurementSystem === MeasurementSystem.IMPERIAL && 'IMP'}
                    {measurementSystem === MeasurementSystem.METRIC && 'MET'}
                </Button>
            </Tooltip>
        </Button.Group>
    );
};

export { DiagramLayersControl };
