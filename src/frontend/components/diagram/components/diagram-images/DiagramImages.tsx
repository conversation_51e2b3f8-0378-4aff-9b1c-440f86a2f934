import React, { useState, FC } from 'react';
import { PermissionDiagramGroups, type DiagramImage as DiagramImageType } from 'models';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Box, Image, Skeleton } from '@mantine/core';

import { ImageService } from '@diagram/services';

import { useDiagram } from '@diagram/hooks';

import { useFile } from 'hooks/use-file';

import { state as imageUploadPreviewState } from '@diagram/state/image-upload-preview';
import { useSelectedImages } from '@diagram/hooks/selection/use-selected-images';
import { SelectionService } from '@diagram/services/SelectionService';

import { Z_INDEX } from '../../diagram-z-index';
import cx from './DiagramImages.module.scss';

import { DiagramResizableElement } from '../diagram-textareas/DiagramResizableElement';

import { DraggingType } from 'types/DraggingType';
import { usePermission } from 'hooks/use-permission';
import { ImageToolbar } from '@diagram/components/diagram-images/components/ImageToolbar';

const DiagramImages: FC = () => {
    const { images = [] } = useDiagram();

    return (
        <React.Fragment>
            <DiagramImagePreview />
            {images.filter(Boolean).map((image) => (
                <DiagramImage id={image.id} key={image.id} />
            ))}
        </React.Fragment>
    );
};

const DiagramImagePreview: FC = () => {
    const {
        id,
        image: { ref: image },
        position,
        dimensions,
    } = useSnapshot(imageUploadPreviewState);

    return image ? (
        <Box
            className={cx.image}
            style={() => ({
                '--image-z-index': Z_INDEX.CANVAS.IMAGE_PREVIEW,
                '--image-left': `${position.x}px`,
                '--image-top': `${position.y}px`,
            })}
            key={id}
        >
            <Skeleton visible>
                <Image src={URL.createObjectURL(image)} w={dimensions.width} h={dimensions.height} alt="" />
            </Skeleton>
        </Box>
    ) : null;
};

const DiagramImage: FC<{
    id: DiagramImageType['id'];
}> = ({ id }) => {
    const { images = [] } = useDiagram();
    const image = images.find((image) => image.id === id)!;

    const { file } = useFile(image.file);
    const [loading, setLoading] = useState(true);

    const { isSelectedImage, isOnlySelectedImage } = useSelectedImages();
    const selected = isSelectedImage(image.id);
    const isPartOfSelection = selected && !isOnlySelectedImage(image.id);
    const canEdit = usePermission(PermissionDiagramGroups.EDIT);

    return (
        <DiagramResizableElement
            type={DraggingType.IMAGE}
            element={image}
            onResizeStop={(data) => {
                ImageService.resize(image.id, data.position, data.dimensions);
            }}
            lockAspectRatio
        >
            <Skeleton visible={loading}>
                <Image
                    src={file.url}
                    w="100%"
                    h="100%"
                    alt=""
                    onLoad={() => {
                        setLoading(false);
                        ImageService.resetImageUploadPreview(image.id);
                    }}
                    onContextMenu={(event) => {
                        event.preventDefault();

                        if (!canEdit) {
                            return;
                        }

                        if (isPartOfSelection) {
                            return;
                        }

                        SelectionService.selectImage(image.id);
                    }}
                />
            </Skeleton>
            {canEdit && <ImageToolbar image={image} />}
        </DiagramResizableElement>
    );
};

export { DiagramImages };
