import { FC } from 'react';

import { Box, Tooltip } from '@mantine/core';
import { useElementSize } from '@mantine/hooks';

import { LabelService } from '@diagram/services';

import { useConnectionByAnchor } from '@diagram/hooks';
import { useCanvas } from '@diagram/hooks/use-canvas';
import { useComponent } from 'hooks/use-component';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { DiagramComponentInstance } from 'models';

import cx from './ComponentInstanceLabels.module.scss';

import { CELL_HEIGHT, CELL_PADDING, CELL_WIDTH, ICON_WIDTH } from '@diagram/diagram-dimensions';
import { Z_INDEX } from '@diagram/diagram-z-index';
import { useSimulationState } from '@diagram/hooks/use-simulation-state';
import { useSimulation } from '@diagram/hooks/use-simulation';
import { SimulationHelpers } from '@diagram/helpers/SimulationHelpers';

import { useDiagramLiveData } from '@diagram/hooks/use-diagram-live-data';
import { LiveDataHelpers } from '@diagram/helpers/LiveDataHelpers';

const LINE_HEIGHT = 12;
const OFFSET_X = 2;

const ComponentInstanceLabels: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) => {
    const {
        position: { x, y },
        colSpan,
    } = componentInstance;

    const { component } = useComponent(componentInstance.componentId, {
        skipAccessCheck: true,
    });
    const { company: manufacturer } = useCompanyProfile(component?.manufacturer);

    const { active, activeTimestamp } = useSimulationState();
    const simulation = useSimulation(active);
    const simulationResult = simulation?.result?.componentInstances?.[componentInstance.id];

    const labels = LabelService.getComponentLabels({
        componentInstance,
        component,
        manufacturer,
    });

    if (simulationResult) {
        const stateOfChargeLabel = SimulationHelpers.getStateOfCharge(simulationResult, activeTimestamp);

        if (stateOfChargeLabel) {
            labels.push(stateOfChargeLabel);
        }

        const powerLosses = SimulationHelpers.getPowerLosses(simulationResult, activeTimestamp);

        if (powerLosses) {
            labels.push(powerLosses);
        }
    }

    const { data } = useDiagramLiveData();
    const liveDataForComponentInstance = LiveDataHelpers.getLiveDataForComponentInstance(data, componentInstance);

    if (liveDataForComponentInstance) {
        const stateOfChargeLabel = LiveDataHelpers.format.stateOfCharge(liveDataForComponentInstance);

        if (stateOfChargeLabel) {
            labels.push(`SoC: ${stateOfChargeLabel}`);
        }
    }

    const leftAnchorIsConnected = !!useConnectionByAnchor({
        componentInstanceId: componentInstance.id,
        edge: 'left',
        offset: 0,
    });

    const rightAnchorIsConnected = !!useConnectionByAnchor({
        componentInstanceId: componentInstance.id,
        edge: 'right',
        offset: 0,
    });

    const bothAnchorsConnected = leftAnchorIsConnected && rightAnchorIsConnected;

    if (!labels.length) {
        return null;
    }

    const labelsPosition = rightAnchorIsConnected || !leftAnchorIsConnected ? 'left' : 'right';

    const x_ = x * CELL_WIDTH + OFFSET_X + (labelsPosition === 'right' ? colSpan * CELL_WIDTH - CELL_PADDING : 0);
    const y_ = y * CELL_HEIGHT + CELL_PADDING;

    let shownLabels = labels;

    if (bothAnchorsConnected) {
        shownLabels = labels.slice(0, 1);
    }

    return (
        <Tooltip.Floating
            className={cx.tooltip}
            disabled={labels.length === shownLabels.length}
            label={labels.map((label, index) => (
                <Box
                    key={`component-${componentInstance.id}-tooltip-${index}`}
                    className={cx.tooltipLabel}
                    style={{
                        '--text-color': index > 0 ? 'var(--mantine-color-gray-5)' : 'var(--mantine-color-gray-9)',
                    }}
                >
                    {label}
                </Box>
            ))}
        >
            <Box>
                {shownLabels.map((label, index) => (
                    <LabelWithTooltip
                        key={`component-${componentInstance.id}-label-${index}`}
                        x={x_}
                        y={y_}
                        label={label}
                        index={index}
                        labelsPosition={labelsPosition}
                        disableTooltip={labels.length !== shownLabels.length}
                    />
                ))}
            </Box>
        </Tooltip.Floating>
    );
};

const LabelWithTooltip: FC<{
    x: number;
    y: number;
    index: number;
    label: string | null;
    labelsPosition: 'left' | 'right';
    disableTooltip?: boolean;
}> = ({ label, index, labelsPosition, x, y, disableTooltip }) => {
    const { width, ref } = useElementSize();
    const { width: innerWidth, ref: innerRef } = useElementSize();

    return (
        <Tooltip
            offset={2}
            className={cx.tooltip}
            disabled={disableTooltip || innerWidth <= width}
            label={
                <Box
                    className={cx.tooltipLabel}
                    style={{
                        '--text-color': index > 0 ? 'var(--mantine-color-gray-5)' : 'var(--mantine-color-gray-9)',
                    }}
                >
                    {label}
                </Box>
            }
        >
            <Box
                ref={ref}
                className={cx.label}
                style={{
                    '--left': `${x}px`,
                    '--top': `${2 + y + index * LINE_HEIGHT}px`,
                    '--width': `${ICON_WIDTH - 2 * OFFSET_X}px`,
                    '--text-align': labelsPosition === 'left' ? 'right' : 'left',
                    '--text-color': index > 0 ? 'var(--mantine-color-gray-5)' : 'var(--mantine-color-gray-9)',
                    '--z-index': Z_INDEX.CANVAS.LABELS,
                    '--z-index-hover': Z_INDEX.CANVAS.LABELS_HOVER,
                }}
            >
                <span ref={innerRef} className={cx.innerLabel} data-component-instance-label>
                    {label}
                </span>
            </Box>
        </Tooltip>
    );
};

const WrappedComponentInstanceLabels: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) => {
    const { labels } = useCanvas();
    const show = Object.values(labels).some(Boolean);

    return show ? <ComponentInstanceLabels componentInstance={componentInstance} /> : null;
};

export { WrappedComponentInstanceLabels as ComponentInstanceLabels };
