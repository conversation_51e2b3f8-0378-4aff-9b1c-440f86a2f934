import React, { FC } from 'react';

import { ConnectionHelpers } from '@diagram/helpers/ConnectionHelpers';

import { useCanvas } from '@diagram/hooks/use-canvas';
import { useMeasurementSystem } from 'hooks/use-measurement-system';
import { useConnectionContext } from '@diagram/components/diagram-connections/context';

import { ConnectionLabel } from './ConnectionLabel';
import { FormatHelpers } from 'helpers/formatters';
import { voltageConverter } from 'models';

const ConnectionLabelsCenter: FC = () => {
    const { connection, pointsAndPath, voltageType } = useConnectionContext();
    const measurementSystem = useMeasurementSystem();

    const canvas = useCanvas();

    if (!pointsAndPath) return null;

    const { points } = pointsAndPath;

    const labels = [];

    if (canvas.labels.connections.others) {
        labels.push(connection.label);
    }

    if (canvas.labels.connections.voltage) {
        labels.push(FormatHelpers.formatVoltage(connection.requirements.voltage));
    }

    if (canvas.labels.connections.current) {
        labels.push(FormatHelpers.formatCurrent(connection.requirements.current));
    }

    if (canvas.labels.connections.wireSize) {
        const numberOfCables = connection.multiple.parallel ?? 1;
        const wireSize = FormatHelpers.formatWireSize(connection.wireSize, measurementSystem);
        const length = FormatHelpers.formatLength(connection.length.value, measurementSystem);
        const resistance = connection.resistance.value;

        const parts = [
            numberOfCables > 1 ? `${numberOfCables}x ${wireSize}` : wireSize,
            length,
            resistance ? `${resistance} Ω` : '',
        ]
            .filter(Boolean)
            .join(' / ');

        labels.push(parts);
    }

    if (canvas.labels.connections.lines) {
        const { lines, PE } = ConnectionHelpers.getLinesLabel(connection, voltageType);

        if (lines) {
            labels.push(lines);
        }

        if (PE) {
            labels.push('PE');
        }
    }

    return (
        <ConnectionLabel
            position="center"
            points={points}
            edge={connection.from.edge}
            labels={labels.map((label) => ({ label }))}
        />
    );
};

const WrappedConnectionLabelsCenter = () => {
    const { labels } = useCanvas();
    const show = Object.values(labels).some(Boolean);

    return show ? <ConnectionLabelsCenter /> : null;
};

export { WrappedConnectionLabelsCenter as ConnectionLabelsCenter };
