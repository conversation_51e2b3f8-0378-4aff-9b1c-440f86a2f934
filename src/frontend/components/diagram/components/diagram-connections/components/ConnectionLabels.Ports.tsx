import React, { FC } from 'react';

import { get, isNumber } from 'radash';

import { DiagramComponentInstance, PortControlMethods, powerConverter } from 'models';

import { FormatHelpers } from 'helpers/formatters';

import { useCanvas } from '@diagram/hooks/use-canvas';
import { useConnectionContext } from '@diagram/components/diagram-connections/context';
import { useSelectedComponentInstances } from '@diagram/hooks/selection/use-selected-component-instances';
import { useDiagramLiveData } from '@diagram/hooks/use-diagram-live-data';

import { ConnectionLabel } from './ConnectionLabel';

import { LiveDataHelpers } from '@diagram/helpers/LiveDataHelpers';

const ConnectionLabelsPorts: FC = () => {
    const { labels } = useCanvas();
    const { connection, fromComponentInstance, toComponentInstance, voltageType, pointsAndPath } =
        useConnectionContext();

    const { selection, isSelectedComponentInstance } = useSelectedComponentInstances();

    const { from, to } = connection;

    const fromPort = from?.port !== null ? fromComponentInstance?.configuration?.ports?.[from.port] : null;
    const toPort = to?.port !== null ? toComponentInstance?.configuration?.ports?.[to.port] : null;

    const getPower = (component: DiagramComponentInstance, port: number | null) => {
        if (!isNumber(port)) return '';

        const multiplier = component.multiple?.amount ?? 1;

        const value = get(component, `specifications.electrical.ports[${port}][${voltageType}].power.nom`);
        const unit = get(component, `specifications.electrical.ports[${port}][${voltageType}].power.unit`);

        if (value && unit) {
            return FormatHelpers.formatValue(+value * multiplier, powerConverter);
        }

        return '';
    };

    const getPortNumber = (component: DiagramComponentInstance, port: number | null) => {
        const nbPorts = (get(component, `specifications.electrical.ports`) as any[])?.length ?? 0;
        const componentSelected = isSelectedComponentInstance(component.id);

        if (!componentSelected) return '';
        if (selection.length > 1) return '';

        if (nbPorts < 2) return '';
        if (!isNumber(port)) return '';

        if (port < 20) {
            return String.fromCharCode(0x2460 + port);
        }

        return `${port + 1}`;
    };

    if (!pointsAndPath) return null;

    const { points } = pointsAndPath;

    const fromPortControlMethod =
        from?.port !== null ? fromComponentInstance?.configuration?.ports?.[from.port]?.controlMethod : null;
    const toPortControlMethod =
        to?.port !== null ? toComponentInstance?.configuration?.ports?.[to.port]?.controlMethod : null;

    const fromPortControlMethodAbbreviation =
        fromPortControlMethod && PortControlMethods.abbreviations[fromPortControlMethod];
    const toPortControlMethodAbbreviation =
        toPortControlMethod && PortControlMethods.abbreviations[toPortControlMethod];

    const fromLabels: any[] = [
        {
            label: fromPort?.label || getPortNumber(fromComponentInstance, from?.port),
            isIcon: !fromPort?.label,
        },
    ];

    const toLabels: any[] = [
        {
            label: toPort?.label || getPortNumber(toComponentInstance, to?.port),
            isIcon: !toPort?.label,
        },
    ];

    const { data, activated: liveDataActivated } = useDiagramLiveData();
    const fromLiveData = LiveDataHelpers.getLiveDataForComponentInstance(data, fromComponentInstance);
    const toLiveData = LiveDataHelpers.getLiveDataForComponentInstance(data, toComponentInstance);

    if (fromLiveData && typeof from?.port === 'number') {
        if (labels.ports.voltage) {
            fromLabels.push({
                label: LiveDataHelpers.format.portVoltage(fromLiveData, from.port),
            });
        }

        if (labels.ports.current) {
            fromLabels.push({
                label: LiveDataHelpers.format.portCurrent(fromLiveData, from.port),
            });
        }

        if (labels.ports.power) {
            fromLabels.push({
                label: LiveDataHelpers.format.portPower(fromLiveData, from.port),
            });
        }
    } else if (!liveDataActivated) {
        if (labels.ports.power) {
            fromLabels.push({
                label: getPower(fromComponentInstance, from?.port),
            });
        }
    }

    if (toLiveData && typeof to?.port === 'number') {
        if (labels.ports.voltage) {
            toLabels.push({
                label: LiveDataHelpers.format.portVoltage(toLiveData, to.port),
            });
        }

        if (labels.ports.current) {
            toLabels.push({
                label: LiveDataHelpers.format.portCurrent(toLiveData, to.port),
            });
        }

        if (labels.ports.power) {
            toLabels.push({
                label: LiveDataHelpers.format.portPower(toLiveData, to.port),
            });
        }
    } else if (!liveDataActivated) {
        if (labels.ports.power) {
            toLabels.push({
                label: getPower(toComponentInstance, to?.port),
            });
        }
    }

    if (labels.ports.others) {
        fromLabels.push({
            label: fromPortControlMethodAbbreviation,
            underline: true,
        });

        toLabels.push({
            label: toPortControlMethodAbbreviation,
            underline: true,
        });
    }

    return (
        <>
            {[
                { edge: from.edge, labels: fromLabels, position: 'start' as any },
                { edge: to.edge, labels: toLabels, position: 'end' as any },
            ].map((item) => (
                <ConnectionLabel points={points} {...item} key={item.position} />
            ))}
        </>
    );
};

const WrappedConnectionLabelsPorts = () => {
    const { labels } = useCanvas();
    const show = Object.values(labels.ports).some(Boolean);

    return show ? <ConnectionLabelsPorts /> : null;
};

export { WrappedConnectionLabelsPorts as ConnectionLabelsPorts };
