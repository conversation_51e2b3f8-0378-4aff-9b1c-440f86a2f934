import type { FC } from 'react';
import type { DiagramConnection } from 'models';

import { TbBackspace, TbCodeDots, TbCodePlus } from 'react-icons/tb';
import { IoTrashOutline } from 'react-icons/io5';

import { DiagramToolbar } from '@diagram/components/diagram-toolbar/DiagramToolbar';

import { ConnectionService } from '@diagram/services/ConnectionService';
import { SelectionService } from '@diagram/services/SelectionService';

import { useSelectedConnections } from '@diagram/hooks/selection/use-selected-connections';

import { ConnectionToolbarVoltageType } from './ConnectionToolbar.VoltageType';
import { ConnectionToolbarPowerFlow } from './ConnectionToolbar.PowerFlow';
import { ConnectionToolbarWireSize } from './ConnectionToolbar.WireSize';

import { Form } from 'components/forms/Form';
import { AutoSave } from 'components/forms/AutoSave';
import { DiagramSyncService } from '@diagram/services/DiagramSyncService';
import { useComponentInstance } from '@diagram/hooks';
import { useCopyPasteSpecifications } from '@diagram/hooks/use-copy-paste-specifications';

const ConnectionToolbar: FC<{
    connection: DiagramConnection;
    position: 'top' | 'right';
}> = ({ connection, position }) => {
    const { isOnlySelectedConnection } = useSelectedConnections();

    const opened = isOnlySelectedConnection(connection.id);
    const connectionDirection = position === 'top' ? 'horizontal' : 'vertical';

    const fromComponentInstance = useComponentInstance(connection.from.componentInstanceId)!;
    const toComponentInstance = useComponentInstance(connection.to.componentInstanceId)!;

    const { connection: copiedSpecifications } = useCopyPasteSpecifications();

    return (
        <DiagramToolbar opened={opened} position={position} key={connection.id}>
            <Form<DiagramConnection>
                defaultValues={connection}
                onSubmit={(values, { getUpdates, hasUpdates }) => {
                    if (!hasUpdates) {
                        return;
                    }

                    const updates = getUpdates();

                    ConnectionService.update({
                        id: connection.id,
                        ...updates,
                    });

                    DiagramSyncService.save();
                }}
            >
                <AutoSave />

                <DiagramToolbar.Input name="label" placeholder="Label" />
                <DiagramToolbar.Divider />
                <ConnectionToolbarVoltageType
                    connection={connection}
                    fromComponentInstance={fromComponentInstance}
                    toComponentInstance={toComponentInstance}
                />
                <DiagramToolbar.Divider />
                <ConnectionToolbarPowerFlow
                    connection={connection}
                    fromComponentInstance={fromComponentInstance}
                    toComponentInstance={toComponentInstance}
                    direction={connectionDirection}
                />
                <DiagramToolbar.Divider />
                <DiagramToolbar.Input
                    name="requirements.voltage.value"
                    placeholder="Voltage"
                    parse={(value) => (value ? +value : null)}
                    stringify={(value) => (value ? value.toString() : '')}
                    suffix=" V"
                />
                <DiagramToolbar.Divider />
                <DiagramToolbar.Input
                    name="requirements.current.value"
                    placeholder="Current"
                    parse={(value) => (value ? +value : null)}
                    stringify={(value) => (value ? value.toString() : '')}
                    suffix=" A"
                />
                <DiagramToolbar.Divider />
                <DiagramToolbar.IconButton
                    tooltip="Copy specifications"
                    onClick={() => {
                        ConnectionService.copySpecifications(connection.id);
                    }}
                >
                    <TbCodeDots />
                </DiagramToolbar.IconButton>
                <DiagramToolbar.IconButton
                    tooltip={copiedSpecifications ? 'Paste specifications' : 'No specifications to paste'}
                    onClick={() => {
                        ConnectionService.pasteSpecifications(connection.id);
                        DiagramSyncService.save();
                    }}
                    disabled={!copiedSpecifications}
                >
                    <TbCodePlus />
                </DiagramToolbar.IconButton>
                <DiagramToolbar.Divider />
                <DiagramToolbar.IconButton
                    tooltip="Delete"
                    shortcut={<TbBackspace />}
                    onClick={() => {
                        ConnectionService.delete(connection.id);
                        SelectionService.clear();
                    }}
                >
                    <IoTrashOutline />
                </DiagramToolbar.IconButton>
            </Form>
        </DiagramToolbar>
    );
};

export { ConnectionToolbar };
