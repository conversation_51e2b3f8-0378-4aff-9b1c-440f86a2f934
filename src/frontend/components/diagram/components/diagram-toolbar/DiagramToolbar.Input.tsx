import type { FC } from 'react';

import { useController } from 'react-hook-form';

import { Box } from '@mantine/core';

import cx from './DiagramToolbar.module.scss';

const DiagramToolbarInput: FC<{
    name: string;
    placeholder: string;
    stringify?: (value: number | string | null) => string;
    parse?: (value: string) => number | string | null;
    suffix?: string;
}> = ({ name, placeholder, stringify = (value) => value || '', parse = (value) => value, suffix = '' }) => {
    const { field } = useController({
        name,
    });

    return (
        <Box className={cx.text}>
            <Box className={cx.autoInput}>
                <input
                    {...field}
                    value={stringify(field.value)}
                    onChange={(event) => {
                        field.onChange(parse(event.target.value));
                    }}
                    autoComplete="off"
                    className={cx.autoInputInput}
                    placeholder={placeholder}
                />
                <span className={cx.autoInputSizer}>{field.value || placeholder}</span>
                {suffix && field.value && <span>{suffix}</span>}
            </Box>
        </Box>
    );
};

export { DiagramToolbarInput };
