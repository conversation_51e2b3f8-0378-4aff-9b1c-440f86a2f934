import React, { FC, useState } from 'react';

import { Table, MultiSelect, Button, UnstyledButton, Group } from '@mantine/core';

import {
    TbArrowsDiagonal,
    TbArrowsDiagonalMinimize2,
    TbArrowsDownUp,
    TbSortAscending,
    TbSortDescending,
} from 'react-icons/tb';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';
import { DiagramInputTable } from 'components/diagram/components/diagram-input-table/DiagramInputTable';

import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';

import { useSidebarFullscreen } from 'components/diagram/hooks/use-sidebar-fullscreen';
import { useSidebarWidth } from 'components/diagram/hooks/use-sidebar-width';

import { useDiagram } from 'components/diagram/hooks';
import { useSelectedColumns } from './use-selected-columns';

import { download, generateCsv, mkConfig } from 'export-to-csv';

import { columns } from './WireSchedule.columns';
import cx from './WireSchedule.module.scss';
import { useMeasurementSystem } from 'hooks/use-measurement-system';

const WireSchedule = () => {
    useSidebarWidth(800);
    const [fullscreen, toggleFullscreen] = useSidebarFullscreen();

    useMeasurementSystem();

    const { connections } = useDiagram();

    const [sorting, setSorting] = useState({
        key: 'id',
        direction: 'asc',
    });

    const { selectedColumns, setSelectedColumns } = useSelectedColumns();

    const shownColumns = columns.filter((column) => selectedColumns.includes(column.key));
    const rows: string[][] = [];

    Object.values(connections).forEach((connection) => {
        const row: string[] = [];

        const fromComponentInstance = ComponentInstanceService.getById(connection.from.componentInstanceId);
        const toComponentInstance = ComponentInstanceService.getById(connection.to.componentInstanceId);

        shownColumns.forEach((column) => {
            row.push(column.getter(connection, fromComponentInstance, toComponentInstance) || '-');
        });

        rows.push(row);
    });

    const sortingKeyIndex = shownColumns.findIndex((column) => column.key === sorting.key);

    if (sortingKeyIndex !== -1) {
        if (sorting.direction === 'asc') {
            rows.sort((a, b) => a[sortingKeyIndex].localeCompare(b[sortingKeyIndex]));
        } else if (sorting.direction === 'desc') {
            rows.sort((a, b) => b[sortingKeyIndex].localeCompare(a[sortingKeyIndex]));
        }
    }

    const downloadAsCSV = () => {
        const csvConfig = mkConfig({
            filename: 'wire-schedule.csv',
            useKeysAsHeaders: true,
            replaceUndefinedWith: '',
        });

        const data: { [key: string]: string }[] = [];

        rows.map((row) => {
            const item: { [key: string]: string } = {};

            shownColumns.forEach((column, index) => {
                item[column.label] = row[index];
            });

            data.push(item);
        });

        const csv = generateCsv(csvConfig)(data);
        download(csvConfig)(csv);
    };

    return (
        <>
            <DiagramSidebar.SimpleHeader
                rightSection={
                    <DiagramSidebar.Button onClick={toggleFullscreen}>
                        {fullscreen ? <TbArrowsDiagonalMinimize2 /> : <TbArrowsDiagonal />}
                    </DiagramSidebar.Button>
                }
            >
                Wire Schedule
            </DiagramSidebar.SimpleHeader>
            <DiagramSidebar.Section>
                <MultiSelect
                    label="Columns"
                    data={columns.map((column) => ({
                        value: column.key,
                        label: column.label,
                    }))}
                    value={selectedColumns}
                    onChange={setSelectedColumns}
                    searchable
                    size="xs"
                />
                <Button onClick={downloadAsCSV} variant="gradient" size="xs" mt="xs">
                    Download as CSV
                </Button>
            </DiagramSidebar.Section>
            <DiagramSidebar.Divider />
            <DiagramSidebar.Section>
                <DiagramInputTable className={cx.table}>
                    <Table.Thead>
                        <Table.Tr>
                            {shownColumns.map((column) => (
                                <Table.Th key={column.key}>
                                    <Group justify="space-between" align="center" gap="xs" wrap="nowrap">
                                        {column.label}
                                        {sorting.key !== column.key && (
                                            <UnstyledButton
                                                onClick={() => {
                                                    setSorting({ key: column.key, direction: 'asc' });
                                                }}
                                            >
                                                <TbArrowsDownUp />
                                            </UnstyledButton>
                                        )}
                                        {sorting.key === column.key && sorting.direction === 'asc' && (
                                            <UnstyledButton
                                                onClick={() => {
                                                    setSorting({ key: column.key, direction: 'desc' });
                                                }}
                                            >
                                                <TbSortDescending />
                                            </UnstyledButton>
                                        )}
                                        {sorting.key === column.key && sorting.direction === 'desc' && (
                                            <UnstyledButton
                                                onClick={() => {
                                                    setSorting({ key: 'id', direction: 'asc' });
                                                }}
                                            >
                                                <TbSortAscending />
                                            </UnstyledButton>
                                        )}
                                    </Group>
                                </Table.Th>
                            ))}
                        </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                        {rows.map((row, index) => (
                            <Row columns={row} key={index + row.join('-')} />
                        ))}
                    </Table.Tbody>
                </DiagramInputTable>
            </DiagramSidebar.Section>
        </>
    );
};

const Row: FC<{
    columns: string[];
}> = ({ columns }) => (
    <Table.Tr>
        {columns.map((column, index) => (
            <Table.Td key={index + column}>
                <span data-wire-schedule-column={column}>{column}</span>
            </Table.Td>
        ))}
    </Table.Tr>
);

export { WireSchedule };
