import { useEffect } from 'react';
import { useSnapshot, proxy } from 'valtio';

import { LocalStorageService } from 'services/LocalStorageService';

const state = proxy({
    initialized: false,
    selectedColumns: [] as string[],
});

const localStorageKey = 'wire-schedule-selected-columns';

const useSelectedColumns = () => {
    const { selectedColumns } = useSnapshot(state);

    useEffect(() => {
        if (state.initialized) {
            return;
        }

        const fallback = [
            'id',
            'voltageType',
            'from',
            'to',
            'amount',
            'crossSection',
            'length',
            'voltage',
            'current',
            'notes',
        ];
        const initial = LocalStorageService.get(localStorageKey) || fallback;

        state.initialized = true;
        state.selectedColumns = initial;
    }, []);

    const setSelectedColumns = (columns: string[]) => {
        state.selectedColumns = columns;
        LocalStorageService.store(localStorageKey, columns);
    };

    return {
        selectedColumns,
        setSelectedColumns,
    };
};

export { useSelectedColumns };
