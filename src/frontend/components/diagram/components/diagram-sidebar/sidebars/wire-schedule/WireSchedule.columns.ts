import {
    ConductorMaterial,
    ConnectionCores,
    DiagramComponentInstance,
    DiagramConnection,
    InsulationMaterial,
} from 'models';

import { ComponentInstanceService } from 'components/diagram/services';
import { MeasurementSystemService } from 'services/MeasurementSystemService';

import { FormatHelpers } from 'helpers/formatters';
import { ConnectionHelpers } from 'components/diagram/helpers/ConnectionHelpers';

export type Column = {
    key: string;
    label: string;
    getter: (
        connection: DiagramConnection,
        fromComponentInstance?: DiagramComponentInstance,
        toComponentInstance?: DiagramComponentInstance,
    ) => string;
};

export const columns: Column[] = [
    {
        key: 'id',
        label: 'ID',
        getter: (connection: DiagramConnection) => {
            let hash = 216613626139583;

            for (let i = 0; i < connection.id.length; i++) {
                hash ^= connection.id.charCodeAt(i);
                hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);
            }

            return (Math.abs(hash) % 100_000).toString();
        },
    },
    {
        key: 'voltageType',
        label: 'Voltage Type',
        getter: (connection: DiagramConnection, fromComponentInstance, toComponentInstance) => {
            if (fromComponentInstance && toComponentInstance) {
                return ConnectionHelpers.getVoltageType(connection, fromComponentInstance, toComponentInstance) || '';
            }

            return '';
        },
    },
    {
        key: 'from',
        label: 'From',
        getter: (connection: DiagramConnection) => {
            const componentInstance = ComponentInstanceService.getById(connection.from.componentInstanceId);

            return componentInstance?.designator || '-';
        },
    },
    {
        key: 'to',
        label: 'To',
        getter: (connection: DiagramConnection) => {
            const componentInstance = ComponentInstanceService.getById(connection.to.componentInstanceId);

            return componentInstance?.designator || '-';
        },
    },
    {
        key: 'amount',
        label: 'Amount',
        getter: (connection: DiagramConnection) => {
            return `${connection.multiple.parallel || 1}`;
        },
    },
    {
        key: 'crossSection',
        label: 'Cross Section',
        getter: (connection: DiagramConnection) => {
            return FormatHelpers.formatWireSize(connection.wireSize, MeasurementSystemService.get());
        },
    },
    {
        key: 'length',
        label: 'Length',
        getter: (connection: DiagramConnection) => {
            const measurementSystem = MeasurementSystemService.get();

            return FormatHelpers.formatLength(connection.length.value, measurementSystem || undefined);
        },
    },
    {
        key: 'conductorMaterial',
        label: 'Conductor Material',
        getter: (connection: DiagramConnection) => {
            return FormatHelpers.formatOption(ConductorMaterial.options, connection.conductorMaterial);
        },
    },
    {
        key: 'insulationMaterial',
        label: 'Insulation Material',
        getter: (connection: DiagramConnection) => {
            return FormatHelpers.formatOption(InsulationMaterial.options, connection.insulationMaterial);
        },
    },
    {
        key: 'cores',
        label: 'Cores',
        getter: (connection: DiagramConnection) => {
            return (
                {
                    [ConnectionCores.SINGLE]: 'Single',
                    [ConnectionCores.MULTI]: 'Multi',
                    [ConnectionCores.MULTI_PE]: 'Multi + PE',
                }[connection.cores] || ''
            );
        },
    },
    {
        key: 'lines',
        label: 'Lines',
        getter: (connection: DiagramConnection, fromComponentInstance, toComponentInstance) => {
            if (fromComponentInstance && toComponentInstance) {
                const voltageType = ConnectionHelpers.getVoltageType(
                    connection,
                    fromComponentInstance,
                    toComponentInstance,
                );

                if (voltageType) {
                    return Object.entries(connection.lines[voltageType])
                        .filter(([, active]) => active)
                        .map(([line]) => line)
                        .join(' ');
                }
            }

            return '';
        },
    },
    {
        key: 'numberOfConductors',
        label: 'Number of Conductors',
        getter: (connection: DiagramConnection, fromComponentInstance, toComponentInstance) => {
            if (fromComponentInstance && toComponentInstance) {
                const voltageType = ConnectionHelpers.getVoltageType(
                    connection,
                    fromComponentInstance,
                    toComponentInstance,
                );

                return ConnectionHelpers.getNumberOfConductors(connection, voltageType).toString();
            }

            return '0';
        },
    },
    {
        key: 'voltage',
        label: 'Voltage',
        getter: (connection: DiagramConnection) => {
            return FormatHelpers.formatVoltage(connection.requirements.voltage);
        },
    },
    {
        key: 'current',
        label: 'Current',
        getter: (connection: DiagramConnection) => {
            return FormatHelpers.formatCurrent(connection.requirements.current);
        },
    },
    {
        key: 'resistance',
        label: 'Resistance',
        getter: (connection: DiagramConnection) => {
            return `${connection.resistance.value} Ω`;
        },
    },
    {
        key: 'temperature',
        label: 'Temperature',
        getter: (connection: DiagramConnection) => {
            return `${connection.installation.temperature.value}°C`;
        },
    },
    {
        key: 'notes',
        label: 'Notes',
        getter: (connection: DiagramConnection) => connection.notes,
    },
];
