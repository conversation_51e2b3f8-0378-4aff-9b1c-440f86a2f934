import React, { useState, FC } from 'react';

import { Flex, Loader, Text, ActionIcon, Stack } from '@mantine/core';
import { IoTrashOutline } from 'react-icons/io5';

import { ProjectService } from 'services/ProjectService';

import { useProject } from 'hooks/use-project';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { Avatar } from 'components/avatar/Avatar';
import { CompanyProfile } from 'models';
import { DiagramSidebar } from '@diagram/components/diagram-sidebar/DiagramSidebar';

const Companies: FC = () => {
    const { project } = useProject(null, { depth: 1 }) as any;

    if (!project) return null;

    const manufacturers = project.collaborators.manufacturers;

    const handleDeleteManufacturer = async (id: string) => {
        if (!project) return;

        await ProjectService.deleteManufacturer(project.id, id);
    };

    return manufacturers.length ? (
        <DiagramSidebar.Section title="Companies who offer in-app support for this diagram">
            <Stack>
                {manufacturers.map((manufacturer: string) => (
                    <Company key={manufacturer} manufacturerId={manufacturer} handleDelete={handleDeleteManufacturer} />
                ))}
            </Stack>
        </DiagramSidebar.Section>
    ) : null;
};

const Company = ({
    manufacturerId,
    distributorId,
    handleDelete,
}: {
    manufacturerId?: CompanyProfile['id'];
    distributorId?: CompanyProfile['id'];
    handleDelete: (id: string) => Promise<void>;
}) => {
    const { mutate } = useProject(null, { depth: 1 });
    const [isUpdating, setIsUpdating] = useState(false);

    const { company: manufacturer } = useCompanyProfile(manufacturerId);
    const { company: distributor } = useCompanyProfile(distributorId);

    const company = manufacturer || distributor;

    const onDelete = async () => {
        setIsUpdating(true);

        if (company) {
            await handleDelete(company?.id);
            await mutate();
        }

        setIsUpdating(false);
    };

    return (
        <Flex justify="space-between">
            <Flex align="center" gap="xs">
                <Avatar company={company} size="xs" />
                <Text fz="sm">{company?.name}</Text>
            </Flex>
            <ActionIcon size="sm" variant="subtle" color="red" type="button" onClick={onDelete}>
                {isUpdating ? <Loader size={12} /> : <IoTrashOutline size={12} />}
            </ActionIcon>
        </Flex>
    );
};

export { Companies };
