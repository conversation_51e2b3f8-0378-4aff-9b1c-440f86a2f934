import React, { useState, FC } from 'react';

import { Badge, Flex, Text, Stack } from '@mantine/core';
import { openContextModal } from '@mantine/modals';

import { IoPersonAddOutline, IoPersonRemoveOutline } from 'react-icons/io5';

import { TextHelpers } from 'helpers/TextHelpers';
import { ProjectService } from 'services/ProjectService';

import { useUser } from 'hooks/use-user';
import { useCurrentProject } from 'hooks/use-current-project';

import { SettingsDropdown } from 'components/dropdown/SettingsDropdown';

import { Avatar } from 'components/avatar/Avatar';
import { FeatureKey, useFeatureAccess } from 'hooks/use-feature-access';

const Collaborators: FC = () => {
    const project = useCurrentProject();
    const collaborators = project?.collaborators.users || [];

    return collaborators.length > 0 ? (
        <Stack>
            {collaborators.map((collaborator) => (
                <Collaborator collaborator={collaborator} key={collaborator.user} />
            ))}
        </Stack>
    ) : null;
};

const Collaborator = ({ collaborator }: any) => {
    const project = useCurrentProject()!;
    const { user } = useUser(collaborator.user);

    const [loading, setLoading] = useState(false);

    const hasEditAccess = collaborator.permissions.includes('project.edit');
    const featureEnabled = useFeatureAccess(FeatureKey.INVITE_EDITOR_COLLABORATOR);

    const handleGiveViewAccess = async () => {
        setLoading(true);
        await ProjectService.editCollaborator(project!.id, collaborator.user, ['project.view']);
        setLoading(false);
    };

    const handleGiveEditAccess = async () => {
        if (!featureEnabled) {
            openContextModal({
                modal: 'simpleSubscriptionModal',
                withCloseButton: false,
                innerProps: {
                    message: <>Upgrade your plan to invite others to edit the same project together.</>,
                },
                size: 'md',
            });

            return;
        }

        setLoading(true);
        await ProjectService.editCollaborator(project!.id, collaborator.user, ['project.view', 'project.edit']);
        setLoading(false);
    };

    const handleDelete = async () => {
        setLoading(true);
        await ProjectService.deleteCollaborator(project!.id, collaborator.user);
    };

    if (!user) return null;

    return (
        <Flex justify="space-between">
            <Flex align="center" gap="xs">
                <Avatar user={user} />
                <Text fz="sm">{TextHelpers.getTextWithEllipsis(user.name ?? user.email, 25)}</Text>
            </Flex>
            <Flex align="center" gap="xs">
                {!hasEditAccess && (
                    <Badge size="xs" radius="xs">
                        view only
                    </Badge>
                )}
                <SettingsDropdown loading={loading}>
                    {!hasEditAccess && (
                        <SettingsDropdown.Item leftSection={<IoPersonAddOutline />} onClick={handleGiveEditAccess}>
                            Grant edit access
                        </SettingsDropdown.Item>
                    )}
                    {hasEditAccess && (
                        <SettingsDropdown.Item leftSection={<IoPersonRemoveOutline />} onClick={handleGiveViewAccess}>
                            Remove edit access
                        </SettingsDropdown.Item>
                    )}
                    <SettingsDropdown.Delete onClick={handleDelete}>Remove access</SettingsDropdown.Delete>
                </SettingsDropdown>
            </Flex>
        </Flex>
    );
};

export { Collaborators };
