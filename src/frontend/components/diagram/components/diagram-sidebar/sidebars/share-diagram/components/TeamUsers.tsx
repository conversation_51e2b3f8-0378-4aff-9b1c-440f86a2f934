import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { Tooltip, Group, Stack, Text, ActionIcon } from '@mantine/core';
import { IoAddSharp } from 'react-icons/io5';

import { useUser } from 'hooks/use-user';

import { Avatar } from 'components/avatar/Avatar';
import { useProjectTeam } from 'hooks/use-project-team';

const TeamUsers: FC = () => {
    const { team } = useProjectTeam();

    return team && team.users.length > 0 ? (
        <Stack gap="xs">
            <Text fz="xs" c="dimmed">
                This diagram is also visible to your team members
            </Text>
            <Group gap={4}>
                {team.users.map(({ user }) => (
                    <TeamUser userId={user} key={user} />
                ))}
                <Tooltip label="Invite team user">
                    <ActionIcon
                        component={Link}
                        href="/account#team"
                        target="_blank"
                        size={26}
                        radius={99}
                        variant="light"
                        color="primart"
                    >
                        <IoAddSharp />
                    </ActionIcon>
                </Tooltip>
            </Group>
        </Stack>
    ) : null;
};

const TeamUser = ({ userId }: { userId: string }) => {
    const { user } = useUser(userId);

    if (!user) return null;

    return <Avatar user={user} tooltip />;
};

export { TeamUsers };
