import React, { FC } from 'react';

import { LoadingOverlay, Tabs } from '@mantine/core';

import { useComponent } from 'hooks/use-component';

import { DiagramSidebar } from '@diagram';
import { useComponentInstance, useDiagramSidebarTabs } from '@diagram/hooks';

import { useSelectedComponentInstances } from '@diagram/hooks/selection/use-selected-component-instances';

import { DiagramSidebarErrorBoundary } from '@diagram/components/diagram-sidebar/DiagramSidebar.ErrorBoundary';

import {
    Header,
    Specifications,
    ComponentSuggestions,
    CompatibleComponents,
    Notes,
    Files,
    Simulation,
    DiagramComponentChat,
} from './sections';

import { LiveData } from './tabs/LiveData';
import { Networking } from './tabs/Networking';

import { ComponentImages } from './sections/ComponentImages';

import { EditComponentInstanceComments } from './EditComponentInstance.Comments';
import { SimulationHelpers } from '@diagram/helpers/SimulationHelpers';
import { SidebarService } from '@diagram/services/SidebarService';
import { useSidebar } from '@diagram/hooks/use-sidebar';
import { SidebarTab } from '@diagram/state/sidebar';
import { useCurrentUser } from 'hooks/use-current-user';
import { SearchBox } from 'components/search-box';
import { ProductSearchService } from '@diagram/services/ProductSearchService';

const EditComponentInstance: FC = () => {
    const user = useCurrentUser();

    const { selection } = useSelectedComponentInstances();
    const componentInstance = useComponentInstance(selection[0]);
    const { component, isLoading } = useComponent(componentInstance?.componentId ?? '', {
        skipAccessCheck: true,
    });

    const { defaultTabs } = useDiagramSidebarTabs('componentInstance');
    const { editComponentInstanceTab } = useSidebar();

    if (!componentInstance || selection.length !== 1) {
        return null;
    }

    if (isLoading) {
        return <LoadingOverlay visible />;
    }

    if (!SimulationHelpers.componentHasSimulationFields(componentInstance)) {
        const simulationTabIndex = defaultTabs.findIndex((tab) => tab.value === SidebarTab.SIMULATION);

        if (simulationTabIndex > -1) {
            defaultTabs.splice(simulationTabIndex, 1);
        }
    }

    const tabExists = defaultTabs.some((tab) => tab.value === editComponentInstanceTab);

    if (!tabExists) {
        SidebarService.setEditComponentInstanceTab(SidebarTab.SPECIFICATIONS);

        return null;
    }

    return (
        <>
            <DiagramSidebar.Section padding={0}>
                <Header componentInstance={componentInstance} />
            </DiagramSidebar.Section>
            <Tabs
                value={editComponentInstanceTab}
                onChange={(value) => {
                    SidebarService.setEditComponentInstanceTab(value as SidebarTab);
                }}
            >
                <DiagramSidebar.Divider />
                <DiagramSidebar.Tabs tabs={defaultTabs} />
                <DiagramSidebar.Divider />

                <Tabs.Panel value="specifications">
                    {!componentInstance.componentId && (
                        <DiagramSidebar.Section pb={0}>
                            <SearchBox
                                placeholder="Search product catalog"
                                onFocus={() => ProductSearchService.openProductSearchFor(componentInstance)}
                            />
                        </DiagramSidebar.Section>
                    )}

                    <DiagramSidebarErrorBoundary identifier={componentInstance.id}>
                        <Specifications componentInstance={componentInstance} />
                        {user && component && <ComponentImages component={component} />}
                        {user && !componentInstance.componentId && (
                            <ComponentSuggestions componentInstance={componentInstance} />
                        )}
                        {user && componentInstance.componentId && (
                            <CompatibleComponents componentInstance={componentInstance} />
                        )}
                    </DiagramSidebarErrorBoundary>
                </Tabs.Panel>

                {user?.developer && (
                    <Tabs.Panel value="liveData">
                        <LiveData componentInstance={componentInstance} />
                    </Tabs.Panel>
                )}

                <Tabs.Panel value="networking">
                    <Networking componentInstance={componentInstance} />
                </Tabs.Panel>

                {user && (
                    <Tabs.Panel value="ai">
                        <DiagramComponentChat component={component} componentInstance={componentInstance} />
                    </Tabs.Panel>
                )}

                <Tabs.Panel value="notes">
                    <Notes componentInstance={componentInstance} />
                </Tabs.Panel>

                <Tabs.Panel value="simulation">
                    <DiagramSidebarErrorBoundary identifier={componentInstance.id}>
                        <Simulation componentInstance={componentInstance} />
                    </DiagramSidebarErrorBoundary>
                </Tabs.Panel>

                {user && (
                    <Tabs.Panel value="comments">
                        <EditComponentInstanceComments componentInstance={componentInstance} />
                    </Tabs.Panel>
                )}

                {user && (
                    <Tabs.Panel value="files">
                        <Files componentInstance={componentInstance} />
                    </Tabs.Panel>
                )}
            </Tabs>
        </>
    );
};

export { EditComponentInstance };
