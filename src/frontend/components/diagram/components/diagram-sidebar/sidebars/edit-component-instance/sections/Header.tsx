import React, { FC } from 'react';

import Link from 'next/link';
import { Anchor, Box, Flex, Grid, Modal, Stack, Text, Transition, UnstyledButton } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoOpenOutline } from 'react-icons/io5';

import { EditComponentInstanceContextMenu } from '../EditComponentInstance.ContextMenu';
import { ComponentInstanceService } from '@diagram/services';

import { Component, DiagramComponentInstance, FeatureKey } from 'models';
import { useComponent } from 'hooks/use-component';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { TextField } from 'components/forms/fields/TextField';

import cx from './Header.module.scss';
import { useWatch } from 'react-hook-form';
import { BsBookmarkPlus } from 'react-icons/bs';
import { DiagramSidebar } from '@diagram/components/diagram-sidebar/DiagramSidebar';
import { useFeatureAccess } from 'hooks/use-feature-access';
import { useIsTeamComponent } from '@diagram/hooks/use-is-team-component';

import { SubscriptionOnboardingMessage } from 'components/subscriptions/SubscriptionOnboardingMessage';
import { ComponentDatasheetHeaderImages } from 'components/component-datasheet/components/ComponentDatasheetHeaderImages';
import { ComponentThumbnail } from 'components/thumbnail';
import { ComponentInstanceForm } from '@diagram/components/diagram-component-instance/ComponentInstanceForm';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

const Header: FC<{
    componentInstance: DiagramComponentInstance;
    showComponentMenu?: boolean;
    rightSection?: React.ReactNode;
}> = ({ componentInstance, showComponentMenu = true, rightSection }) => {
    const { component } = useComponent(componentInstance.componentId, {
        skipAccessCheck: true,
    });
    const isTeamComponent = useIsTeamComponent(componentInstance);

    const showDatasheetUrl = component && !isTeamComponent;

    return (
        <Flex justify="space-between" align="flex-start" gap="xs" p="xs">
            <ComponentInstanceForm componentInstance={componentInstance}>
                <Stack gap={4}>
                    <Flex align="center" gap={2}>
                        <Box
                            style={{
                                flexGrow: 1,
                            }}
                            key={'designator'}
                        >
                            <TextField name="designator" placeholder="Designator" variant="edit-on-hover" size="xs" />
                        </Box>

                        {rightSection}

                        {showComponentMenu && (
                            <Flex justify="flex-end" px={0} gap={2} key="actions">
                                <EditComponentInstanceContextMenu componentInstance={componentInstance} />
                            </Flex>
                        )}
                    </Flex>
                    <Flex gap={8}>
                        {component && <ProductImages component={component} />}
                        <Grid gutter={2} w="100%">
                            <Grid.Col span={12}>
                                <TextField name="label" placeholder="Label" variant="edit-on-hover" size="xs" />
                            </Grid.Col>
                            <ProductInfo componentInstance={componentInstance} component={component} />
                        </Grid>
                    </Flex>

                    {showDatasheetUrl && (
                        <Anchor
                            fz="xs"
                            component={Link}
                            href={ComponentHelpers.urls.view(component.id)}
                            target="_blank"
                            style={{ display: 'flex', alignSelf: 'flex-start' }}
                        >
                            <span>View datasheet</span>
                            <IoOpenOutline size={10} style={{ marginTop: 3, marginLeft: 4 }} />
                        </Anchor>
                    )}
                </Stack>
            </ComponentInstanceForm>
        </Flex>
    );
};

const ProductInfo = ({
    componentInstance,
    component,
}: {
    componentInstance: DiagramComponentInstance;
    component?: Component;
}) => {
    const privateLibraryEnabled = useFeatureAccess(FeatureKey.TEAM_COMPONENT_LIBRARY);

    const manufacturerValue = useWatch({
        name: 'manufacturer',
    });

    const linkedToProduct = Boolean(componentInstance.componentId);

    const { company: manufacturer } = useCompanyProfile(component?.manufacturer);
    const manufacturerName = manufacturer?.name || component?.teamManufacturer;
    const partNumber = component?.productIdentifier;

    const showTeamComponentMessage = !linkedToProduct && manufacturerValue;

    return (
        <>
            <Grid.Col span={8}>
                {linkedToProduct ? (
                    manufacturer ? (
                        <Text className={cx.linkedInfo}>{manufacturerName}</Text>
                    ) : (
                        <Text c="gray.5" className={cx.linkedInfo}>
                            Manufacturer
                        </Text>
                    )
                ) : (
                    <TextField name="manufacturer" placeholder="Manufacturer" variant="edit-on-hover" size="xs" />
                )}
            </Grid.Col>
            <Grid.Col span={4}>
                {linkedToProduct ? (
                    partNumber ? (
                        <Text className={cx.linkedInfo}>{partNumber}</Text>
                    ) : (
                        <Text c="gray.5" className={cx.linkedInfo}>
                            Part number
                        </Text>
                    )
                ) : (
                    <TextField name="partNumber" placeholder="Part number" variant="edit-on-hover" size="xs" />
                )}
            </Grid.Col>

            <Transition mounted={!!showTeamComponentMessage} transition="fade" duration={400} timingFunction="ease">
                {(styles) => (
                    <Grid.Col span={12} style={styles} mt={4}>
                        {privateLibraryEnabled ? (
                            <DiagramSidebar.Button
                                onClick={() => {
                                    ComponentInstanceService.openCreateReusableComponentModal(componentInstance);
                                }}
                            >
                                <BsBookmarkPlus /> <span>Save component</span>
                            </DiagramSidebar.Button>
                        ) : (
                            <SubscriptionOnboardingMessage>
                                Want to save components for reuse?
                            </SubscriptionOnboardingMessage>
                        )}
                    </Grid.Col>
                )}
            </Transition>
        </>
    );
};

const ProductImages = ({ component }: { component: Component }) => {
    const [opened, handlers] = useDisclosure();

    const hasImages = component.images.filter((image) => Boolean(image.file)).length > 0;

    if (!hasImages) {
        return null;
    }

    return (
        <>
            <UnstyledButton onClick={handlers.open} className={cx.productImage}>
                <ComponentThumbnail component={component} style={{ width: 40 }} showEmpty />
            </UnstyledButton>
            <Modal opened={opened} onClose={handlers.close} withCloseButton={false}>
                <ComponentDatasheetHeaderImages images={component.images} imageHeight={400} showType />
            </Modal>
        </>
    );
};

export { Header };
