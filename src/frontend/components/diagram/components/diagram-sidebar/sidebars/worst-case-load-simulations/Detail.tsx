import React, { FC } from 'react';

import { useWireSizings } from '@diagram/hooks/use-wire-sizings';
import { useComponentInstance, useConnections } from '@diagram/hooks';
import { useDiagramValidationErrorCount } from '@diagram/hooks/use-diagram-validation-error-count';

import { DiagramSidebar } from '@diagram/components/diagram-sidebar/DiagramSidebar';
import { WireSizingService } from '@diagram/services/WireSizingService';

import { DiagramComponentInstance, DiagramConnection, Zone, currentConverter, voltageConverter } from 'models';
import { SidebarType } from '@diagram/state/sidebar';

import { ZoneService } from '@diagram/services/ZoneService';
import { ActionIcon, Box, Group, HoverCard, ScrollArea, Tooltip } from '@mantine/core';

import { IoWarningOutline } from 'react-icons/io5';
import { ConnectionForm } from '@diagram/components/diagram-connections/ConnectionForm';

import { ConnectionService } from '@diagram/services';
import { DiagramSyncService } from '@diagram/services/DiagramSyncService';
import { SidebarService } from '@diagram/services/SidebarService';

import { FormatHelpers } from 'helpers/formatters';

import { ConnectionContext, useConnectionContext } from 'contexts/connection';
import { ConnectionHelpers } from '@diagram/helpers/ConnectionHelpers';

import { TbCircleCheck } from 'react-icons/tb';
import { SimpleButton } from 'components/buttons';

import { useSidebarWidth } from '@diagram/hooks/use-sidebar-width';

import cx from './WireSizing.module.scss';
import { WorstCaseLoadHelpers } from '@diagram/helpers/WorstCaseLoadHelpers';

const Detail: FC = () => {
    useSidebarWidth(600);

    const { wireSizing } = useWireSizings();

    const connections = useConnections();
    const connectionsWithWorstCaseLoad: {
        connection: DiagramConnection;
        worstCaseLoad: any;
    }[] = [];

    connections.forEach((connection) => {
        const item = {
            connection,
            worstCaseLoad: null as {
                voltage: { unit: 'V'; value: number };
                current: { unit: 'A'; value: number };
            } | null,
        };

        const voltage = WorstCaseLoadHelpers.getDesignVoltage(wireSizing, connection);
        const current = WorstCaseLoadHelpers.getDesignCurrent(wireSizing, connection);

        if (voltage && current) {
            item.worstCaseLoad = {
                voltage: {
                    unit: 'V' as const,
                    value: voltage,
                },
                current: {
                    unit: 'A' as const,
                    value: current,
                },
            };
        }

        connectionsWithWorstCaseLoad.push(item);
    });

    const apply = () => {
        connectionsWithWorstCaseLoad.forEach(({ connection, worstCaseLoad }) => {
            if (worstCaseLoad) {
                ConnectionService.update({
                    id: connection.id,
                    requirements: {
                        ...connection.requirements,
                        voltage: worstCaseLoad.voltage,
                        current: worstCaseLoad.current,
                    },
                });
            }
        });

        DiagramSyncService.save();
    };

    return wireSizing ? (
        <React.Fragment>
            <DiagramSidebar.SimpleHeader
                rightSection={
                    <DiagramSidebar.Button onClick={apply}>Apply all calculated values</DiagramSidebar.Button>
                }
                handleBackClick={WireSizingService.deactivate}
            >
                {wireSizing.name}
            </DiagramSidebar.SimpleHeader>
            <DiagramSidebar.Section>
                <ScrollArea className={cx.wireSizingWrapper} type="never">
                    <Box className={cx.wireSizing}>
                        <Box className={[cx.gridHeader, cx.row].join(' ')}>
                            <Box />
                            <Box>Max. Voltage</Box>
                            <Box>Max. Current</Box>
                            <Box></Box>
                        </Box>
                        {connectionsWithWorstCaseLoad.map(({ connection, worstCaseLoad }) => (
                            <Connection connection={connection} worstCaseLoad={worstCaseLoad} key={connection.id} />
                        ))}
                    </Box>
                </ScrollArea>
            </DiagramSidebar.Section>
        </React.Fragment>
    ) : (
        <DiagramSidebar.Loader />
    );
};

const Connection: FC<{
    connection: DiagramConnection;
    worstCaseLoad: {
        voltage: { unit: 'V'; value: number };
        current: { unit: 'A'; value: number };
    } | null;
}> = ({ connection, worstCaseLoad }) => {
    const fromComponentInstance = useComponentInstance(connection.from.componentInstanceId);
    const toComponentInstance = useComponentInstance(connection.to.componentInstanceId);

    const componentInstances = [fromComponentInstance, toComponentInstance].filter(
        Boolean,
    ) as DiagramComponentInstance[];

    const zone: Zone = {
        id: 'temp',
        type: 'control',
        connections: [connection],
        componentInstances,
    };

    const highlight = () => {
        ZoneService.highlight(zone);
    };

    const lowlight = () => {
        ZoneService.highlight();
    };

    const focus = () => {
        ZoneService.moveIntoView(zone);
    };

    const hasZoneErrors = Boolean(useDiagramValidationErrorCount());

    const apply = () => {
        if (worstCaseLoad) {
            ConnectionService.update({
                id: connection.id,
                requirements: {
                    ...connection.requirements,
                    voltage: worstCaseLoad.voltage,
                    current: worstCaseLoad.current,
                },
            });

            DiagramSyncService.save();
        }
    };

    const applied =
        worstCaseLoad &&
        connection.requirements.voltage.value === worstCaseLoad.voltage.value &&
        connection.requirements.current.value === worstCaseLoad.current.value;

    return fromComponentInstance && toComponentInstance ? (
        <ConnectionContext.Provider value={{ connection, fromComponentInstance, toComponentInstance }}>
            <ConnectionForm connection={connection}>
                <Box className={[cx.grid, cx.row].join(' ')}>
                    <Group
                        gap={8}
                        align="center"
                        onMouseEnter={highlight}
                        onMouseLeave={lowlight}
                        onClick={focus}
                        style={{ cursor: 'pointer' }}
                    >
                        <HoverCard position="bottom-start" radius="xs" withArrow>
                            <HoverCard.Target>
                                <div>
                                    {fromComponentInstance.designator}
                                    {' • '}
                                    {toComponentInstance.designator}
                                </div>
                            </HoverCard.Target>
                            <HoverCard.Dropdown p="xs">
                                <ConnectionInformation />
                            </HoverCard.Dropdown>
                        </HoverCard>
                        {!worstCaseLoad && hasZoneErrors && (
                            <Tooltip
                                label={
                                    <>
                                        We could not calculate the worst case load for this connection.
                                        <br />
                                        Please fix your validation errors and try again.
                                    </>
                                }
                            >
                                <ActionIcon
                                    size="xs"
                                    radius={99}
                                    variant="light"
                                    color="orange"
                                    onClick={() => SidebarService.open({ type: SidebarType.VALIDATIONS })}
                                >
                                    <IoWarningOutline size={10} style={{ marginTop: -1 }} />
                                </ActionIcon>
                            </Tooltip>
                        )}
                    </Group>
                    <Box className={cx.gridConnectionValue}>
                        {FormatHelpers.formatVoltage(worstCaseLoad?.voltage) || '-'}
                    </Box>
                    <Box className={cx.gridConnectionValue}>
                        {FormatHelpers.formatCurrent(worstCaseLoad?.current) || '-'}
                    </Box>
                    <Box className={cx.gridConnectionValue} onClick={apply}>
                        {applied ? (
                            <Tooltip label="The required current and voltage are already applied.">
                                <TbCircleCheck size={14} color="#37b24d" />
                            </Tooltip>
                        ) : worstCaseLoad ? (
                            <Tooltip label="This will apply the rated current and voltage for this connection.">
                                <SimpleButton onClick={apply} size="xs">
                                    Apply
                                </SimpleButton>
                            </Tooltip>
                        ) : null}
                    </Box>
                </Box>
            </ConnectionForm>
        </ConnectionContext.Provider>
    ) : null;
};

const ConnectionInformation: FC = () => {
    const { connection, fromComponentInstance, toComponentInstance } = useConnectionContext();
    const voltageType = ConnectionHelpers.getVoltageType(connection, fromComponentInstance, toComponentInstance);

    const lines: string[] = [];

    if (voltageType) {
        Object.entries(connection.lines[voltageType]).forEach(([line, active]) => {
            if (active) {
                lines.push(line);
            }
        });
    }

    return (
        <Box className={cx.connectionInformation}>
            {[
                { label: 'Voltage Type', value: voltageType },
                { label: 'Lines', value: lines.join(', ') },
            ].map((item) => (
                <Box className={cx.connectionInformationLine} key={item.label}>
                    <Box>{item.label}:</Box>
                    <Box>{item.value || '-'}</Box>
                </Box>
            ))}
        </Box>
    );
};

export { Detail };
