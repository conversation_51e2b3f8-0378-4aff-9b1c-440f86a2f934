import React, { FC } from 'react';

import { BillOfMaterialsComponent as BOMComponent, Component } from 'models';

import { ComponentThumbnail } from 'components/thumbnail';

import { useComponentMeta } from 'hooks/use-component-meta';
import { useBillOfMaterialsContext } from '@diagram/components/diagram-sidebar/sidebars/bill-of-materials/BillOfMaterialContex';

import { ComponentInstanceService } from '@diagram/services';
import { SelectionService } from '@diagram/services/SelectionService';

import { BillOfMaterialsItem } from './BillOfMaterialsItem';

const BillOfMaterialsComponent: FC<{ index: number; component: Component } & Omit<BOMComponent, 'component'>> = ({
    index,
    component,
    quantity,
}) => {
    const { uniqueName, subtitle } = useComponentMeta(component);

    const { orderPrices } = useBillOfMaterialsContext();

    const orderData = orderPrices?.orderComponents.find((needle) => needle.component === component.id)?.data;

    const selectComponent = () => {
        const componentInstances = ComponentInstanceService.getByComponentId(component.id);

        SelectionService.selectComponentInstances(componentInstances.map((componentInstance) => componentInstance.id));

        ComponentInstanceService.moveIntoView(componentInstances[0].id);
    };

    return (
        <BillOfMaterialsItem
            path={`components.${index}`}
            name={uniqueName}
            subtitle={subtitle}
            quantity={quantity}
            image={
                <ComponentThumbnail
                    component={component}
                    style={{
                        width: 40,
                        height: 40,
                    }}
                />
            }
            orderData={orderData}
            selectComponent={selectComponent}
        />
    );
};

export { BillOfMaterialsComponent };
