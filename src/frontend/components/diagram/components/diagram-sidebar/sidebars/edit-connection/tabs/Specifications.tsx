import React, { FC } from 'react';

import { set } from 'sync-engine';

import { DiagramConnection, UserFeatureFlags } from 'models';

import { keys } from 'radash';

import { Alert, Box, Flex, Group, Stack, Text, Tooltip } from '@mantine/core';

import { DiagramSidebar } from 'components/diagram';
import { LinesField } from 'components/component-fields/LinesFields';
import { AutoSave } from 'components/forms/AutoSave';
import { ConductorMaterialField } from 'components/component-fields/ConductorMaterialField';
import { ConnectionCoresField } from 'components/component-fields/ConnectionCores';
import { Form, FormOnSubmit } from 'components/forms/Form';
import { InsulationMaterialField } from 'components/component-fields/InsulationMaterialField';
import { VoltageTypeSwitch } from '../components/VoltageTypeSwitch';
import { WireSizeSection } from '../components/WireSizeSection';
import { CableSuggestions } from '../components/CableSuggestions';
import { CurrentField } from 'components/component-fields/CurrentField';

import { FieldSuggestions } from 'components/forms/fields/FieldSuggestions';

import { ConnectionService } from '@diagram/services';
import { DiagramSyncService } from '@diagram/services/DiagramSyncService';

import { useOs } from '@mantine/hooks';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentUserFlag } from 'hooks/use-current-user-flag';
import { useEditConnectionContext } from '../context';
import { useWireSizingSettings } from '@diagram/hooks/use-wire-sizing-settings';

import cx from '../EditConnection.module.scss';
import { IoCalculatorSharp } from 'react-icons/io5';
import { WireSizingService } from '@diagram/services/WireSizingService';
import { Endpoints } from '@diagram/components/diagram-sidebar/sidebars/edit-connection/components/Endpoints';
import { VoltageField } from 'components/component-fields/VoltageField';
import { SelectField } from 'components/forms/fields/SelectField';
import { useLatestWorstCaseLoad } from '@diagram/hooks/use-latest-worst-case-load';
import { WorstCaseLoadHelpers } from '@diagram/helpers/WorstCaseLoadHelpers';
import { ConductorTemperatureField } from 'components/component-fields/ConductorTemperatureField';
import { useCurrentProject } from 'hooks/use-current-project';

const Specifications: FC = () => {
    const user = useCurrentUser();

    const project = useCurrentProject();
    const standard = project?.wireSizing.standard;

    const { connection, voltageType } = useEditConnectionContext();
    const userHasWireSizing = useCurrentUserFlag(UserFeatureFlags.WIRE_SIZING);
    const OS = useOs();

    const { voltageDrop } = useWireSizingSettings();
    const { worstCaseLoad } = useLatestWorstCaseLoad();

    const linkedToCable = !!connection.cableId;

    const maxNbLines = connection.numberOfConductors || Infinity;
    const enablePE = linkedToCable ? connection.hasPE : true;

    const save: FormOnSubmit<DiagramConnection> = (values, { getUpdates }) => {
        const updates = getUpdates();

        ConnectionService.update({ ...updates, id: connection.id });
        DiagramSyncService.save();
    };

    return (
        <Form<DiagramConnection> defaultValues={connection} onSubmit={save} key={connection?.id}>
            <AutoSave
                instant={[
                    ...keys(connection?.lines ?? {}).map((key) => `lines.${key}`),
                    'cores',
                    'conductorMaterial',
                    'insulationMaterial',
                ]}
            />
            <DiagramSidebar.Section className={cx.root}>
                <Stack gap="xs">
                    <Endpoints />

                    <Flex gap="xs">
                        <VoltageTypeSwitch />
                        <LinesField voltageType={voltageType} maxNbLines={maxNbLines} enablePE={enablePE} />
                    </Flex>

                    <Group gap={4}>
                        <Text w={140} fz={12} fw={500}>
                            Design Voltage
                        </Text>
                        <Box style={{ flexGrow: 1 }}>
                            <VoltageField
                                name="requirements.voltage"
                                placeholder={`${WorstCaseLoadHelpers.getDesignVoltage(worstCaseLoad, connection) || ''}`}
                                fields={['value']}
                                size="sm"
                                hideIcons
                            />
                        </Box>
                        {userHasWireSizing && (
                            <Tooltip
                                label={
                                    <>
                                        Calculate your design voltage by
                                        <br />
                                        running a worst-case load simulation.
                                    </>
                                }
                                position="bottom-end"
                                withArrow
                            >
                                <DiagramSidebar.Button
                                    size="md"
                                    onClick={() => {
                                        WireSizingService.openWorstCaseLoadSidebar();
                                    }}
                                >
                                    <IoCalculatorSharp strokeWidth={0.5} />
                                </DiagramSidebar.Button>
                            </Tooltip>
                        )}
                    </Group>

                    <Line label="Design Voltage Drop">
                        <SelectField
                            name="requirements.voltageDrop"
                            data={[
                                { value: '0.01', label: '1%' },
                                { value: '0.02', label: '2%' },
                                { value: '0.03', label: '3%' },
                                { value: '0.04', label: '4%' },
                                { value: '0.05', label: '5%' },
                            ]}
                            stringify={(value) => value?.toString() || null}
                            parse={(value) => (value ? +value : null)}
                            placeholder={`${voltageDrop * 100}%`}
                            size="sm"
                            w="100%"
                        />
                    </Line>

                    <Group wrap="nowrap" gap={4}>
                        <Text w={140} fz={12} fw={500}>
                            Design Current (lb)
                        </Text>
                        <Box style={{ flexGrow: 1 }}>
                            <CurrentField
                                name="requirements.current"
                                placeholder={`${WorstCaseLoadHelpers.getDesignCurrent(worstCaseLoad, connection) || ''}`}
                                fields={['value']}
                                size="sm"
                                hideIcons
                            />
                        </Box>
                        {userHasWireSizing && (
                            <Tooltip
                                label={
                                    <>
                                        Calculate your design current by
                                        <br />
                                        running a worst-case load simulation.
                                    </>
                                }
                                position="bottom-end"
                                withArrow
                            >
                                <DiagramSidebar.Button
                                    size="md"
                                    onClick={() => {
                                        WireSizingService.openWorstCaseLoadSidebar();
                                    }}
                                >
                                    <IoCalculatorSharp strokeWidth={0.5} />
                                </DiagramSidebar.Button>
                            </Tooltip>
                        )}
                    </Group>
                </Stack>
            </DiagramSidebar.Section>
            <DiagramSidebar.Divider />
            <DiagramSidebar.Section>
                <Line label="Rated Voltage">
                    <FieldSuggestions
                        suggestions={voltageType === 'AC' ? ACVoltageSuggestions : DCVoltageSuggestions}
                        onSelect={(option) => {
                            ConnectionService.update({ ...option.values, id: connection.id });
                            DiagramSyncService.save();
                        }}
                    >
                        <Group wrap="nowrap" gap={4}>
                            <VoltageField
                                name="lineToEarthVoltage"
                                fields={['value']}
                                placeholder="Line to Earth"
                                size="sm"
                                w="100%"
                                hideIcons
                            />
                            <VoltageField
                                name="lineToLineVoltage"
                                fields={['value']}
                                placeholder="Line to Line"
                                size="sm"
                                w="100%"
                                hideIcons
                            />
                        </Group>
                    </FieldSuggestions>
                </Line>
            </DiagramSidebar.Section>
            <DiagramSidebar.Divider />
            <DiagramSidebar.Section className={cx.root}>
                <Stack gap="xs">
                    <Line label="Conductor Material">
                        <ConductorMaterialField
                            w="100%"
                            name="conductorMaterial"
                            disabled={linkedToCable}
                            inline={standard === 'IEC'}
                            size="sm"
                            standard={standard}
                        />
                    </Line>

                    {standard === 'NEC' && (
                        <Line label="Conductor Temperature">
                            <ConductorTemperatureField
                                w="100%"
                                placeholder="Select temperature"
                                name="conductorTemperature"
                                disabled={linkedToCable}
                                size="sm"
                                standard={standard}
                            />
                        </Line>
                    )}

                    {standard === 'IEC' && (
                        <Line label="Insulation Material">
                            <InsulationMaterialField name="insulationMaterial" disabled={linkedToCable} inline />
                        </Line>
                    )}

                    <Line label="Cores">
                        <ConnectionCoresField name="cores" disabled={linkedToCable} connection={connection} inline />
                    </Line>
                </Stack>
            </DiagramSidebar.Section>
            <DiagramSidebar.Divider />
            <DiagramSidebar.Section>
                <Stack gap="xs">
                    <WireSizeSection userHasWireSizing={userHasWireSizing} />
                </Stack>
            </DiagramSidebar.Section>
            <DiagramSidebar.Divider />
            <DiagramSidebar.Section>
                <Alert title="Did you know you can edit multiple connections at once?" color="blue">
                    {OS === 'macos'
                        ? 'Select multiple connections by holding the Command key and clicking on each desired connection.'
                        : 'Select multiple connections by holding the Ctrl key and clicking on each desired connection.'}
                </Alert>
                {user && !connection.cableId && <CableSuggestions connection={connection} />}
            </DiagramSidebar.Section>
        </Form>
    );
};

const Line: FC<{
    label: string;
    children: React.ReactNode;
}> = ({ label, children }) => (
    <Group wrap="nowrap" gap={4}>
        <Text w={140} fz={12} fw={500} style={{ flexGrow: 0, flexShrink: 0 }}>
            {label}
        </Text>
        <Flex style={{ flexGrow: 1, flexShrink: 1 }} justify="flex-end">
            {children}
        </Flex>
    </Group>
);

const ACVoltageSuggestions = [
    {
        value: '0.6/1 kV',
        label: '0.6/1 kV',
        values: {
            lineToEarthVoltage: { value: 600, unit: 'V' },
            lineToLineVoltage: { value: 1000, unit: 'V' },
        },
    },
];

const DCVoltageSuggestions = [
    {
        value: '0.75/1.5 kV',
        label: '0.75/1.5 kV',
        values: {
            lineToEarthVoltage: { value: 750, unit: 'V' },
            lineToLineVoltage: { value: 1500, unit: 'V' },
        },
    },
];

export { Specifications };
