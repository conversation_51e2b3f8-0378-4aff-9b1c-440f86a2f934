import React, { FC } from 'react';

import { DiagramConnection, UserFeatureFlags, isUnderground } from 'models';

import { ActionIcon, Box, Button, Collapse, Group, Stack, Text, Tooltip } from '@mantine/core';
import { IoInformationCircleOutline, IoSwapVertical } from 'react-icons/io5';
import { GiHorizontalFlip, GiVerticalFlip } from 'react-icons/gi';

import { Form, FormOnSubmit } from 'components/forms/Form';
import { InstallationMethodField } from 'components/component-fields/InstallationMethodField';
import { TemperatureField } from 'components/component-fields/TemperatureField';
import { IntegerField } from 'components/forms/fields/IntegerField';
import { DuctToDuctClearanceField } from 'components/component-fields/DuctToDuctClearanceField';
import { CableToCableClearanceField } from 'components/component-fields/CableToCableClearanceField';
import { CableArrangementField } from 'components/component-fields/CableArrangementField';
import { NumberField } from 'components/forms/fields/NumberField';
import { FieldInfoWrapper } from 'components/component-fields/FieldInfoWrapper';
import { DiagramSidebar } from '@diagram';
import { AutoSave } from 'components/forms/AutoSave';
import { WireSizeSection } from '@diagram/components/diagram-sidebar/sidebars/edit-connection/components/WireSizeSection';
import { CableSupportTypeField } from 'components/component-fields/CableSupportTypeField';
import { CableOrientationField } from 'components/component-fields/CableOrientationField';
import { SoilThermalResistivityField } from 'components/component-fields/SoilThermalResistivityField';

import { ConnectionService } from '@diagram/services';
import { DiagramSyncService } from '@diagram/services/DiagramSyncService';

import { WireSizingHelpers } from '@diagram/helpers/WireSizingHelpers';

import { useCurrentUserFlag } from 'hooks/use-current-user-flag';
import { useEditConnectionContext } from '../context';
import { useWireSizeValidation } from '@diagram/hooks/use-wire-size-validation';
import { ReferenceInstallationMethodField } from 'components/component-fields/ReferenceInstallationMethodField';
import { openContextModal } from '@mantine/modals';
import { useCurrentProject } from 'hooks/use-current-project';

const Installation: FC = () => {
    const { connection, voltageType } = useEditConnectionContext();
    const { data: validationData } = useWireSizeValidation(connection, voltageType);

    const userHasWireSizing = useCurrentUserFlag(UserFeatureFlags.WIRE_SIZING);

    const save: FormOnSubmit<DiagramConnection> = (values, { getUpdates }) => {
        const updates = getUpdates();

        ConnectionService.update({ ...updates, id: connection.id });

        DiagramSyncService.save();
    };

    return (
        <Form<DiagramConnection> defaultValues={connection} onSubmit={save} key={connection?.id}>
            <AutoSave
                instant={[
                    'installation.method',
                    'installation.referenceMethod',
                    'installation.referenceMethod',
                    'installation.cableArrangement',
                    'installation.cableFormation',
                    'installation.cableToCableClearance',
                    'installation.ductToDuctClearance',
                    'installation.numberOfDucts.horizontal',
                    'installation.numberOfDucts.vertical',
                ]}
            />
            <DiagramSidebar.Section>
                <Stack gap="xs">
                    <WireSizeSection userHasWireSizing={userHasWireSizing} />
                </Stack>
            </DiagramSidebar.Section>
            <DiagramSidebar.Divider title="Installation" />
            <DiagramSidebar.Section>
                <Stack gap="xs">
                    <InstallionMethods />

                    {conditions.ambientTemperature(connection) && (
                        <Line label="Ambient Temperature">
                            <TemperatureField
                                name="installation.temperature"
                                fields={['value']}
                                suggestions={[
                                    {
                                        value: '20',
                                        internalValue: { value: 273.15 + 20, unit: 'K' },
                                        label: '20°C / 68°F',
                                    },
                                    {
                                        value: '30',
                                        internalValue: { value: 273.15 + 30, unit: 'K' },
                                        label: '30°C / 86°F',
                                        default: true,
                                    },
                                ]}
                                size="sm"
                            />
                        </Line>
                    )}

                    {conditions.soilTemperature(connection) && (
                        <Line label="Soil Temperature">
                            <TemperatureField
                                name="installation.temperature"
                                fields={['value']}
                                suggestions={[
                                    {
                                        value: '20',
                                        internalValue: { value: 273.15 + 20, unit: 'K' },
                                        label: '20°C / 68°F',
                                        default: true,
                                    },
                                    {
                                        value: '30',
                                        internalValue: { value: 273.15 + 30, unit: 'K' },
                                        label: '30°C / 86°F',
                                    },
                                ]}
                                size="sm"
                            />
                        </Line>
                    )}

                    {conditions.soilThermalResistivity(connection) && (
                        <Line label="Soil Thermal Resistivity">
                            <SoilThermalResistivityField name="installation.soilThermalResistivity" size="sm" />
                        </Line>
                    )}
                </Stack>
            </DiagramSidebar.Section>

            {conditions.cableGrouping(connection) && (
                <>
                    <DiagramSidebar.Divider label="Grouping" />
                    <DiagramSidebar.Section>
                        <Stack gap="xs">
                            <Line label="Arrangement">
                                <CableArrangementField
                                    name="installation.cableArrangement"
                                    size="sm"
                                    disabled={Boolean(
                                        connection.installation.referenceMethod &&
                                            isUnderground(connection.installation.referenceMethod),
                                    )}
                                    afterChange={(value) => {
                                        ConnectionService.setDefaultInstallationProperty('cableArrangement', value);
                                    }}
                                />
                            </Line>

                            <Line label="Orientation">
                                <Group wrap="nowrap" gap={4}>
                                    <Box style={{ flexGrow: 1, flexShrink: 1 }} />
                                    <CableOrientationField
                                        name="installation.cableOrientation"
                                        size="sm"
                                        afterChange={(value) => {
                                            ConnectionService.setDefaultInstallationProperty('cableOrientation', value);
                                        }}
                                        inline
                                        style={{ flexShrink: 1 }}
                                    />
                                </Group>
                            </Line>

                            {conditions.ladderOrTray(connection) && (
                                <Line label="Ladder / Tray">
                                    <Group wrap="nowrap" gap={4}>
                                        <IntegerField
                                            name="installation.numberOfTraysOrLadders"
                                            min={1}
                                            suffix=" x"
                                            hideControls
                                            size="sm"
                                            w={50}
                                        />
                                        <CableSupportTypeField
                                            name="installation.cableSupportType"
                                            size="sm"
                                            afterChange={(value) => {
                                                ConnectionService.setDefaultInstallationProperty(
                                                    'cableSupportType',
                                                    value,
                                                );
                                            }}
                                            style={{ flexGrow: 1, flexShrink: 1 }}
                                        />
                                    </Group>
                                </Line>
                            )}

                            {conditions.cableToCableClearance(connection) && (
                                <Line label="Cable to Cable Clearance">
                                    <CableToCableClearanceField
                                        name="installation.cableToCableClearance"
                                        size="sm"
                                        connection={connection}
                                        voltageType={voltageType}
                                        afterChange={(value) => {
                                            ConnectionService.setDefaultInstallationProperty(
                                                'cableToCableClearance',
                                                value,
                                            );
                                        }}
                                    />
                                </Line>
                            )}

                            {conditions.ductToDuctClearance(connection) && (
                                <Line label="Duct to Duct Clearance">
                                    <DuctToDuctClearanceField
                                        name="installation.ductToDuctClearance"
                                        size="sm"
                                        afterChange={(value) => {
                                            ConnectionService.setDefaultInstallationProperty(
                                                'ductToDuctClearance',
                                                value,
                                            );
                                        }}
                                    />
                                </Line>
                            )}

                            {conditions.numberOfDucts(connection) && (
                                <Group wrap="nowrap" gap={4}>
                                    <Group wrap="nowrap" gap={4} w={150} style={{ flexGrow: 0, flexShrink: 0 }}>
                                        <Text fz={12} fw={500}>
                                            Number of ducts
                                        </Text>
                                        <Tooltip
                                            label={
                                                <Stack>
                                                    <Text>Horizontal: Number of ducts arranged side by side.</Text>
                                                    <Text>
                                                        Vertical: Number of layers of ducts stacked on top of each
                                                        other.
                                                    </Text>
                                                </Stack>
                                            }
                                            multiline
                                            w={220}
                                            withArrow
                                        >
                                            <ActionIcon variant="transparent" size="xs" color="gray">
                                                <IoInformationCircleOutline size={12} />
                                            </ActionIcon>
                                        </Tooltip>
                                    </Group>
                                    <Group wrap="nowrap" gap={4} style={{ width: '100%', flex: 1 }}>
                                        <IntegerField
                                            name="installation.numberOfDucts.horizontal"
                                            min={1}
                                            placeholder="Horizontal"
                                            leftSection={<GiHorizontalFlip size={14} />}
                                            hideControls
                                            size="sm"
                                            style={{ flex: 1 }}
                                            afterChange={(value) => {
                                                ConnectionService.setDefaultInstallationProperty('numberOfDucts', {
                                                    ...connection.installation.numberOfDucts,
                                                    horizontal: value,
                                                });
                                            }}
                                        />
                                        <IntegerField
                                            name="installation.numberOfDucts.vertical"
                                            min={1}
                                            placeholder="Vertical"
                                            leftSection={<GiVerticalFlip size={14} />}
                                            hideControls
                                            size="sm"
                                            style={{ flex: 1 }}
                                            afterChange={(value) => {
                                                ConnectionService.setDefaultInstallationProperty('numberOfDucts', {
                                                    ...connection.installation.numberOfDucts,
                                                    vertical: value,
                                                });
                                            }}
                                        />
                                    </Group>
                                </Group>
                            )}

                            <Line label="Cables in Vicinity">
                                <IntegerField
                                    name="installation.cablesInVicinity"
                                    min={0}
                                    size="sm"
                                    afterChange={(value) => {
                                        ConnectionService.setDefaultInstallationProperty('cablesInVicinity', value);
                                    }}
                                />
                            </Line>
                        </Stack>
                    </DiagramSidebar.Section>
                </>
            )}

            <DiagramSidebar.Section>
                <Collapse
                    in={
                        connection.installation.correctionFactorOverride !== null ||
                        Boolean(validationData?.currentCarryingCapacityInformation?.correctionFactor)
                    }
                >
                    <Line label="Correction Factor">
                        <FieldInfoWrapper
                            description={
                                <>
                                    By default, the correction factor is calculated based on the installation
                                    parameters.
                                    <br />
                                    Experts can use this field to override the correction factor.
                                </>
                            }
                        >
                            <NumberField
                                name="installation.correctionFactorOverride"
                                placeholder={`${validationData?.currentCarryingCapacityInformation?.correctionFactor || ''}`}
                                size="sm"
                                hideControls
                            />
                        </FieldInfoWrapper>
                    </Line>
                </Collapse>
            </DiagramSidebar.Section>
        </Form>
    );
};

const Line: FC<{
    label: string;
    children: React.ReactNode;
}> = ({ label, children }) => (
    <Group wrap="nowrap" gap={4}>
        <Text w={150} fz={12} fw={500} style={{ flexGrow: 0, flexShrink: 0 }}>
            {label}
        </Text>
        <Box style={{ flexGrow: 1, flexShrink: 1 }}>{children}</Box>
    </Group>
);

const InstallionMethods = () => {
    const project = useCurrentProject();

    const standard = project?.wireSizing?.standard;

    const openWireSizeSettingsModal = () => {
        openContextModal({
            modal: 'wireSizeSettings',
            title: 'Wire Size Settings',
            centered: true,
            innerProps: {},
        });
    };

    return (
        <>
            {standard ? (
                <InstallationMethodField
                    standard={standard}
                    name="installation.method"
                    label="Installation Method"
                    size="sm"
                    afterChange={(value) => {
                        ConnectionService.setDefaultInstallationProperty('method', value);
                    }}
                />
            ) : (
                <Line label="Installation Method">
                    <Button fullWidth variant="light" color="red" size="xs" onClick={openWireSizeSettingsModal}>
                        Set wire size standard first
                    </Button>
                </Line>
            )}
            {standard && (
                <ReferenceInstallationMethodField
                    standard={standard}
                    name="installation.referenceMethod"
                    label="Reference Installation Method"
                    size="sm"
                    afterChange={(value) => {
                        if (!value) return;

                        ConnectionService.setDefaultInstallationProperty('referenceMethod', value);
                    }}
                />
            )}
        </>
    );
};

const conditions = {
    ambientTemperature: (connection: DiagramConnection) => {
        return connection.installation.referenceMethod !== 'D1' && connection.installation.referenceMethod !== 'D2';
    },

    soilTemperature: (connection: DiagramConnection) => {
        return connection.installation.referenceMethod === 'D1' || connection.installation.referenceMethod === 'D2';
    },

    ductToDuctClearance: (connection: DiagramConnection) => {
        return connection.installation.referenceMethod === 'D1';
    },

    ladderOrTray: (connection: DiagramConnection) => {
        return connection.installation.referenceMethod === 'E' || connection.installation.referenceMethod === 'F';
    },

    soilThermalResistivity: (connection: DiagramConnection) => {
        return connection.installation.referenceMethod === 'D1' || connection.installation.referenceMethod === 'D2';
    },

    correctionFactorOverride: (connection: DiagramConnection) => {
        return connection.installation.correctionFactorOverride !== null;
    },

    cableGrouping: (connection: DiagramConnection) => {
        const standard = WireSizingHelpers.getStandardFromInstallationMethod(connection);

        return standard && standard === 'IEC';
    },

    cableToCableClearance: (connection: DiagramConnection) => {
        return connection.installation.referenceMethod !== 'D1';
    },

    numberOfDucts: (connection: DiagramConnection) => {
        return connection.installation.referenceMethod === 'D1';
    },
};

export { Installation };
