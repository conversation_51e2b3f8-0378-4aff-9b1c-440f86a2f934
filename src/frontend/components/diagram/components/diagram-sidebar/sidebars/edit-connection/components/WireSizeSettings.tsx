import React, { FC } from 'react';

import { Group, Text, UnstyledButton } from '@mantine/core';
import { IoSettingsOutline } from 'react-icons/io5';

import { useCurrentProject } from 'hooks/use-current-project';

import { openContextModal } from '@mantine/modals';
import { useEditConnectionContext } from '@diagram/components/diagram-sidebar/sidebars/edit-connection/context';

const WireSizeSettings: FC = () => {
    const project = useCurrentProject();
    const standard = project?.wireSizing.standard;

    const { connection } = useEditConnectionContext();
    const voltageDrop = connection.requirements.voltageDrop ?? project?.wireSizing.voltageDrop;

    return (
        <UnstyledButton
            bg="gray.0"
            p={8}
            ml={-8}
            mr={-8}
            mb={-8}
            mt="xs"
            onClick={(e) => {
                e.stopPropagation();

                openContextModal({
                    modal: 'wireSizeSettings',
                    title: 'Wire Size Settings',
                    centered: true,
                    innerProps: {},
                });
            }}
            fz="sm"
            component={Group}
        >
            <Text fw={600} span inherit>
                Standard:{' '}
                <Text fw={400} span inherit>
                    {standard || 'Not specifed'}
                </Text>
            </Text>

            <Text fw={600} span inherit>
                Voltage drop:{' '}
                <Text fw={400} span inherit>
                    {voltageDrop ? `${voltageDrop * 100}%` : '-'}
                </Text>
            </Text>

            <Group gap={4} ml="auto" fw={600} c="blue">
                <IoSettingsOutline size={12} />
                Settings
            </Group>
        </UnstyledButton>
    );
};

export { WireSizeSettings };
