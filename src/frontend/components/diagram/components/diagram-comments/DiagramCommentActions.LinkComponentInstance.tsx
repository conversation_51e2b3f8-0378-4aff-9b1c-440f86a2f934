import React, { FC } from 'react';

import { DiagramComment, DiagramComponentInstance, Component } from 'models';

import { Tooltip } from '@mantine/core';
import { IoLinkOutline, IoUnlinkOutline } from 'react-icons/io5';

import { CommentService } from '@diagram/services/CommentService';
import { LabelService } from '@diagram/services/LabelService';
import { useComponentInstance } from '@diagram/hooks';
import { useComponent } from 'hooks/use-component';

const LinkComponentInstance: FC<{
    comment: DiagramComment;
    withTooltip?: boolean;
    children: (props: {
        linked: boolean;
        linkedComponentInstance: DiagramComponentInstance | null;
        linkedComponent: Component | null;
        Icon: any;
        label: string;
        handle: () => void;
    }) => React.ReactNode;
}> = ({ comment, withTooltip = false, children }) => {
    const linkedComponentInstance = useComponentInstance(comment.componentInstances[0] || undefined) || null;
    const linked = !!linkedComponentInstance;

    const { component: linkedComponent } = useComponent(linkedComponentInstance?.componentId || null, {
        skipAccessCheck: true,
    });

    const Icon = linked ? IoLinkOutline : IoUnlinkOutline;
    const label = linked
        ? `Unlink ${LabelService.getComponentLabel(linkedComponentInstance)}`
        : 'Move close to a component to link';

    const link = () => {};
    const unlink = () => {
        CommentService.unlinkComponentInstance(comment);
    };

    const handle = () => {
        if (linked) {
            unlink();
        } else {
            link();
        }
    };

    return withTooltip ? (
        <Tooltip label={label} withArrow>
            {children({ linked, linkedComponentInstance, linkedComponent, Icon, label, handle })}
        </Tooltip>
    ) : (
        <>{children({ linked, linkedComponentInstance, linkedComponent, Icon, label, handle })}</>
    );
};

export { LinkComponentInstance };
