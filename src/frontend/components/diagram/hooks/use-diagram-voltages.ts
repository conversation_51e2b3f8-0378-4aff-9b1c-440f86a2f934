import { useMemo } from 'react';

import { Voltage } from 'models';

import { useDerivedDiagram } from '@diagram/hooks/use-diagram';

import { FormatHelpers } from 'helpers/formatters';

const useDiagramVoltages = () => {
    const { connectionsAsArray, componentInstancesAsArray } = useDerivedDiagram();

    return useMemo(() => {
        const allVoltages: Voltage[] = [];

        componentInstancesAsArray.forEach((componentInstance) => {
            componentInstance.specifications?.electrical?.ports.forEach((port: any) => {
                allVoltages.push(port.DC?.voltage, port.AC?.voltage);
            });
        });

        return FormatHelpers.getUniqueVoltages(allVoltages);
    }, [connectionsAsArray, componentInstancesAsArray]);
};

export { useDiagramVoltages };
