import { DiagramComponentInstance, DiagramConnection } from 'models';

import { useComponent } from 'hooks/use-component';

import { PortOption, PortService } from '@diagram/services';

import { useDiagram } from './use-diagram';
import { DiagramSyncService } from '../services/DiagramSyncService';

const useConnectionPorts = ({
    componentInstance,
    connection,
    endpoint,
}: {
    componentInstance?: DiagramComponentInstance | null;
    connection: DiagramConnection;
    endpoint: keyof Pick<DiagramConnection, 'from' | 'to'>;
}) => {
    const diagram = useDiagram();

    const { component } = useComponent(componentInstance?.componentId || null, {
        skipAccessCheck: true,
    });

    if (!componentInstance) {
        return {
            ports: [],
            options: [],
            selected: null,
        };
    }

    const options: PortOption[] = PortService.getOptions({
        diagram,
        component,
        componentInstance,
        connection,
    });

    const selected = options.find((option) => option.port.key === connection[endpoint].port);

    return {
        options,
        selected,
    };
};

export { useConnectionPorts };
