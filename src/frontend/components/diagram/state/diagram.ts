import {
    DiagramComponentInstance,
    DiagramConnection,
    getReferenceInstallationMethod,
    NECInstallationMethod,
    isUnderground,
    getDefaultTemperature,
    IECReferenceInstallationMethod,
} from 'models';

import { proxy, subscribe } from 'valtio';
import { derive } from 'derive-valtio';

import { defaultDiagram } from '@diagram/data/default-diagram';

import { DiagramSyncService } from '../services/DiagramSyncService';
import { isEqual, isObject } from 'radash';

export const diagram = proxy(defaultDiagram());

if (typeof window !== 'undefined') {
    // @ts-ignore
    window.internalSubscribeThing = subscribe;
    // @ts-ignore
    window.internalDiagramProxy = diagram;
}

export const derivedDiagramState = derive({
    connectionKeys: (get) => {
        const connections = get(diagram.connections);

        return Object.keys(connections);
    },
    connectionsAsArray: (get) => {
        const connections = get(diagram.connections);

        return Object.values(connections) as DiagramConnection[];
    },
    connectionAnchorLookup: (get) => {
        const connections = get(diagram.connections);

        const lookup: {
            [key: string]: DiagramConnection;
        } = {};

        const createKey = (anchor: DiagramConnection['from']) => {
            return `${anchor.componentInstanceId}-${anchor.edge}-${anchor.offset}`;
        };

        Object.values(connections).forEach((connection) => {
            lookup[createKey(connection.from)] = connection;
            lookup[createKey(connection.to)] = connection;
        });

        return lookup;
    },
    componentInstanceKeys: (get) => {
        const componentInstances = get(diagram.componentInstances);

        return Object.keys(componentInstances);
    },
    componentInstancesAsArray: (get) => {
        const componentInstances = get(diagram.componentInstances);

        return Object.values(componentInstances) as DiagramComponentInstance[];
    },
});

subscribe(
    diagram,
    (changes) => {
        changes.forEach((change) => {
            const [type, paths, value_, previousValue_] = change;

            const value = value_ === undefined ? undefined : JSON.parse(JSON.stringify(value_));
            const previousValue = previousValue_ === undefined ? undefined : JSON.parse(JSON.stringify(previousValue_));

            const path = paths.join('.');

            if (!path) return;

            if (isEqual(value, previousValue)) return;

            if (isObject(value) && Object.keys(value).length > 0 && !process.env.TEST) {
                // Maybe throw an error in the future
                console.warn(
                    `value is an object with keys. Use set(${path}, value) instead. value: ${JSON.stringify(value)}`,
                );
            }

            const skippable = [/textareas\.[0-9]+\.content/].filter((regex) => regex.test(path)).length > 0;

            if (isObject(previousValue) && isObject(value) && Object.keys(value).length > 0 && !skippable) {
                throw new Error(
                    `value cannot be an object to update ${path} Use set(${path}, value) instead or update key-value pairs individually. value: ${JSON.stringify(value)} previousValue: ${JSON.stringify(previousValue)}`,
                );
            }

            if (type === 'set') {
                if (previousValue === undefined) {
                    DiagramSyncService.create(path, value, false);
                } else {
                    DiagramSyncService.updateByKey(path, value, false, previousValue);
                }
            } else if (type === 'delete') {
                DiagramSyncService.delete(path, false, previousValue);
            }
        });
    },
    true,
);

const syncInstallationMethod = (
    connectionId: string,
    installationMethod: DiagramConnection['installation']['method'],
) => {
    if (!installationMethod) {
        return;
    }

    if (installationMethod === NECInstallationMethod.FREE_AIR || installationMethod === NECInstallationMethod.BURIED) {
        return;
    }

    if (!diagram.connections[connectionId]) {
        return;
    }

    try {
        const cores = diagram.connections[connectionId].cores;

        const {
            referenceMethod,
            cableOrientation,
            cableSupportType,
            cores: updatedCores,
        } = getReferenceInstallationMethod(installationMethod, cores);

        diagram.connections[connectionId].installation.referenceMethod = referenceMethod;

        if (cableOrientation) {
            diagram.connections[connectionId].installation.cableOrientation = cableOrientation;
        }

        if (cableSupportType) {
            diagram.connections[connectionId].installation.cableSupportType = cableSupportType;
        }

        if (updatedCores) {
            diagram.connections[connectionId].cores = updatedCores;
        }
    } catch (e) {
        console.error('Failed to sync installation method', e);
    }
};

const updateTemperatureBasedOnInstallationMethod = (
    connectionId: string,
    installationMethod: IECReferenceInstallationMethod | NECInstallationMethod | null | undefined,
    previousInstallationMethod: IECReferenceInstallationMethod | NECInstallationMethod | null | undefined,
) => {
    if (!installationMethod || !diagram.connections[connectionId]) {
        return;
    }

    if (installationMethod === previousInstallationMethod) {
        return;
    }

    if (previousInstallationMethod && isUnderground(installationMethod) === isUnderground(previousInstallationMethod)) {
        return;
    }

    const isUndergroundMethod = isUnderground(installationMethod);
    const temperatureValue = getDefaultTemperature(isUndergroundMethod);

    if (diagram.connections[connectionId]?.installation?.temperature?.value) {
        diagram.connections[connectionId].installation.temperature.value = temperatureValue;
    }
};

const updateCableArrangementBasedOnInstallationMethod = (
    connectionId: string,
    installationMethod: IECReferenceInstallationMethod | NECInstallationMethod | null | undefined,
    previousInstallationMethod: IECReferenceInstallationMethod | NECInstallationMethod | null | undefined,
) => {
    if (!installationMethod || !diagram.connections[connectionId]) {
        return;
    }

    if (installationMethod === previousInstallationMethod) {
        return;
    }

    const isUndergroundMethod = isUnderground(installationMethod);
    const wasUndergroundMethod = previousInstallationMethod ? isUnderground(previousInstallationMethod) : false;

    // Set to 'bunched' when switching to underground installation
    if (isUndergroundMethod && !wasUndergroundMethod) {
        if (diagram.connections[connectionId]?.installation) {
            diagram.connections[connectionId].installation.cableArrangement = 'bunched';
        }
    }
};

subscribe(
    diagram.connections,
    (changes) => {
        changes.forEach((change) => {
            const [type, paths, value_, previousValue_] = change;

            if (type === 'set') {
                const [id, ...fields] = paths;

                if (id && fields.length === 2 && fields[0] === 'installation' && fields[1] === 'method') {
                    syncInstallationMethod(String(id), value_ as DiagramConnection['installation']['method']);
                }

                if (id && fields.join('.') === 'installation.referenceMethod') {
                    const referenceMethod = value_ as IECReferenceInstallationMethod;
                    const previousReferenceMethod = previousValue_ as IECReferenceInstallationMethod | null | undefined;

                    updateTemperatureBasedOnInstallationMethod(String(id), referenceMethod, previousReferenceMethod);
                    updateCableArrangementBasedOnInstallationMethod(
                        String(id),
                        referenceMethod,
                        previousReferenceMethod,
                    );
                }

                if (id && fields.join('.') === 'installation.method') {
                    const method = value_ as NECInstallationMethod;
                    const previousMethod = previousValue_ as NECInstallationMethod | null | undefined;

                    updateTemperatureBasedOnInstallationMethod(String(id), method, previousMethod);
                    updateCableArrangementBasedOnInstallationMethod(String(id), method, previousMethod);
                }
            }
        });
    },
    // Important! notifyInSync true to run effects in sync with state updates
    true,
);
