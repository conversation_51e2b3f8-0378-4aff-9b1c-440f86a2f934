import { proxy } from 'valtio';

export type CanvasState = {
    grid: boolean;
    labels: {
        components: {
            all: boolean;
        };
        ports: {
            voltage: boolean;
            current: boolean;
            power: boolean;
            others: boolean;
        };
        connections: {
            voltage: boolean;
            current: boolean;
            wireSize: boolean;
            lines: boolean;
            others: boolean;
        };
    };
    scale: number;
    translation: {
        x: number;
        y: number;
    };
    inViewportChecks: boolean;
    componentInstancesInViewport: string[];
    connectingComponentInstances: string[];
    printing: boolean;
    printingViewportRecovery: {
        scale: number;
        translation: {
            x: number;
            y: number;
        };
    } | null;
};

const canvasState = proxy<CanvasState>({
    grid: true,
    labels: {
        components: {
            all: true,
        },
        ports: {
            voltage: true,
            current: true,
            power: true,
            others: true,
        },
        connections: {
            voltage: true,
            current: true,
            wireSize: true,
            lines: true,
            others: true,
        },
    },
    scale: 1,
    translation: {
        x: 0,
        y: 0,
    },
    inViewportChecks: true,
    componentInstancesInViewport: [],
    connectingComponentInstances: [],
    printing: false,
    printingViewportRecovery: null,
});

export { canvasState };
