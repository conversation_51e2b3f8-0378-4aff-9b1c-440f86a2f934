import React, { FC } from 'react';
import { CompanyProfile, CompanySubscription, getCompanySubscriptionData, Team } from 'models';

import { Stack, Text } from '@mantine/core';

import { CompanySubscriptionOptions } from '../subscriptions/CompanySubscriptionOptions';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { SubscriptionUpdateWrapper } from 'components/subscriptions/SubscriptionUpdateWrapper';
import { SubscriptionUpdateNotice } from 'components/subscriptions/components/SubscriptionUpdateNotice';
import { CompanySubscriptionUpdgradeCopy } from './CompanySubscriptionUpgradeCopy';

export const CompanyProfileSubscription: FC<{ team: Team; profileId: CompanyProfile['id'] }> = ({
    team,
    profileId,
}) => {
    return (
        <SubscriptionUpdateWrapper
            currentSubscription={
                getCompanySubscriptionData(team.subscriptions)?.subscription ?? CompanySubscription.NONE
            }
            team={team}
            postSubmitAction={() => CompanyProfileService.refresh(profileId)}
        >
            <Stack>
                <Text>
                    <CompanySubscriptionUpdgradeCopy />
                </Text>
                <SubscriptionUpdateNotice type="company" />
                <CompanySubscriptionOptions />
            </Stack>
        </SubscriptionUpdateWrapper>
    );
};
