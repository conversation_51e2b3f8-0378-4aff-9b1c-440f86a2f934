import { Flex, GroupProps, Stack, Text } from '@mantine/core';
import { BsLightningCharge } from 'react-icons/bs';
import { IoArrowDown, IoArrowUp, IoBatteryHalfOutline } from 'react-icons/io5';

import { Project, energyConverter, powerConverter } from 'models';

import { FormatHelpers } from 'helpers/formatters';
import { NumberHelpers } from 'helpers/NumberHelpers';

const DesignLibraryTeaserIndicators = ({
    project,
}: {
    project: Project;
} & GroupProps) => {
    const {
        template: { indicators },
    } = project;

    const { billOfMaterials, generation, grid, load, storage } = indicators ?? {};

    return (
        <Flex c="dimmed" fz={10} gap={8} justify="space-between" mr="xl">
            <ElectricalItem
                icon={<IoBatteryHalfOutline />}
                label="Storage"
                value={FormatHelpers.formatValue(storage, energyConverter)}
            />
            <ElectricalItem
                icon={<BsLightningCharge />}
                label="Grid"
                value={FormatHelpers.formatValue(grid, powerConverter)}
            />
            <ElectricalItem
                icon={<IoArrowUp />}
                label="Generated"
                value={FormatHelpers.formatValue(generation, powerConverter)}
            />
            <ElectricalItem
                icon={<IoArrowDown />}
                label="Load"
                value={FormatHelpers.formatValue(load, powerConverter)}
            />
            <ElectricalItem label="BOM" value={billOfMaterials ? NumberHelpers.formatPrice(billOfMaterials) : ''} />
        </Flex>
    );
};

const ElectricalItem = ({ icon, label, value }: { icon?: React.ReactNode; label: string; value?: string }) => {
    return (
        <Stack gap={4} align="center">
            <Flex gap={4} align="center">
                {icon}
                {label}
            </Flex>
            <Text inherit ta="center" c={value ? 'black' : 'inherit'} fw={value ? 500 : 'inherit'}>
                {value || '-'}
            </Text>
        </Stack>
    );
};

export { DesignLibraryTeaserIndicators };
