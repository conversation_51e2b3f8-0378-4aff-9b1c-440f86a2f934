import { StackProps } from '@mantine/core';

import { Project } from 'models';

import { useTeam } from 'hooks/use-team';
import { useUser } from 'hooks/use-user';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCompanyProfile } from 'hooks/use-company-profile';

const DesignLibraryTeaserCreatedBy = ({
    project,
}: {
    project: Project;
} & StackProps) => {
    const {
        createdBy,
        team: incomingTeam,
        template: { profile },
    } = project;

    const currentUser = useCurrentUser();

    const { user } = useUser(currentUser ? createdBy : undefined);
    const { team } = useTeam(currentUser ? incomingTeam : undefined);

    const { company } = useCompanyProfile(profile ?? null);

    if (company) {
        return <>Designed by {company.name}</>;
    }

    if (user && team) {
        return (
            <>
                Designed by {user.name} ∙ {team.name}
            </>
        );
    }

    return null;
};

export { DesignLibraryTeaserCreatedBy };
