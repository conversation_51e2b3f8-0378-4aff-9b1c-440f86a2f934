import Link from 'next/link';

import { Stack, Text, Tooltip } from '@mantine/core';
import { Carousel } from '@mantine/carousel';
import { IoChevronBack, IoChevronForward } from 'react-icons/io5';

import { Component } from 'models';

import { useComponentMeta } from 'hooks/use-component-meta';

import { ComponentThumbnail } from 'components/thumbnail';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import cx from './DesignLibraryTeaserComponents.module.scss';

const DesignLibraryTeaserComponents = ({ components = [] }: { components: Component[] }) => {
    if (components === null || components.length === 0) {
        return null;
    }

    return (
        <Carousel
            classNames={cx}
            emblaOptions={{
                align: 'start',
                loop: true,
                containScroll: 'trimSnaps',
                slidesToScroll: 3,
            }}
            height={50}
            draggable={false}
            slideGap={8}
            slideSize="auto"
            controlSize={20}
            controlsOffset={0}
            previousControlIcon={<IoChevronBack />}
            nextControlIcon={<IoChevronForward />}
        >
            {components
                // Filter out components that are not found
                .filter((component) => component.id)
                .map((component) => (
                    <Carousel.Slide key={component.id}>
                        <DesignLibraryTeaserComponent component={component} />
                    </Carousel.Slide>
                ))}
        </Carousel>
    );
};

const DesignLibraryTeaserComponent = ({ component }: { component: Component }) => {
    const { uniqueName, subtitle } = useComponentMeta(component);

    return (
        <Tooltip
            label={
                <Stack gap={0} ta="center">
                    <Text fz="xs">{uniqueName}</Text>
                    <Text fz="xs" c="dimmed">
                        {subtitle}
                    </Text>
                </Stack>
            }
            position="bottom"
        >
            <Link href={ComponentHelpers.urls.view(component.id)}>
                <ComponentThumbnail
                    showEmpty
                    component={component as Component}
                    style={{
                        width: 50,
                        height: 50,
                        padding: 2,
                        border: '1px solid var(--mantine-color-gray-2)',
                        flex: 'auto 0 0',
                    }}
                />
            </Link>
        </Tooltip>
    );
};

export { DesignLibraryTeaserComponents };
