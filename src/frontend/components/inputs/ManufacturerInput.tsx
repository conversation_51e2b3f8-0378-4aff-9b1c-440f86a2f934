import React, { useState } from 'react';

import {
    Combobox,
    Loader,
    Flex,
    Text,
    Input,
    InputBase,
    useCombobox,
    InputWrapperProps,
    ScrollArea,
    InputBaseProps,
} from '@mantine/core';

import { useCompanyProfiles } from 'hooks/use-company-profiles';

export type ManufacturerInputProps = Omit<InputWrapperProps, 'data'> & {
    value: string;
    name?: string;
    filterManufacturers?: string[];
    error?: string;
    disableAddManufacturers?: boolean;
    onOptionSubmit: (value: string, query: string) => Promise<void>;
    onBlur?: () => void;
    inputProps?: InputBaseProps;
    disabled?: boolean;
    showClaimed?: boolean;
};

const ManufacturerInput = ({
    name,
    value,
    filterManufacturers,
    error,
    disableAddManufacturers,
    onOptionSubmit,
    onBlur,
    inputProps,
    showClaimed,
    ...props
}: ManufacturerInputProps) => {
    const combobox = useCombobox();

    const [loading, setLoading] = useState(false);
    const [query, setQuery] = useState('');

    const { companies: manufacturers } = useCompanyProfiles();

    const isPrefiltered = filterManufacturers && filterManufacturers.length > 0;

    const data = manufacturers
        .filter((manufacturer) => !isPrefiltered || filterManufacturers.includes(manufacturer.id))
        .map((manufacturer: any) => ({
            value: manufacturer.id,
            label: manufacturer.name,
            logos: manufacturer.logos,
            internal: manufacturer.internal,
        }))
        .filter((manufacturer) => {
            return manufacturer.label.toLowerCase().includes(query.toLowerCase());
        });

    const selectedOption = data.find((option) => {
        return option.value === value;
    });

    const onOptionSubmitWrapper = async (value: string) => {
        setLoading(true);
        combobox.closeDropdown();
        await onOptionSubmit(value, query);
        setLoading(false);
    };

    return (
        <Input.Wrapper {...props}>
            <Combobox store={combobox} onOptionSubmit={onOptionSubmitWrapper} disabled={props.disabled}>
                <Combobox.Target>
                    <InputBase
                        name={name}
                        value={combobox.dropdownOpened ? query : selectedOption?.label || ''}
                        leftSection={loading ? <Loader size="14" /> : null}
                        rightSection={<Combobox.Chevron />}
                        rightSectionPointerEvents="none"
                        onChange={(event) => {
                            setQuery(event.currentTarget.value);
                        }}
                        onClick={() => {
                            combobox.openDropdown();
                        }}
                        onFocus={() => {
                            combobox.openDropdown();
                        }}
                        onBlur={() => {
                            combobox.closeDropdown();
                            onBlur && onBlur();
                        }}
                        placeholder="Select a manufacturer"
                        disabled={props.disabled}
                        {...inputProps}
                    />
                </Combobox.Target>
                <Combobox.Dropdown>
                    <Combobox.Options>
                        <ScrollArea.Autosize type="always" mah={200}>
                            {data.map((option) => (
                                <Combobox.Option value={option.value} key={option.value}>
                                    <Flex gap="xs">
                                        <Flex
                                            style={{
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                width: 16,
                                                height: 16,
                                            }}
                                        >
                                            {option.logos?.small && (
                                                <img
                                                    src={option.logos?.small?.url}
                                                    width={option.logos?.small?.width}
                                                    height={option.logos?.small?.height}
                                                    alt=""
                                                    style={{
                                                        width: '16px',
                                                        height: '16px',
                                                        objectFit: 'contain',
                                                    }}
                                                />
                                            )}
                                        </Flex>
                                        <Text size="sm">{option.label}</Text>
                                        {showClaimed && !option.internal && (
                                            <Text size="xs" c="green">
                                                Claimed
                                            </Text>
                                        )}
                                    </Flex>
                                </Combobox.Option>
                            ))}
                            {data.length === 0 &&
                                query &&
                                (disableAddManufacturers || isPrefiltered ? (
                                    <Combobox.Empty>Nothing found</Combobox.Empty>
                                ) : (
                                    <Combobox.Option value="$new">
                                        <Text size="sm">+ Add {query}</Text>
                                    </Combobox.Option>
                                ))}
                        </ScrollArea.Autosize>
                    </Combobox.Options>
                </Combobox.Dropdown>
            </Combobox>
            {error && <Input.Error mt={5}>{error}</Input.Error>}
        </Input.Wrapper>
    );
};

export { ManufacturerInput };
