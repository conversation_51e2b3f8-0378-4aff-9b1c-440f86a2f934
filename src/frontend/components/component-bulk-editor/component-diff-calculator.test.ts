import { expect, test } from '@jest/globals';
import { getDiffAndSameKeys } from './component-diff-calculator';
import { AMPT_STRING_OPTIMIZERS } from './test-data';

test('diff calculator should return differing fields', () => {
    const { diff, same } = getDiffAndSameKeys(AMPT_STRING_OPTIMIZERS);

    for (const key of [
        'id',
        'productIdentifier',
        'electrical.ports.0.DC.voltage.max',
        'electrical.ports.0.DC.power.max',
        'electrical.ports.1.DC.voltage.nom',
        'electrical.ports.1.DC.voltage.max',
        'electrical.ports.1.DC.power.max',
        'mechanical.weight.value',
        'createdAt',
    ]) {
        expect(diff).toContain(key);
    }

    for (const key of [
        'description',
        'type',
        'manufacturer',
        'productSeries',
        'website',
        'electrical.ports.0.enabled',
        'electrical.ports.0.DC.enabled',
        'electrical.ports.0.DC.voltage.unit',
        'electrical.ports.0.DC.voltage.min',
        'electrical.ports.0.DC.voltage.nom',
        'electrical.ports.0.DC.current.unit',
        'electrical.ports.0.DC.current.nom',
        'electrical.ports.0.DC.current.max',
        'electrical.ports.0.DC.power.unit',
        'electrical.ports.0.DC.power.nom',

        // Array fields
        'mechanical.mountingType',
        'communication.interfaces',
        'communication.protocols',
        'standards',
        // 'compatibleWith',
        // 'distributors',
        'distributorsDetails',
        // 'compatibleWithPlaceholders',
        'projects',
    ]) {
        expect(same).toContain(key);
    }
});
