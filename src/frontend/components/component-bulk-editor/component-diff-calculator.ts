import { unique } from 'radash';

import { Component, crush } from 'models';

type KeyValue = { [key: string]: any };

type DiffAndSameKeys = {
    diff: string[];
    same: string[];
};

const KEYS_TO_IGNORE: string[] = ['files'];

const compareValues = (valueA: any, valueB: any): boolean => {
    if (Array.isArray(valueA) && Array.isArray(valueB)) {
        if (valueA.length !== valueB.length) {
            return false;
        }

        for (let i = 0; i < valueA.length; i++) {
            if (!valueB.includes(valueA[i])) {
                return false;
            }
        }

        return true;
    }

    return valueA === valueB;
};

const compare = (a: KeyValue, b: KeyValue): DiffAndSameKeys => {
    const result = {
        diff: [] as string[],
        same: [] as string[],
    };

    const keys = Object.keys(a);

    for (const key of keys) {
        const valueA = a[key];
        const valueB = b[key];

        if (compareValues(valueA, valueB)) {
            result.same.push(key);
        } else {
            result.diff.push(key);
        }
    }

    return result;
};

export const getDiffAndSameKeys = (components: Component[]): DiffAndSameKeys => {
    const crushedComponents: Record<string, any>[] = components.map((component) => crush(component, KEYS_TO_IGNORE));

    const result = {
        diff: [] as string[],
        same: [] as string[],
    };

    for (let a = 0; a < crushedComponents.length - 1; a++) {
        for (let b = a + 1; b < crushedComponents.length; b++) {
            const { diff, same } = compare(crushedComponents[a], crushedComponents[b]);

            result.diff.push(...diff);
            result.same.push(...same);
        }
    }

    result.diff = unique(result.diff);
    // remove keys that are also part of diff
    result.same = unique(result.same).filter((key) => !result.diff.includes(key));

    return result;
};
