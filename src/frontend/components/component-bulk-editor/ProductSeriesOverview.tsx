import React, { FC } from 'react';

import Link from 'next/link';

import { List, ListItem, Stack, Title, Text, Card } from '@mantine/core';

import { ProductSeriesMeta } from 'models';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { useComponentMetaSubtitle } from 'hooks/use-component-meta';

import { EmptyMessage } from 'components/empty-message/EmptyMessage';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { getId } from 'helpers/getId';

import { CarouselSection } from 'components/section/CarouselSection';

import cx from './ProductSeriesOverview.module.scss';

const ProductSeriesOverview: FC<{ productSeries?: ProductSeriesMeta[] }> = ({ productSeries }) => {
    return (
        <>
            {!productSeries?.length ? (
                <EmptyMessage>You currently do not have edit access to any product series.</EmptyMessage>
            ) : (
                <CarouselSection nbCols={4}>
                    {productSeries.map((meta) => (
                        <ProductSeriesItem key={meta.productSeries} meta={meta} />
                    ))}
                </CarouselSection>
            )}
        </>
    );
};

const ProductSeriesItem: FC<{ meta: ProductSeriesMeta }> = ({ meta }) => {
    const { company: manufacturer } = useCompanyProfile(meta.manufacturer);
    const { subtitle } = useComponentMetaSubtitle(meta.type, manufacturer);

    const getProductIdentifier = (component: ProductSeriesMeta['components'][number]) =>
        component.productIdentifier || component.name;
    const componentsText =
        meta.components.length > 5
            ? [...meta.components.slice(0, 5).map(getProductIdentifier), '...']
            : meta.components.map(getProductIdentifier);

    return (
        <Card
            className={cx.item}
            component={Link}
            href={ComponentHelpers.urls.bulk(getId(meta.manufacturer)!, meta.productSeries)}
        >
            <Stack gap="sm">
                <Stack gap={0}>
                    <Title order={3} fz="lg" fw={700} c="brand" className={cx.title}>
                        {meta.productSeries}
                    </Title>
                    <Text c="gray.5" fz="sm" fw={600}>
                        {subtitle}
                    </Text>
                </Stack>

                <List size="xs" c="dimmed">
                    {componentsText.map((text) => (
                        <ListItem key={text}>{text}</ListItem>
                    ))}
                </List>
            </Stack>
        </Card>
    );
};

export { ProductSeriesOverview };
