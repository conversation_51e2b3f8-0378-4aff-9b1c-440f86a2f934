// @ts-nocheck
import { Component } from 'models';

export const AMPT_STRING_OPTIMIZERS: Component[] = [
    {
        id: '64d11653ee1f97d54804d6d5',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1500-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 28000,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 1420,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 28000,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 13.1,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8a41b213b30001547ae6',
            },
            {
                file: '64d1147aee1f97d54804d49f',
                type: 'datasheet',
                id: '64fb8a41b213b30001547ae7',
            },
        ],
        images: [
            {
                file: '64fb8a3a9b7801542f902198',
                type: 'thumbnail',
                id: '64fb8a41b213b30001547ae8',
            },
            {
                file: '64fb8a3f9b7801542f9021de',
                type: 'ISOPicture',
                id: '64fb8a41b213b30001547ae9',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8a41b213b30001547aea',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8a41b213b30001547aeb',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8a41b213b30001547aec',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8a41b213b30001547aed',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8a41b213b30001547aee',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8a41b213b30001547aef',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            ODCA: true,
            emergeAlliance: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T16:05:39.275Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d1162eee1f97d54804d68e',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1475-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 27800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 1395,
                            max: 1475,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 27800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 13.1,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8a56b213b30001547af0',
            },
            {
                file: '64d1147aee1f97d54804d49f',
                type: 'datasheet',
                id: '64fb8a56b213b30001547af1',
            },
        ],
        images: [
            {
                file: '64fb8a4d9b7801542f902266',
                type: 'thumbnail',
                id: '64fb8a56b213b30001547af2',
            },
            {
                file: '64fb8a539b7801542f9022ac',
                type: 'ISOPicture',
                id: '64fb8a56b213b30001547af3',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8a56b213b30001547af4',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8a56b213b30001547af5',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8a56b213b30001547af6',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8a56b213b30001547af7',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8a56b213b30001547af8',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8a56b213b30001547af9',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T16:05:02.326Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d1160bee1f97d54804d647',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1450-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 27300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 1370,
                            max: 1450,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 27300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 13.1,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8a6fb213b30001547b04',
            },
            {
                file: '64d1147aee1f97d54804d49f',
                type: 'datasheet',
                id: '64fb8a6fb213b30001547b05',
            },
        ],
        images: [
            {
                file: '64fb8a669b7801542f902361',
                type: 'thumbnail',
                id: '64fb8a6fb213b30001547b06',
            },
            {
                file: '64fb8a6c9b7801542f9023ad',
                type: 'ISOPicture',
                id: '64fb8a6fb213b30001547b07',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8a6fb213b30001547b08',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8a6fb213b30001547b09',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8a6fb213b30001547b0a',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8a6fb213b30001547b0b',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8a6fb213b30001547b0c',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8a6fb213b30001547b0d',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T16:04:27.757Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d115cbee1f97d54804d600',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1425-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 26800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 1345,
                            max: 1425,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 26800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 13.1,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8a83b213b30001547b0e',
            },
            {
                file: '64d1147aee1f97d54804d49f',
                type: 'datasheet',
                id: '64fb8a83b213b30001547b0f',
            },
        ],
        images: [
            {
                file: '64fb8a7b9b7801542f90243e',
                type: 'thumbnail',
                id: '64fb8a83b213b30001547b10',
            },
            {
                file: '64fb8a809b7801542f90248d',
                type: 'ISOPicture',
                id: '64fb8a83b213b30001547b11',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8a83b213b30001547b12',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8a83b213b30001547b13',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8a83b213b30001547b14',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8a83b213b30001547b15',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8a83b213b30001547b16',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8a83b213b30001547b17',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T16:03:23.719Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d1159eee1f97d54804d5b9',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1400-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 26300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 1320,
                            max: 1400,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 26300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 13.1,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8b03b213b30001547b18',
            },
            {
                file: '64d1147aee1f97d54804d49f',
                type: 'datasheet',
                id: '64fb8b03b213b30001547b19',
            },
        ],
        images: [
            {
                file: '64fb8af29b7801542f902621',
                type: 'thumbnail',
                id: '64fb8b03b213b30001547b1a',
            },
            {
                file: '64fb8afc9b7801542f902655',
                type: 'ISOPicture',
                id: '64fb8b03b213b30001547b1b',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8b03b213b30001547b1c',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8b03b213b30001547b1d',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8b03b213b30001547b1e',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8b03b213b30001547b1f',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8b03b213b30001547b20',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8b03b213b30001547b21',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T16:02:38.409Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d114dbee1f97d54804d4ca',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1375-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 25800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 1295,
                            max: 1375,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 25800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 13.1,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8b35b213b30001547b22',
            },
            {
                file: '64d1147aee1f97d54804d49f',
                type: 'datasheet',
                id: '64fb8b35b213b30001547b23',
            },
        ],
        images: [
            {
                file: '64fb8b209b7801542f9026ce',
                type: 'thumbnail',
                id: '64fb8b35b213b30001547b24',
            },
            {
                file: '64fb8b2d9b7801542f902705',
                type: 'ISOPicture',
                id: '64fb8b35b213b30001547b25',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8b35b213b30001547b26',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8b35b213b30001547b27',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8b35b213b30001547b28',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8b35b213b30001547b29',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8b35b213b30001547b2a',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8b35b213b30001547b2b',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:59:23.090Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d11457ee1f97d54804d45e',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1350-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1500,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 25300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 1270,
                            max: 1350,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 25300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 13.1,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8b5bb213b30001547b2c',
            },
            {
                file: '64d1147aee1f97d54804d49f',
                type: 'datasheet',
                id: '64fb8b5bb213b30001547b2d',
            },
        ],
        images: [
            {
                file: '64fb8b529b7801542f902781',
                type: 'thumbnail',
                id: '64fb8b5bb213b30001547b2e',
            },
            {
                file: '64fb8b589b7801542f9027bb',
                type: 'ISOPicture',
                id: '64fb8b5bb213b30001547b2f',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8b5bb213b30001547b30',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8b5bb213b30001547b31',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8b5bb213b30001547b32',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8b5bb213b30001547b33',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8b5bb213b30001547b34',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8b5bb213b30001547b35',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:57:11.417Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10f8bee1f97d54804d2fb',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V1000-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 18300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 920,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 18300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8ba8b213b30001547b36',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8ba8b213b30001547b37',
            },
        ],
        images: [
            {
                file: '64fb8b7f9b7801542f90283a',
                type: 'thumbnail',
                id: '64fb8ba8b213b30001547b38',
            },
            {
                file: '64fb8b969b7801542f902877',
                type: 'ISOPicture',
                id: '64fb8ba8b213b30001547b39',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8ba8b213b30001547b3a',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8ba8b213b30001547b3b',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8ba8b213b30001547b3c',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8ba8b213b30001547b3d',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8ba8b213b30001547b3e',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8ba8b213b30001547b3f',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:36:43.277Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10f67ee1f97d54804d2b4',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V975-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 17800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 895,
                            max: 975,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 17800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8bd1b213b30001547b40',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8bd1b213b30001547b41',
            },
        ],
        images: [
            {
                file: '64fb8bc29b7801542f9028f9',
                type: 'thumbnail',
                id: '64fb8bd1b213b30001547b42',
            },
            {
                file: '64fb8bca9b7801542f902939',
                type: 'ISOPicture',
                id: '64fb8bd1b213b30001547b43',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8bd1b213b30001547b44',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8bd1b213b30001547b45',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8bd1b213b30001547b46',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8bd1b213b30001547b47',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8bd1b213b30001547b48',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8bd1b213b30001547b49',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:36:07.052Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10f43ee1f97d54804d26d',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V950-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 17300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 870,
                            max: 950,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 17300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8c0eb213b30001547b4a',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8c0eb213b30001547b4b',
            },
        ],
        images: [
            {
                file: '64fb8bff9b7801542f9029be',
                type: 'thumbnail',
                id: '64fb8c0eb213b30001547b4c',
            },
            {
                file: '64fb8c079b7801542f902a01',
                type: 'ISOPicture',
                id: '64fb8c0eb213b30001547b4d',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8c0eb213b30001547b4e',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8c0eb213b30001547b4f',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8c0eb213b30001547b50',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8c0eb213b30001547b51',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8c0eb213b30001547b52',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8c0eb213b30001547b53',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:35:31.521Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10f1eee1f97d54804d226',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V925-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 16800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 845,
                            max: 925,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 16800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8c36b213b30001547b54',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8c36b213b30001547b55',
            },
        ],
        images: [
            {
                file: '64fb8c2d9b7801542f902a89',
                type: 'thumbnail',
                id: '64fb8c36b213b30001547b56',
            },
            {
                file: '64fb8c339b7801542f902acf',
                type: 'ISOPicture',
                id: '64fb8c36b213b30001547b57',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8c36b213b30001547b58',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8c36b213b30001547b59',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8c36b213b30001547b5a',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8c36b213b30001547b5b',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8c36b213b30001547b5c',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8c36b213b30001547b5d',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:34:54.577Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10ef8ee1f97d54804d1df',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V900-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 16300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 820,
                            max: 900,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 16300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8c6cb213b30001547b5e',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8c6cb213b30001547b5f',
            },
        ],
        images: [
            {
                file: '64fb8c5a9b7801542f902b5a',
                type: 'thumbnail',
                id: '64fb8c6cb213b30001547b60',
            },
            {
                file: '64fb8c629b7801542f902ba3',
                type: 'ISOPicture',
                id: '64fb8c6cb213b30001547b61',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8c6cb213b30001547b62',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8c6cb213b30001547b63',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8c6cb213b30001547b64',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8c6cb213b30001547b65',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8c6cb213b30001547b66',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8c6cb213b30001547b67',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:34:16.340Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10ecdee1f97d54804d198',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V875-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 15800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 795,
                            max: 875,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 15800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8c93b213b30001547b68',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8c93b213b30001547b69',
            },
        ],
        images: [
            {
                file: '64fb8c849b7801542f902c31',
                type: 'thumbnail',
                id: '64fb8c93b213b30001547b6a',
            },
            {
                file: '64fb8c8c9b7801542f902c7d',
                type: 'ISOPicture',
                id: '64fb8c93b213b30001547b6b',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8c93b213b30001547b6c',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8c93b213b30001547b6d',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8c93b213b30001547b6e',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8c93b213b30001547b6f',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8c93b213b30001547b70',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8c93b213b30001547b71',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:33:33.128Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10e6dee1f97d54804d096',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V850-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 15300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 770,
                            max: 850,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 15300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8cb4b213b30001547b72',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8cb4b213b30001547b73',
            },
        ],
        images: [
            {
                file: '64fb8ca79b7801542f902d0e',
                type: 'thumbnail',
                id: '64fb8cb4b213b30001547b74',
            },
            {
                file: '64fb8cb19b7801542f902d5d',
                type: 'ISOPicture',
                id: '64fb8cb4b213b30001547b75',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8cb4b213b30001547b76',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8cb4b213b30001547b77',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8cb4b213b30001547b78',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8cb4b213b30001547b79',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8cb4b213b30001547b7a',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8cb4b213b30001547b7b',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:31:57.763Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10e01ee1f97d54804cf78',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V825-I20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 14800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 745,
                            max: 825,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 14800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8cf5b213b30001547b7c',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8cf5b213b30001547b7d',
            },
        ],
        images: [
            {
                file: '64fb8cd99b7801542f902de1',
                type: 'thumbnail',
                id: '64fb8cf5b213b30001547b7e',
            },
            {
                file: '64fb8cec9b7801542f902e15',
                type: 'ISOPicture',
                id: '64fb8cf5b213b30001547b7f',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8cf5b213b30001547b80',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8cf5b213b30001547b81',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8cf5b213b30001547b82',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8cf5b213b30001547b83',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8cf5b213b30001547b84',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8cf5b213b30001547b85',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:30:09.283Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d10d7eee1f97d54804ce68',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V800-I20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 1000,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 14300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 720,
                            max: 800,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 14300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8d1fb213b30001547b86',
            },
            {
                file: '64d10dadee1f97d54804cf37',
                type: 'other',
                id: '64fb8d1fb213b30001547b87',
            },
        ],
        images: [
            {
                file: '64fb8d0f9b7801542f902e8e',
                type: 'thumbnail',
                id: '64fb8d1fb213b30001547b88',
            },
            {
                file: '64fb8d189b7801542f902ec5',
                type: 'ISOPicture',
                id: '64fb8d1fb213b30001547b89',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8d1fb213b30001547b8a',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8d1fb213b30001547b8b',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8d1fb213b30001547b8c',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8d1fb213b30001547b8d',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8d1fb213b30001547b8e',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8d1fb213b30001547b8f',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T15:27:58.581Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d0ff128248610d1b4a90a0',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V750-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 750,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 13300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 670,
                            max: 750,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 13300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa3d8248610d1b4a8daf',
                type: 'datasheet',
                id: '64fb8d44b213b30001547b90',
            },
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8d44b213b30001547b91',
            },
        ],
        images: [
            {
                file: '64fb8d399b7801542f902f41',
                type: 'thumbnail',
                id: '64fb8d44b213b30001547b92',
            },
            {
                file: '64fb8d3f9b7801542f902f7b',
                type: 'ISOPicture',
                id: '64fb8d44b213b30001547b93',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8d44b213b30001547b94',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8d44b213b30001547b95',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8d44b213b30001547b96',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8d44b213b30001547b97',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8d44b213b30001547b98',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8d44b213b30001547b99',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T14:26:26.151Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d0fee48248610d1b4a9059',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V725-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 750,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 12800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 645,
                            max: 725,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 12800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa3d8248610d1b4a8daf',
                type: 'datasheet',
                id: '64fb8d71b213b30001547b9a',
            },
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8d71b213b30001547b9b',
            },
        ],
        images: [
            {
                file: '64fb8d5d9b7801542f902ffa',
                type: 'thumbnail',
                id: '64fb8d71b213b30001547b9c',
            },
            {
                file: '64fb8d679b7801542f903037',
                type: 'ISOPicture',
                id: '64fb8d71b213b30001547b9d',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8d71b213b30001547b9e',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8d71b213b30001547b9f',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8d71b213b30001547ba0',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8d71b213b30001547ba1',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8d71b213b30001547ba2',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8d71b213b30001547ba3',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T14:25:40.141Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d0feb68248610d1b4a9012',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V700-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 750,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 12300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 620,
                            max: 700,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 12300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa3d8248610d1b4a8daf',
                type: 'datasheet',
                id: '64fb8d8fb213b30001547ba4',
            },
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8d8fb213b30001547ba5',
            },
        ],
        images: [
            {
                file: '64fb8d839b7801542f9030b9',
                type: 'thumbnail',
                id: '64fb8d8fb213b30001547ba6',
            },
            {
                file: '64fb8d8b9b7801542f9030f9',
                type: 'ISOPicture',
                id: '64fb8d8fb213b30001547ba7',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8d8fb213b30001547ba8',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8d8fb213b30001547ba9',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8d8fb213b30001547baa',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8d8fb213b30001547bab',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8d8fb213b30001547bac',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8d8fb213b30001547bad',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T14:24:54.889Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d0fe258248610d1b4a8f20',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V675-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 750,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 11800,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 595,
                            max: 675,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 11800,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa3d8248610d1b4a8daf',
                type: 'datasheet',
                id: '64fb8db5b213b30001547bae',
            },
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8db5b213b30001547baf',
            },
        ],
        images: [
            {
                file: '64fb8da09b7801542f90317e',
                type: 'thumbnail',
                id: '64fb8db5b213b30001547bb0',
            },
            {
                file: '64fb8dad9b7801542f9031c1',
                type: 'ISOPicture',
                id: '64fb8db5b213b30001547bb1',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8db5b213b30001547bb2',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8db5b213b30001547bb3',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8db5b213b30001547bb4',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8db5b213b30001547bb5',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8db5b213b30001547bb6',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8db5b213b30001547bb7',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fdff8248610d1b4a8f0c',
            '64d0fe138248610d1b4a8f16',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T14:22:29.370Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
    {
        id: '64d0fa258248610d1b4a8d6e',
        name: 'String Optimizer',
        description:
            'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
        type: 'converter',
        manufacturer: '64cbd363ac05e7f93f90830a',
        productSeries: 'i20',
        msrp: null,
        leadTime: null,
        productIdentifier: 'V650-i20-20',
        website: 'https://www.ampt.com/',
        lifecycle: {},
        electrical: {
            ports: [
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: 0,
                            max: 750,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 12.8,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 11300,
                        },
                        configuration: 'unipolar',
                        controlMethods: ['constant-voltage'],
                    },
                    powerFlowDirection: 'input',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
                {
                    enabled: true,
                    AC: {
                        enabled: false,
                        voltage: {
                            unit: 'V',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: null,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: null,
                        },
                        frequency: {
                            unit: 'Hz',
                            min: null,
                            nom: null,
                            max: null,
                        },
                        powerFactor: 0,
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            unit: 'V',
                            min: 0,
                            nom: 570,
                            max: 650,
                        },
                        current: {
                            unit: 'A',
                            nom: null,
                            max: 20,
                        },
                        power: {
                            unit: 'W',
                            nom: null,
                            max: 11300,
                        },
                        configuration: 'unipolar',
                        controlMethods: [],
                    },
                    powerFlowDirection: 'output',
                    isolated: true,
                    parallelableCapacity: 1,
                    capacitance: {
                        unit: 'F',
                        value: null,
                    },
                    features: [],
                },
            ],
            standards: [],
            isolationVoltage: {
                unit: 'kV',
                value: null,
            },
        },
        communication: {
            interfaces: [],
            protocols: [],
        },
        environmental: {
            operatingTemperature: {
                unit: 'K',
                min: 313.15,
                max: 328.15,
            },
            storageTemperature: {
                unit: 'K',
                min: null,
                max: 269.15,
            },
            operatingHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            storageHumidity: {
                unit: '%',
                min: null,
                max: null,
            },
            ingressProtection_IP: 'IP66',
            ingressProtection_NEMA: '4X',
            coolingMethod: 'passive',
        },
        performance: {
            standbyPower: {
                unit: 'W',
                value: null,
            },
            efficiency: {
                unit: '%',
                nom: 99.2,
                max: 99.5,
            },
            losses: {
                unit: 'W',
                nom: null,
                max: null,
            },
        },
        mechanical: {
            dimensions: {
                unit: 'm',
                width: 0.3386,
                length: 0.24,
                height: 0.1082,
            },
            weight: {
                unit: 'kg',
                value: 5.9,
            },
            mountingType: ['wall', 'panel'],
        },
        files: [
            {
                file: '64d0fa3d8248610d1b4a8daf',
                type: 'datasheet',
                id: '64fb8dd1b213b30001547bb8',
            },
            {
                file: '64d0fa4b8248610d1b4a8dc9',
                type: 'installationManual',
                id: '64fb8dd1b213b30001547bb9',
            },
        ],
        images: [
            {
                file: '64fb8dc59b7801542f903249',
                type: 'thumbnail',
                id: '64fb8dd1b213b30001547bba',
            },
            {
                file: '64fb8dcd9b7801542f90328f',
                type: 'ISOPicture',
                id: '64fb8dd1b213b30001547bbb',
            },
            {
                file: null,
                type: 'front',
                id: '64fb8dd1b213b30001547bbc',
            },
            {
                file: null,
                type: 'rear',
                id: '64fb8dd1b213b30001547bbd',
            },
            {
                file: null,
                type: 'left',
                id: '64fb8dd1b213b30001547bbe',
            },
            {
                file: null,
                type: 'right',
                id: '64fb8dd1b213b30001547bbf',
            },
            {
                file: null,
                type: 'top',
                id: '64fb8dd1b213b30001547bc0',
            },
            {
                file: null,
                type: 'bottom',
                id: '64fb8dd1b213b30001547bc1',
            },
        ],
        compliance: {
            CE: true,
            UL: true,
            currentOS: true,
            emergeAlliance: true,
            ODCA: true,
        },
        standards: [
            '64f23b4b098c58f1c0bf116a',
            '64f9d3869b7801542f8ef4ca',
            '64f9d3949b7801542f8ef4d4',
            '64f9e3cc9b7801542f8f1a51',
            '64f238f6098c58f1c0bf0e69',
            '64d0fe138248610d1b4a8f16',
            '64d0fdff8248610d1b4a8f0c',
        ],
        archivedAt: null,
        createdBy: '65454b6925fd45182e5ca838',
        team: '65454b6925fd45182e5ca83a',
        createdAt: '2023-08-07T14:05:25.997Z',
        updatedAt: '2024-02-22T19:05:11.202Z',
        metadata: {
            communication: {
                interfaces: {
                    display: 'na',
                },
                protocols: {
                    display: 'na',
                },
            },
            electrical: {
                isolationVoltage: {
                    display: 'unknown',
                },
                ports: {
                    port1: {
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                    port2: {
                        DC: {
                            controlMethods: {
                                display: 'unknown',
                            },
                        },
                        capacitance: {
                            display: 'unknown',
                        },
                        features: {
                            display: 'unknown',
                        },
                    },
                },
            },
            environmental: {
                operatingHumidity: {
                    display: 'unknown',
                },
                storageHumidity: {
                    display: 'unknown',
                },
                storageTemperature: {
                    display: 'unknown',
                },
            },
            performance: {
                losses: {
                    display: 'unknown',
                },
                standbyPower: {
                    display: 'unknown',
                },
            },
        },
        reviewed: false,
        visibility: 'public',
        videos: [],
        projects: [],
        distributorsDetails: [],
        sortRank: 0,
    },
];
