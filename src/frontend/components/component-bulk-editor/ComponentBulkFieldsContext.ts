import { Component } from 'models';
import { createContext, useContext } from 'react';

export const ComponentBulkFieldsContext = createContext<{
    same: string[];
    diff: string[];
    overrides: string[];
    addOverride: (key: string) => void;
    removeOverride: (key: string) => void;
    bulkSave: (component: Component) => void;
}>({
    same: [],
    diff: [],
    overrides: [],
    addOverride: () => {},
    removeOverride: () => {},
    bulkSave: () => {},
});

export const useComponentBulkFields = () => useContext(ComponentBulkFieldsContext);
