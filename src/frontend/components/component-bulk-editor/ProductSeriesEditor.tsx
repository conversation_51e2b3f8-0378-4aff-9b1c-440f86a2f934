import { FC, useMemo, useState } from 'react';
import { Page } from 'components/page';
import { Loader } from '@mantine/core';
import { Component, crush, getComponentPayloadValidator } from 'models';
import { useProductSeriesComponents } from 'hooks/use-product-series-components';
import { getDiffAndSameKeys } from './component-diff-calculator';
import { construct, pick } from 'radash';
import { ComponentDetail } from 'components/component-detail';
import { ComponentBulkFieldsContext } from './ComponentBulkFieldsContext';
import { ComponentService } from 'services/ComponentService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { DatasheetMode } from 'components/datasheet';

const ProductSeriesEditor: FC<{ manufacturerId: string; productSeries: string }> = ({
    manufacturerId,
    productSeries,
}) => {
    const { components, isLoading } = useProductSeriesComponents({ manufacturerId, productSeries });

    if (!isLoading && components) {
        if (!Array.isArray(components) || components.length === 0) {
            return <NoComponents productSeries={productSeries} />;
        }

        return <Editor components={components} productSeries={productSeries} />;
    }

    return (
        <Page showBackground>
            <Page.CenteredContent>
                <Loader />
            </Page.CenteredContent>
        </Page>
    );
};

const Editor: FC<{ components: Component[]; productSeries: string }> = ({ components, productSeries }) => {
    const { diff, same } = useMemo(() => getDiffAndSameKeys(components), [components]);

    const editableComponent = useMemo(() => {
        const base = crush(getComponentPayloadValidator(components[0].type).parse({ name: components[0].name }));
        const sameFields = pick<any, string>(crush(components[0]), same);

        return construct({ ...base, ...sameFields });
    }, [components, same]) as Component;

    const [overrides, setOverrides] = useState<string[]>([]);

    const bulkSave = async (component: Component) => {
        const newFiles = component.files ?? [];
        const changesToSave = construct(pick<any, string>(crush(component), [...same, ...overrides])) as Component;

        try {
            await ComponentService.updateMultipleComponents({
                componentIds: components.map(({ id }) => id),
                update: changesToSave,
                newFiles,
            });

            LocalNotificationService.showSuccess({ message: 'Components updated!' });
        } catch (error: any) {
            LocalNotificationService.showError({ message: error?.message ?? 'Error updating components' });
        }
    };

    if (diff.includes('type')) {
        return (
            <ErrorComponent
                productSeries={productSeries}
                error={
                    `This product series has components of different types. ` +
                    `Bulk editing accross types is currently not supported.` +
                    `If you would like to edit these components in bulk, please ensure all components in the series are of the same type.`
                }
            />
        );
    }

    return (
        <ComponentBulkFieldsContext.Provider
            value={{
                diff,
                same,
                overrides,
                addOverride: (key) => setOverrides((overrides) => [...overrides, key]),
                removeOverride: (key) =>
                    setOverrides((overrides) => overrides.filter((existingKey) => key !== existingKey)),
                bulkSave,
            }}
        >
            <ComponentDetail component={editableComponent} initialMode={DatasheetMode.BULK} />
        </ComponentBulkFieldsContext.Provider>
    );
};

const NoComponents = ({ productSeries }: { productSeries: string }) => (
    <Page showBackground title={`Editing series: ${productSeries}`}>
        <Page.Content title={`Editing series: ${productSeries}`} subtitle="No components found" />
    </Page>
);

const ErrorComponent = ({ error, productSeries }: { error: string; productSeries: string }) => (
    <Page showBackground title={`Editing series: ${productSeries}`}>
        <Page.CenteredContent title={`Editing series: ${productSeries}`} subtitle={error} />
    </Page>
);

export { ProductSeriesEditor };
