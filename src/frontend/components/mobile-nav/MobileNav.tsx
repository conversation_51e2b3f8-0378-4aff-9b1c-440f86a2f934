import { useCallback, useEffect, useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/router';

import { Badge, Box, Paper, Portal, SimpleGrid, Space, UnstyledButton } from '@mantine/core';
import { IoSearch, IoSearchSharp, IoSparkles, IoSparklesOutline } from 'react-icons/io5';
import { BsPersonVcard, BsPersonVcardFill, BsThreeDots } from 'react-icons/bs';

import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';
import { SearchPlanHelpers } from 'components/search-plan/SearchPlanHelpers';

import { NavigationService } from 'services/NavigationService';

import { useMobileNav } from 'hooks/use-mobile-nav';
import { useSidebarNav } from 'hooks/use-sidebar-nav';
import { useShowShowtimeNav } from 'hooks/use-show-showtime-nav';

import { ReplusLogo } from 'components/logo/ReplusLogo';

import cx from './MobileNav.module.scss';

export const MOBILE_NAV_HEIGHT = 60;

enum MobileNavItemKey {
    SHOWTIME = 'showtime',
    GET_MATCHED = 'getMatched',
    PROFILES = 'profiles',
    PRODUCTS = 'products',
    MORE = 'more',
}

export type MobileNavProps = {
    withSpace?: boolean;
    variant?: 'light' | 'dark';
};

type MobileNavItem = {
    key: MobileNavItemKey;
    icon: React.ReactNode;
    iconDark?: React.ReactNode;
    iconActive: React.ReactNode;
    label: string;
    badge?: React.ReactNode;
    href?: string;
    onClick?: () => void;
};

const MOBILE_NAV_ITEMS: MobileNavItem[] = [
    {
        key: MobileNavItemKey.SHOWTIME,
        icon: <ReplusLogo />,
        iconActive: <ReplusLogo />,
        iconDark: <ReplusLogo isWhite />,
        label: 'Showtime',
        href: ShowtimeHelpers.urls.showtime(),
    },
    {
        key: MobileNavItemKey.GET_MATCHED,
        icon: <IoSparklesOutline />,
        iconActive: <IoSparkles />,
        label: 'Get Matched',
        badge: (
            <Badge size="xs" variant="gradient" color="primary" className={cx.badge}>
                AI
            </Badge>
        ),
        onClick: () => {
            SearchPlanHelpers.switchPage('plan');
        },
    },
    {
        key: MobileNavItemKey.PRODUCTS,
        icon: <IoSearch />,
        iconActive: <IoSearchSharp />,
        label: 'Products',
        onClick: () => {
            SearchPlanHelpers.switchPage('search', 'products');
        },
    },
    {
        key: MobileNavItemKey.PROFILES,
        icon: <BsPersonVcard />,
        iconActive: <BsPersonVcardFill />,
        label: 'Profiles',
        onClick: () => {
            SearchPlanHelpers.switchPage('search', 'profiles');
        },
    },
    {
        key: MobileNavItemKey.MORE,
        icon: <BsThreeDots />,
        iconActive: <BsThreeDots />,
        label: 'More',
        onClick: () => {
            NavigationService.toggleMobile();
        },
    },
    // {
    //     key: MobileNavItemKey.NOTES,
    //     icon: <BsPencil />,
    //     iconActive: <BsPencilFill />,
    //     label: 'My Notes',
    //     badge: 'AI',
    //     href: '/summary',
    // },
];

const MobileNav = ({ withSpace = true, variant = 'light' }: MobileNavProps) => {
    const showShowtimeNav = useShowShowtimeNav();

    const router = useRouter();
    const { isMobileOpen } = useSidebarNav();

    const { showMobileNav } = useMobileNav();

    const getActiveItem = useCallback(() => {
        return MOBILE_NAV_ITEMS.find((item: { href?: string }) => item.href && router.asPath.includes(item.href))?.key;
    }, [router.asPath]);

    const [activeItem, setActiveItem] = useState<MobileNavItemKey | undefined>(getActiveItem());

    const handleItemClick = (key: MobileNavItemKey) => {
        setActiveItem(key);
    };

    useEffect(() => {
        const activeItem = getActiveItem();

        if (activeItem) {
            setActiveItem(activeItem);
        }
    }, [router.asPath]);

    if (!showMobileNav) {
        return null;
    }

    const shownShowtimeNav = MOBILE_NAV_ITEMS.filter(
        (item) => item.key !== MobileNavItemKey.SHOWTIME || showShowtimeNav,
    );

    return (
        <>
            {withSpace && <Space h={60} />}
            <Portal>
                <Paper className={cx.root} radius={0} hiddenFrom="sm" data-hidden={isMobileOpen} data-variant={variant}>
                    <SimpleGrid cols={shownShowtimeNav.length} spacing={0}>
                        {shownShowtimeNav.map((item) => {
                            const { key, iconDark, icon, iconActive, label, badge, href, onClick } = item;
                            return (
                                <MobileNavItem
                                    key={key}
                                    icon={variant === 'dark' && iconDark ? iconDark : icon}
                                    iconActive={iconActive}
                                    label={label}
                                    badge={badge}
                                    href={href}
                                    isActive={activeItem === key}
                                    onClick={() => {
                                        onClick?.();
                                        handleItemClick(key);
                                    }}
                                />
                            );
                        })}
                    </SimpleGrid>
                </Paper>
            </Portal>
        </>
    );
};

const MobileNavItem = ({
    icon,
    iconActive,
    label,
    badge,
    isActive,
    href,
    onClick,
}: {
    icon: React.ReactNode;
    iconActive: React.ReactNode;
    label: string;
    badge?: React.ReactNode;
    isActive?: boolean;
    href?: string;
    onClick?: () => void;
}) => {
    const content = (
        <>
            <Box className={cx.iconWrapper}>
                {isActive ? iconActive : icon}
                {badge}
            </Box>

            {label}
        </>
    );

    if (href) {
        return (
            <UnstyledButton className={cx.item} data-active={isActive} component={Link} href={href} onClick={onClick}>
                {content}
            </UnstyledButton>
        );
    }

    return (
        <UnstyledButton className={cx.item} data-active={isActive} onClick={onClick}>
            {content}
        </UnstyledButton>
    );
};

export { MobileNav };
