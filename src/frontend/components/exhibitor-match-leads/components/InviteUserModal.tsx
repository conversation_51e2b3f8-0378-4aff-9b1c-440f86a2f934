import { But<PERSON>, <PERSON><PERSON>, Stack } from '@mantine/core';

import { CompanyProfile, ExhibitorMatchLead } from 'models';

import { useAction } from 'hooks/use-action';
import { useRTE } from 'hooks/use-rte';

import { ExhibitorMatchService } from 'services/ExhibitorMatchService';

const InviteUserModal = ({
    lead,
    company,
    handleClose,
}: {
    lead: ExhibitorMatchLead;
    company: CompanyProfile;
    handleClose: () => void;
}) => {
    const { editor, RTEField } = useRTE(null, 'Write your message here');

    if (!editor) return null;

    const [handleInviteUser, sending] = useAction(async () => {
        if (!editor) return;
        if (!lead.email) return;

        try {
            await ExhibitorMatchService.inviteUser({
                email: lead.email,
                companyId: company.id,
                exhibitorMatchLeadId: lead.id,
                message: editor.getJSON(),
            });
        } catch (e) {
            console.error('Failed to invite user', e);
        }

        handleClose();
    });

    return (
        <Modal opened onClose={handleClose} title={`Invite user to the platform (${lead.email?.split('@')[0]}@...)`}>
            <Stack>
                <RTEField editor={editor} />
                <Button onClick={handleInviteUser} loading={sending} disabled={editor?.isEmpty}>
                    Send invitation
                </Button>
            </Stack>
        </Modal>
    );
};

export { InviteUserModal };
