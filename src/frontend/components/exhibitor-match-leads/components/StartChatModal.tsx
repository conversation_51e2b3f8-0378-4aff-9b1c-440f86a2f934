import { useRouter } from 'next/router';

import { <PERSON><PERSON>, Modal, Stack } from '@mantine/core';

import { CompanyProfile } from 'models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { RouterService } from 'services/RouterService';
import { IntercomService } from 'services/IntercomService';

import { useAction } from 'hooks/use-action';
import { useRTE } from 'hooks/use-rte';
import { useUser } from 'hooks/use-user';
import { useIsUserPartOfCompany } from 'components/intercom/hooks/use-is-user-part-of-company';

const StartChatModal = ({
    userId,
    company,
    handleClose,
}: {
    userId: string;
    company: CompanyProfile;
    handleClose: () => void;
}) => {
    const { asPath } = useRouter();

    const { user } = useUser(userId);

    const { editor, RTEField } = useRTE(null, 'Write your first message here');

    const userPartOfCompany = useIsUserPartOfCompany(company);

    const [handleCreateChannel, sending] = useAction(async () => {
        if (!editor) return;
        if (!userId) return;

        const channel = await IntercomService.createChannel({
            type: 'company',
            companyId: company.id,
            content: editor?.getJSON(),
            files: [],
            userPartOfCompany,
            userId,
        });

        handleClose();

        IntercomService.open(channel.id);

        const companyUrl = CompanyProfileHelpers.urls.view(company.slug);

        if (!asPath.includes(companyUrl)) {
            RouterService.push(companyUrl).then();
        }
    });

    if (!editor) return null;

    return (
        <Modal opened onClose={handleClose} title={`Start chat with ${user?.name}`}>
            <Stack>
                <RTEField editor={editor} />
                <Button onClick={handleCreateChannel} loading={sending} disabled={editor?.isEmpty}>
                    Send first message
                </Button>
            </Stack>
        </Modal>
    );
};

export { StartChatModal };
