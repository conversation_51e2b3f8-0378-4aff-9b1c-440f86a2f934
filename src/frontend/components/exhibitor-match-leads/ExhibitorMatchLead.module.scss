.requirements {
    position: relative;


    :global(.mantine-Spoiler-content) {
        font-size: var(--mantine-font-size-sm);
    }

    [data-disabled='true'] {
        :global(.mantine-Spoiler-control) {
            display: none;
        }

        &[data-has-spoiler="true"] {
            margin-bottom: 0;
    
            &::after {
                content: '';
    
                position: absolute;
                top:0;
                bottom: 0;
    
                width: 100%;
    
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
            }
        }
    }

   
}
