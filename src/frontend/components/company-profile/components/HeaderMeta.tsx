import { FC } from 'react';

import { Button, Group } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { BsPencil, BsTagsFill, BsWrenchAdjustable } from 'react-icons/bs';

import { CompanyProfile, PermissionCompany } from 'models';

import { useShowCompanyInfo } from 'components/company-profile/hooks/use-show-company-info';
import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { IconWithText } from 'components/IconWithText';

import { Address } from 'components/company-profile/components/Address';
import { ServicesAndApplicationModal } from 'components/company-profile/components/ServicesAndApplicationModal';

const HeaderMeta: FC<{ company: CompanyProfile }> = ({ company }) => {
    const { shownServices } = useShowCompanyInfo(company);
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const applications = company.applicationTags ?? [];

    const locationToDisplay =
        company.locations.find((location) => location.isHeadquarter) ||
        company.locations.find((location) => location.address?.city);

    const [servicesOpened, servicesHandlers] = useDisclosure();

    return (
        <>
            {(locationToDisplay || company.website) && (
                <Group gap="md">{locationToDisplay && <Address location={locationToDisplay} />}</Group>
            )}

            {(!!shownServices.length || !!applications.length || canEdit) && (
                <Group gap="8px var(--mantine-spacing-md)" align="center">
                    {shownServices.length && (
                        <IconWithText text={shownServices.join(', ')} icon={<BsWrenchAdjustable />} />
                    )}
                    {applications.length && (
                        <IconWithText text={applications.join(', ')} icon={<BsTagsFill />} wordBreak="normal" />
                    )}

                    {canEdit && (
                        <Button
                            onClick={servicesHandlers.open}
                            variant="transparent"
                            size="compact-xs"
                            leftSection={<BsPencil />}
                            p={0}
                        >
                            Edit services & applications
                        </Button>
                    )}

                    <ServicesAndApplicationModal
                        company={company}
                        onClose={servicesHandlers.close}
                        opened={servicesOpened}
                    />
                </Group>
            )}
        </>
    );
};

export { HeaderMeta };
