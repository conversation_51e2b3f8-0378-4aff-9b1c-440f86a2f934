import { Component } from 'models';
import { createContext, useContext } from 'react';

export enum GenerateDatasheetState {
    INITIAL = 'initial',
    LOADING = 'loading',
    ERROR = 'error',
    SUCCESS = 'success',
    CANCELING = 'canceling',
}

export const GenerateDatasheetContext = createContext<{
    state: GenerateDatasheetState;
    showOverlay: boolean;
    overwrite: boolean;
    setState: (state: GenerateDatasheetState) => void;
    setShowOverlay: (show: boolean) => void;
    setOverwrite: (overwrite: boolean) => void;
    generateDatasheet: () => void;
    cancelGenerateDatasheet: () => void;
    aiResult: Partial<Component>[] | null;
    chooseModalOpened: boolean;
    showChooseModal: (opened: boolean) => void;
    fillInDatasheet: (data: Partial<Component>) => void;
    selectedFile: string | null;
    setSelectedFile: (file: string | null) => void;
}>({
    state: GenerateDatasheetState.INITIAL,
    showOverlay: true,
    overwrite: true,
    setState: () => {},
    setShowOverlay: () => {},
    setOverwrite: () => {},
    generateDatasheet: () => {},
    cancelGenerateDatasheet: () => {},
    aiResult: null,
    chooseModalOpened: false,
    showChooseModal: () => {},
    fillInDatasheet: () => {},
    selectedFile: null,
    setSelectedFile: () => {},
});

export const useGenerateDatasheet = () => useContext(GenerateDatasheetContext);
