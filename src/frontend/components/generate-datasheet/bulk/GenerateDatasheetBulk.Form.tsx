import React from 'react';

import { SimpleGrid, Stack, Title } from '@mantine/core';

import { NEW_COMPONENT_TYPES } from 'components/component-signup/steps/Step1';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import { TextField } from 'components/forms/fields/TextField';
import { ManufacturerField } from 'components/forms/fields/ManufacturerField';
import { ComponentTypeField } from 'components/forms/fields/ComponentTypeField';
import { DropzoneButtonField } from 'components/forms/fields/DropzoneButtonField';
import {
    GenerateDatasheetBulkValues,
    GenerateDatasheetBulkSchema,
} from 'components/generate-datasheet/bulk/GenerateDatasheetBulk';
import { FormStatus } from 'components/forms/FormStatus';

const GenerateDatasheetBulkForm = ({
    onSubmit,
    disabled,
}: {
    onSubmit: (values: GenerateDatasheetBulkValues) => void;
    disabled?: boolean;
}) => {
    return (
        <Form<GenerateDatasheetBulkValues> onSubmit={onSubmit} zodSchema={GenerateDatasheetBulkSchema}>
            <Stack>
                <Title order={2}>Select the product category</Title>

                <ComponentTypeField
                    required
                    nbCols={12}
                    disabled={disabled}
                    name="type"
                    label="Component type"
                    componentTypes={NEW_COMPONENT_TYPES}
                />

                <SimpleGrid cols={2} spacing={8}>
                    <TextField required name="productSeries" label="Product series" disabled={disabled} />
                    <ManufacturerField
                        required
                        name="manufacturer"
                        label="Manufacturer"
                        inputProps={{
                            disabled,
                        }}
                    />
                </SimpleGrid>

                <DropzoneButtonField
                    required
                    name="file"
                    metadata={{ group: 'component:files' }}
                    label="Upload datasheet"
                    onUploadComplete={() => {}}
                    inputWrapperProps={{
                        label: 'Datasheet',
                    }}
                    disabled={disabled}
                />

                <FormStatus />

                <FormSubmit variant="gradient" style={{ alignSelf: 'start' }}>
                    Generate Datasheets
                </FormSubmit>
            </Stack>
        </Form>
    );
};

export { GenerateDatasheetBulkForm };
