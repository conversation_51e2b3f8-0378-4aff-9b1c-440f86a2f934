import { useEffect, useState } from 'react';

import { Card, Stack } from '@mantine/core';

import { Component } from 'models';

import {
    Communication,
    Electrical,
    Environmental,
    General,
    Mechanical,
    Performance,
    Standards,
} from 'components/component-datasheet/components/sections';

import { useGenerateDatasheetBulk } from 'components/generate-datasheet/bulk/GenerateDatasheetBulk.Context';

import { Form } from 'components/forms/Form';
import { AutoSave } from 'components/forms/AutoSave';
import { HorizontalTabs } from 'components/horizontal-tabs';

import { DatasheetMode, useDatasheetModeState } from 'components/datasheet';
import { DatasheetMantineProvider } from 'components/datasheet/DatasheetMantineProvider';
import { CurrentComponentContext } from 'components/component-datasheet/hooks/use-component-context';

const GenerateDatasheetBulkSpecifications = () => {
    const { createComponents } = useGenerateDatasheetBulk();

    const [, setMode] = useDatasheetModeState();

    useEffect(() => {
        setMode(DatasheetMode.EDIT);
    }, []);

    return (
        <DatasheetMantineProvider>
            <HorizontalTabs
                isSticky
                keepMounted
                tabs={createComponents.map(({ name, productIdentifier }, index) => ({
                    value: `product-${index + 1}`,
                    label: name ?? productIdentifier ?? `Product ${index + 1}`,
                    content: <GenerateDatasheetBulkSpecification index={index} />,
                }))}
            />
        </DatasheetMantineProvider>
    );
};

const GenerateDatasheetBulkSpecification = ({ index }: { index: number }) => {
    const { createComponents, setCreateComponent } = useGenerateDatasheetBulk();

    const [currentComponent, setCurrentComponent] = useState<Component>(createComponents[index]);

    return (
        <CurrentComponentContext.Provider
            value={{
                component: currentComponent,
                setComponent: setCurrentComponent,
            }}
        >
            <Card>
                <Stack gap="xl">
                    <Form<Component>
                        defaultValues={currentComponent}
                        onSubmit={(values) => {
                            setCreateComponent(values, index);
                        }}
                    >
                        <AutoSave />
                        <Stack gap="xl">
                            <General />
                            <Electrical />
                            <Performance />
                            <Communication />
                            <Standards />
                            <Mechanical />
                            <Environmental />
                        </Stack>
                    </Form>
                </Stack>
            </Card>
        </CurrentComponentContext.Provider>
    );
};

export { GenerateDatasheetBulkSpecifications };
