import { createContext, useContext } from 'react';

import { Component } from 'models';

export const GenerateDatasheetBulkContext = createContext<{
    createComponents: Component[];
    setCreateComponent: (components: Component, index: number) => void;
}>({
    createComponents: [],
    setCreateComponent: () => {},
});

export const useGenerateDatasheetBulk = () => useContext(GenerateDatasheetBulkContext);
