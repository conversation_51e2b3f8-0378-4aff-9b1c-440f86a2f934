import { construct, get, isEqual } from 'radash';
import { useEffect, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import { useWindowEvent } from '@mantine/hooks';
import { ActionIcon, Divider, Grid, Stack, Text } from '@mantine/core';

import { Component, getComponentDefinition } from 'models';

import { AIService } from 'services/AIService';
import { AIServiceUtils } from 'helpers/AIServiceUtils';
import { ComponentService } from 'services/ComponentService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { useDatasheetElectrical } from 'components/component-datasheet/hooks/use-datasheet-electrical';
import { useDatasheetPorts } from 'components/component-datasheet/hooks/use-datasheet-ports';
import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { getId } from 'helpers/getId';
import { valueHasNonNullNonUnitValue } from 'helpers/not-null-helpers';

import { Datasheet, DatasheetMode } from 'components/datasheet';

import { GenerateDatasheetFiles } from './GenerateDatasheet.Files';
import { GenerateDatasheetOverlay } from './GenerateDatasheet.Overlay';
import { GenerateDatasheetSpecifiactions } from './GenerateDatasheet.Specifications';
import { GenerateDatasheetChooseModal } from 'components/generate-datasheet/GenerateDatasheet.ChooseModal';
import { GenerateDatasheetContext, GenerateDatasheetState, useGenerateDatasheet } from './GenerateDatasheet.Context';

const GenerateDatasheet = () => {
    const { showOverlay } = useGenerateDatasheet();

    return (
        <Datasheet initialMode={DatasheetMode.CREATE}>
            <Grid gutter="xl">
                <Grid.Col span={{ base: 12, sm: 4, lg: 3 }}>
                    <Stack align="center" ta="center" gap={8} mb="lg">
                        <Divider
                            w="100%"
                            label={
                                <ActionIcon radius={99} variant="gradient" size="lg">
                                    1
                                </ActionIcon>
                            }
                        />
                        <Text c="dimmed" fz="sm" fw={500} h={40}>
                            Upload a datasheet file
                        </Text>
                    </Stack>

                    <WrappedGenerateDatasheet.Files />
                </Grid.Col>
                <Grid.Col span={{ base: 12, sm: 8, lg: 9 }}>
                    <Stack align="center" ta="center" gap={8} mb="lg">
                        <Divider
                            w="100%"
                            label={
                                <ActionIcon radius={99} size="lg" variant={showOverlay ? 'light' : 'gradient'}>
                                    2
                                </ActionIcon>
                            }
                        />
                        <Text c="dimmed" fz="sm" fw={500} h={40}>
                            Generate the datasheet automatically OR fill in the details manually
                        </Text>
                    </Stack>

                    <WrappedGenerateDatasheet.Specifications />
                </Grid.Col>
            </Grid>
        </Datasheet>
    );
};

const WrappedGenerateDatasheet = () => {
    const { setValue } = useFormContext();
    const files = useWatch({ name: 'files' });

    const [selectedFile, setSelectedFile] = useState<string | null>(getId(files?.[0]?.file) ?? null);

    const electrical = useDatasheetElectrical();
    const { addPort } = useDatasheetPorts();

    const { component, setComponent } = useOptionalComponentContext();

    const [overwrite, setOverwrite] = useState(true);
    const [showOverlay, setShowOverlay] = useState(true);
    const [state, setState] = useState<GenerateDatasheetState>(GenerateDatasheetState.INITIAL);

    const [aiResult, setAIResult] = useState<Partial<Component>[] | null>(null);
    const [chooseModalOpened, showChooseModal] = useState(false);

    const [populateRequestController, setPopulateRequestController] = useState<AbortController | null>(null);

    useEffect(() => {
        setSelectedFile(getId(files[0]?.file) ?? null);
    }, [files]);

    // instantly update the component with the new files
    useEffect(() => {
        if (!component) return;
        if (isEqual(component.files, files)) return;

        ComponentService.update(component.id, { files });
        setComponent({ ...component, files });
    }, [component, files]);

    // show confirmation dialog when user tries to leave the page while AI is processing
    useWindowEvent('beforeunload', (event) => {
        if (state === GenerateDatasheetState.LOADING) {
            event.preventDefault();
        }
    });

    const generateDatasheet = async () => {
        if (!component) {
            setState(GenerateDatasheetState.ERROR);
            return;
        }

        if (!selectedFile) {
            return;
        }

        try {
            const abortController = new AbortController();

            setShowOverlay(true);
            setState(GenerateDatasheetState.LOADING);
            setPopulateRequestController(abortController);

            InternalTrackingService.track('product.aiDatasheet.populate', {
                componentId: component.id,
                componentType: component.type,
            });

            const result = await AIService.populateDatasheet({
                componentId: component.id,
                componentType: component.type,
                fileId: selectedFile,
                controller: abortController,
            });

            if (!result?.length) {
                setState(GenerateDatasheetState.ERROR);
                return;
            }

            setAIResult(result);
            setShowOverlay(false);
            setState(GenerateDatasheetState.SUCCESS);
            setPopulateRequestController(null);

            const fillInComponent =
                result.length === 1
                    ? result[0]
                    : result.find((data) => data.productIdentifier === component.productIdentifier);

            if (fillInComponent) {
                fillInDatasheet(fillInComponent);
                return;
            }

            showChooseModal(true);
        } catch (error: any) {
            if (error.name !== 'AbortError') {
                setState(GenerateDatasheetState.ERROR);
                console.error(error);
            }
        } finally {
            setPopulateRequestController(null);
        }
    };

    const fillInDatasheet = (data: Partial<Component>) => {
        if (!component) return;

        const cleanResult = AIServiceUtils.getCleanResult(component.type, data);

        // add extra ports
        const componentDefinition = getComponentDefinition(component.type);
        const nbPortsResult = get(construct(cleanResult), 'electrical.ports', []).length;
        const nbPorts = electrical.ports.length;
        const nbExtraPorts = nbPortsResult - nbPorts;

        if (nbExtraPorts > 0) {
            for (let i = 0; i < nbExtraPorts; i++) {
                if (nbPorts + i < componentDefinition.ports.max) {
                    addPort();
                }
            }
        }

        Object.entries(cleanResult).forEach(([key, value]) => {
            if (value === null || value === '') {
                return;
            }

            const originalValue = get(component, key);
            const hadValue =
                originalValue !== null && originalValue !== '' && valueHasNonNullNonUnitValue(originalValue);

            if (hadValue && !overwrite) {
                return;
            }

            setValue(key, value, { shouldDirty: true, shouldTouch: false });
        });
    };

    const cancelGenerateDatasheet = async () => {
        try {
            setState(GenerateDatasheetState.CANCELING);

            InternalTrackingService.track('product.aiDatasheet.cancel');
            populateRequestController?.abort();

            setShowOverlay(false);
            setState(GenerateDatasheetState.INITIAL);
        } catch (error) {
            setState(GenerateDatasheetState.ERROR);
            console.error(error);
        } finally {
            setPopulateRequestController(null);
        }
    };

    return (
        <GenerateDatasheetContext.Provider
            value={{
                state,
                aiResult,
                overwrite,
                showOverlay,
                chooseModalOpened,
                selectedFile,
                setState,
                setOverwrite,
                setShowOverlay,
                fillInDatasheet,
                generateDatasheet,
                cancelGenerateDatasheet,
                showChooseModal,
                setSelectedFile,
            }}
        >
            <GenerateDatasheet />
            <WrappedGenerateDatasheet.ChooseModal />
        </GenerateDatasheetContext.Provider>
    );
};

WrappedGenerateDatasheet.Files = GenerateDatasheetFiles;
WrappedGenerateDatasheet.Specifications = GenerateDatasheetSpecifiactions;
WrappedGenerateDatasheet.Overlay = GenerateDatasheetOverlay;
WrappedGenerateDatasheet.ChooseModal = GenerateDatasheetChooseModal;

export { WrappedGenerateDatasheet as GenerateDatasheet };
