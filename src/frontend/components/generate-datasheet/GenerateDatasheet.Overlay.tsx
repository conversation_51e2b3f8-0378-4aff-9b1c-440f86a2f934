import { Button, Loader, Overlay, Stack, Text, Title } from '@mantine/core';

import { GenerateDatasheetState, useGenerateDatasheet } from './GenerateDatasheet.Context';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

const GenerateDatasheetOverlay = () => {
    const { component } = useOptionalComponentContext();
    const { state, setShowOverlay, cancelGenerateDatasheet } = useGenerateDatasheet();

    const isLoading = state === GenerateDatasheetState.LOADING;
    const isCanceling = state === GenerateDatasheetState.CANCELING;

    const handleManual = () => {
        InternalTrackingService.track('product.aiDatasheet.manual', {
            componentId: component?.id,
            componentType: component?.type,
        });
        setShowOverlay(false);
    };

    return (
        <Overlay backgroundOpacity={0.1} blur={2} zIndex={1}>
            <Stack w="100%" h="100%" mah="60vh" align="center" justify="center" gap="xl" p="xl">
                {isLoading || isCanceling ? (
                    <>
                        <Stack gap={8} align="center">
                            <Title order={2}>Processing your files, this might take a few minutes</Title>
                            <Text fz="sm" fw={500} opacity={0.8}>
                                You can leave this page running in the background and come back later
                            </Text>
                        </Stack>
                        <Loader type="bars" size="xs" />
                        <Button variant="white" onClick={cancelGenerateDatasheet} loading={isCanceling}>
                            Cancel processing and fill in manually
                        </Button>
                    </>
                ) : (
                    <Button variant="white" onClick={handleManual}>
                        Fill in the details manually
                    </Button>
                )}
            </Stack>
        </Overlay>
    );
};

export { GenerateDatasheetOverlay };
