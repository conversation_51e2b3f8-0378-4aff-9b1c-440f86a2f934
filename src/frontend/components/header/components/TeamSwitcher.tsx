import { FC, useEffect, useState } from 'react';

import { useRouter } from 'next/router';

import { Badge, Group, Loader } from '@mantine/core';

import { TbUsers } from 'react-icons/tb';
import { IoAddSharp } from 'react-icons/io5';
import { Typewriter } from 'components/typewriter/Typewriter';

import { PermissionProject, TeamInfo } from 'models';

import { useTeams } from 'hooks/use-teams';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { UserService } from 'services/UserService';

import { DropDown } from 'components/dropdown/DropDown';
import { TeamService } from 'services/TeamService';
import { TextHelpers } from 'helpers/TextHelpers';

const TeamSwitcher: FC = () => {
    const user = useCurrentUser();
    const { teams } = useTeams();
    const currentTeam = useCurrentTeam();

    const [render, setRender] = useState(false);
    const [animate, setAnimate] = useState(false);

    useEffect(() => {
        setRender(true);

        const switched = window.sessionStorage.getItem('switched-team');

        if (switched !== currentTeam?.id) {
            setAnimate(true);
        }

        window.sessionStorage.setItem('switched-team', currentTeam?.id || 'non-existing');
    }, []);

    if (!user) return null;

    const availableTeams = teams?.filter((team) => team.permissions.includes(PermissionProject.ALL)) ?? [];

    const titleComponent = (
        <Typewriter enabled={animate}>
            {currentTeam ? TextHelpers.getTextWithEllipsis(currentTeam.name, 12) : '[no team]'}
        </Typewriter>
    );

    const openCreateTeamModal = () => {
        TeamService.openCreateTeam();
    };

    return render ? (
        <DropDown icon={<TbUsers size={14} />} title={titleComponent}>
            {availableTeams.length > 0 && (
                <>
                    {availableTeams.map((team) => (
                        <TeamRow key={team.id} team={team} />
                    ))}
                </>
            )}
            <DropDown.Divider />
            <DropDown.Option leftSection={<IoAddSharp size={16} />} onClick={openCreateTeamModal}>
                Create new team
            </DropDown.Option>
        </DropDown>
    ) : null;
};

const TeamRow: FC<{
    team: TeamInfo;
}> = ({ team }) => {
    const [isSwitching, setIsSwitching] = useState(false);
    const router = useRouter();

    const currentTeam = useCurrentTeam();
    const active = team.id === currentTeam?.id;

    const projectId = router.query.projectId as string | undefined;

    const switchTeam = async () => {
        setIsSwitching(true);
        await UserService.switchTeam(team.id);

        if (projectId) {
            router.push('/projects').then();
            return;
        }

        router.reload();
    };

    return (
        <DropDown.Option leftSection={isSwitching ? <Loader size={12} /> : undefined} onClick={switchTeam}>
            <Group justify="space-between">
                {team.name}
                {active && (
                    <Badge color="black" variant="filled" size="xs" radius="xs">
                        current
                    </Badge>
                )}
            </Group>
        </DropDown.Option>
    );
};

export { TeamSwitcher };
