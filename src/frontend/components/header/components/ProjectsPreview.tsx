import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { Menu } from '@mantine/core';
import { TbLayoutGrid } from 'react-icons/tb';

import { Project } from 'models';

import { useProjects } from 'hooks/use-projects';
import { ProjectHelpers } from 'helpers/ProjectHelpers';

const ProjectsPreview = () => {
    const { projects } = useProjects({ depth: 1, limit: 5 });

    return (
        <React.Fragment>
            <Menu.Item component={Link} href="/projects" leftSection={<TbLayoutGrid size={16} strokeWidth={1.5} />}>
                View all projects
            </Menu.Item>

            <Menu.Divider />

            <LatestProjects projects={projects} />
        </React.Fragment>
    );
};

const LatestProjects: FC<{ projects: Project[] }> = ({ projects }) => {
    return (
        <React.Fragment>
            <Menu.Label>Latest projects</Menu.Label>
            {projects.map((project) => (
                <LatestProjectsItem key={project.id} project={project} />
            ))}
        </React.Fragment>
    );
};

const LatestProjectsItem: FC<{ project: Project }> = ({ project }) => (
    <Menu.Item component={Link} href={ProjectHelpers.urls.editor(project.id)}>
        {project.name}
    </Menu.Item>
);

export { ProjectsPreview };
