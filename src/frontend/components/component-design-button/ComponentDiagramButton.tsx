import { <PERSON>, MouseEventHandler } from 'react';

import { But<PERSON> } from '@mantine/core';
import { IoLinkSharp } from 'react-icons/io5';
import { IoLink, IoUnlink } from 'react-icons/io5';

import { Cable, Component, FeatureKey } from 'models';

import { useFeatureAccess } from 'hooks/use-feature-access';
import { useDiagramProductSearch } from '@diagram/hooks';
import { useEditConnectionContext } from '@diagram/components/diagram-sidebar/sidebars/edit-connection/context';

import { ConnectionService } from '@diagram/services';
import { ComponentInstanceService } from '@diagram/services';
import { SidebarService } from '@diagram/services/SidebarService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { DiagramSyncService } from '@diagram/services/DiagramSyncService';
import { ProductSearchService } from '@diagram/services/ProductSearchService';

import { SidebarTab } from '@diagram/state/sidebar';
import { SubscriptionOnboardingMessage } from 'components/subscriptions/SubscriptionOnboardingMessage';

export const ComponentDiagramButton: FC<{ component: Component }> = ({ component }) => {
    const canLinkProduct = useFeatureAccess(FeatureKey.LINK_PRODUCT);

    if (!canLinkProduct) {
        return (
            <SubscriptionOnboardingMessage>
                Want to add link products from the product catalog?
            </SubscriptionOnboardingMessage>
        );
    }

    if (component.type === 'cable') {
        return <ComponentDiagramButtonConnection component={component} />;
    }

    return <ComponentDiagramButtonComponentInstance component={component} />;
};

const ComponentDiagramButtonComponentInstance = ({ component }: { component: Component }) => {
    const { componentInstance, linkedComponentId } = useDiagramProductSearch();

    if (!componentInstance) return null;

    const addToDesign: MouseEventHandler<HTMLButtonElement> = async (e) => {
        e.stopPropagation();

        try {
            ComponentInstanceService.linkProduct({
                componentInstance,
                productCatalogComponent: component,
            });

            DiagramSyncService.save();
            ProductSearchService.updateLinkedComponentId(component.id);
        } catch (error: any) {
            LocalNotificationService.showError({ message: error.message ?? 'Error linking product' });
        }
    };

    const linkedToComponent = linkedComponentId === component.id;

    return (
        <Button
            leftSection={<IoLinkSharp />}
            onClick={addToDesign}
            disabled={linkedToComponent}
            size="compact-xs"
            variant="outline"
            style={{ alignSelf: 'flex-start' }}
        >
            {linkedToComponent ? 'Linked' : 'Link'} to {componentInstance.designator}
        </Button>
    );
};

const ComponentDiagramButtonConnection = ({ component }: { component: Component }) => {
    const { connection } = useEditConnectionContext();

    const linkCable = async (cable: Cable) => {
        try {
            ConnectionService.linkCable({ connection, cable });

            DiagramSyncService.save();
            SidebarService.setActiveTab(SidebarTab.SPECIFICATIONS);
        } catch (error: any) {
            LocalNotificationService.showError({ message: error.message ?? 'Error linking cable' });
        }
    };

    if (!connection) {
        return null;
    }

    return (
        <>
            {connection.cableId === component.id ? (
                <Button
                    size="compact-xs"
                    variant="outline"
                    onClick={() => {
                        ConnectionService.unlinkCable(connection.id);
                        DiagramSyncService.save();
                    }}
                    leftSection={<IoUnlink />}
                >
                    Unlink this cable
                </Button>
            ) : (
                <Button
                    size="compact-xs"
                    variant="outline"
                    onClick={() => linkCable(component as Cable)}
                    leftSection={<IoLink />}
                >
                    Link this cable
                </Button>
            )}
        </>
    );
};
