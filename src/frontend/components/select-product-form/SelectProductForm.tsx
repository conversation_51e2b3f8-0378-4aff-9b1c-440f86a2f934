import { useState } from 'react';

import { Group } from '@mantine/core';

import { useProducts } from 'hooks/use-products';

import { ManufacturerInput } from 'components/inputs/ManufacturerInput';
import { ProductsSelect } from 'components/forms/fields/compatible-with/components/ProductsSelect';

const SelectProductForm = ({ onSelectProduct }: { onSelectProduct: (newId: string) => void }) => {
    const [manufacturer, setManufacturer] = useState<string>('');
    const { products, isLoading } = useProducts({ manufacturer }, Boolean(manufacturer));

    return (
        <Group gap={4}>
            <ManufacturerInput
                disableAddManufacturers
                value={manufacturer}
                style={{ flexGrow: 1 }}
                onOptionSubmit={async (value) => {
                    if (value !== '$new') {
                        setManufacturer(value);
                    }
                }}
            />
            <ProductsSelect
                onOptionSubmit={onSelectProduct}
                components={products}
                manufacturer={manufacturer}
                isLoading={isLoading}
            />
        </Group>
    );
};

export { SelectProductForm };
