import { useSnapshot } from 'hooks/use-safe-snapshot';

import { componentSearchBar } from 'components/component-overview/state/component-search-bar';

const useComponentSearchBar = () => {
    const componentSearchBarSnapshot = useSnapshot(componentSearchBar);

    return {
        ...componentSearchBarSnapshot,
        setQuery: (query: string) => {
            componentSearchBar.query = query;
        },
    };
};

export { useComponentSearchBar };
