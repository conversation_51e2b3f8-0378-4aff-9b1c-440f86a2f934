import React, { FC, useState } from 'react';

import { Badge, Divider, Group, Stack, Text, UnstyledButton } from '@mantine/core';
import { IoChevronDown } from 'react-icons/io5';

import {
    Charger,
    ChargerConnector,
    Component,
    energyConverter,
    VoltageType,
    powerConverter,
    voltageConverter,
    currentConverter,
} from 'models';

import { VoltageTypeSwitch } from 'components/buttons/VoltageTypeSwitch';
import { FormatHelpers } from 'helpers/formatters';
import { getComponentMergedChargingPorts, getComponentMergedPorts } from './helpers/get-component-merged-ports';

const NUMBER_SHOWN_PORTS = 3;

const ComponentOverviewHitDatasheet: FC<{ component: Component }> = ({ component }) => {
    const electrical = component.electrical ?? {};

    const ports = electrical.ports || [];
    const chargingPorts = 'chargingPorts' in electrical ? electrical.chargingPorts : [];

    const energyCapacity = 'energyCapacity' in electrical ? electrical.energyCapacity : null;

    const mergedPorts = getComponentMergedPorts(ports) || [];
    const mergedChargingPorts = getComponentMergedChargingPorts(chargingPorts) || [];

    const [showExpand, setShowExpand] = useState(mergedPorts.length + mergedChargingPorts.length > NUMBER_SHOWN_PORTS);

    const shownPorts = showExpand ? mergedPorts.slice(0, NUMBER_SHOWN_PORTS) : mergedPorts;
    const shownChargingPorts = showExpand
        ? mergedChargingPorts.slice(0, NUMBER_SHOWN_PORTS - shownPorts.length)
        : mergedChargingPorts;

    if (ports.length === 0 && chargingPorts.length === 0 && !energyCapacity?.value) {
        return null;
    }

    const justOnePort = mergedPorts.length + mergedChargingPorts.length === 1;

    const parallelableCapacity = 'parallelableCapacity' in electrical ? electrical.parallelableCapacity : 1;

    return (
        <Stack gap="xs">
            <Stack gap="sm">
                {energyCapacity?.value && (
                    <Group gap="xs">
                        <Badge variant="outline" color="gray.6" radius="xs" px={6}>
                            Capacity
                        </Badge>
                        {FormatHelpers.formatMeasurement(energyCapacity, energyConverter)}
                        {parallelableCapacity > 1 ? `/unit (max ${parallelableCapacity}x units parallel)` : ''}
                    </Group>
                )}
                {shownPorts.map(({ port, index }) => (
                    <ComponentOverviewHitDatasheetPort
                        port={port}
                        index={index}
                        key={index.toString()}
                        justOne={justOnePort}
                    />
                ))}
                {shownChargingPorts.map(({ port, index }) => (
                    <ChargingPortSpecifications
                        port={port}
                        index={index}
                        key={index.toString()}
                        justOne={justOnePort}
                    />
                ))}
                {'canServeAs' in component && component.canServeAs.length > 0 && (
                    <Stack gap={4}>
                        <ComponentOverviewHitDatasheetTitle title="Can be used as" />
                        <Group gap={4}>
                            {component.canServeAs.map((componentType) => (
                                <Badge
                                    variant="outline"
                                    color="gray.6"
                                    radius="xs"
                                    px={6}
                                    size="xs"
                                    key={componentType}
                                >
                                    {componentType}
                                </Badge>
                            ))}
                        </Group>
                    </Stack>
                )}
            </Stack>
            {showExpand && (
                <Group gap={2} c="dimmed">
                    <UnstyledButton fz="sm" fw={600} onClick={() => setShowExpand(false)}>
                        More specifications
                    </UnstyledButton>
                    <IoChevronDown />
                </Group>
            )}
        </Stack>
    );
};

const ComponentOverviewHitDatasheetPort: FC<{
    port: Component['electrical']['ports'][number];
    index: number | [number, number];
    justOne?: boolean;
}> = ({ port, index, justOne }) => {
    const hasDC = 'DC' in port && port.DC.enabled;
    const hasAC = 'AC' in port && port.AC.enabled;

    if (!hasDC && !hasAC) return null;

    const subtitle: string[] = [];

    if (port.powerFlowDirection) {
        subtitle.push(port.powerFlowDirection);
    }

    if ('purpose' in port && port.purpose) {
        subtitle.push(port.purpose);
    }

    const title = getPortTitle(index, justOne);

    return (
        <Stack gap={'xs'}>
            {title && <ComponentOverviewHitDatasheetTitle title={title} subtitle={subtitle.join(' • ')} />}

            {hasDC && <PortSpecificationLine portValues={port['DC']} voltageType={'DC'} />}
            {hasAC && <PortSpecificationLine portValues={port['AC']} voltageType={'AC'} />}
        </Stack>
    );
};

const ChargingPortSpecifications: FC<{
    port: Charger['electrical']['chargingPorts'][number];
    index: number | [number, number];
    justOne?: boolean;
}> = ({ port, index, justOne }) => {
    if (!port.voltageType) return null;

    const title = getPortTitle(index, justOne, 'Charging ');
    const subtitle = ChargerConnector.options.find((c) => port.connector === c.value)?.label || 'Unknown connector';

    return (
        <Stack gap={'xs'}>
            {title && <ComponentOverviewHitDatasheetTitle title={title} subtitle={subtitle} />}

            <PortSpecificationLine portValues={port} voltageType={port.voltageType} />
        </Stack>
    );
};

const ComponentOverviewHitDatasheetTitle = ({ title, subtitle }: { title: string; subtitle?: string }) => {
    return (
        <Divider
            label={
                <Text
                    inherit
                    fw={700}
                    fz="xs"
                    tt="uppercase"
                    style={{
                        lineHeight: '1rem',
                    }}
                >
                    {title}
                    {subtitle && (
                        <Text span tt="initial" fz="xs" c="gray.5">
                            {' '}
                            ({subtitle})
                        </Text>
                    )}
                </Text>
            }
            labelPosition="left"
        />
    );
};

const PortSpecificationLine: FC<{
    portValues: any;
    voltageType: VoltageType;
}> = ({ portValues, voltageType }) => {
    const voltage = FormatHelpers.formatMinNomMax(portValues.voltage, voltageConverter);
    const power = FormatHelpers.formatMinNomMax(portValues.power, powerConverter);
    const current = FormatHelpers.formatMinNomMax(portValues.current, currentConverter);

    const specifications = [voltage, power, current].filter(Boolean);

    const specificationsMissing = specifications.length === 0;

    return (
        <Group gap={'xs'} wrap="nowrap">
            <VoltageTypeSwitch voltageType={voltageType} voltageTypes={[voltageType]} variant={'outline'} />
            <Text c={specificationsMissing ? 'dimmed' : 'inherit'} fz="sm" lh={1.2}>
                {specificationsMissing ? `${voltageType} specifications missing` : specifications.join(' • ')}
            </Text>
        </Group>
    );
};

const getPortTitle = (index: number | [number, number], justOne?: boolean, prefix = '') => {
    const isPortRange = Array.isArray(index);
    const showTitle = !justOne || isPortRange;

    if (!showTitle) return null;

    return isPortRange ? `${prefix}Ports ${index[0] + 1} … ${index[1] + 1}` : `${prefix}Port ${index + 1}`;
};

export { ComponentOverviewHitDatasheet };
