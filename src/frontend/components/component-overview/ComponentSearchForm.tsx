import React, { FC, useEffect } from 'react';

import { Form } from 'components/forms/Form';

import { AutoSave } from 'components/forms/AutoSave';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { ComponentQuery } from 'models';

const ComponentSearchForm: FC<{
    defaultValues?: Partial<ComponentQuery>;
    children: React.ReactNode;
}> = ({ defaultValues = {}, children }) => {
    const filters = useComponentSearchFilters();

    useEffect(() => {
        SearchService.mergeFilters(defaultValues);
    }, [JSON.stringify(defaultValues)]);

    return (
        <Form<ComponentQuery>
            defaultValues={filters}
            onSubmit={(values) => {
                SearchService.mergeFilters(values);
            }}
        >
            <AutoSave debounce={1000} />
            {children}
        </Form>
    );
};

export { ComponentSearchForm };
