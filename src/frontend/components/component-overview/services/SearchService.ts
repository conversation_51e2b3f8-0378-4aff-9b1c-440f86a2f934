import { debounce, isEqual, pick } from 'radash';

import {
    CompanyProfile,
    Component,
    ComponentQuery,
    componentQuerySchema,
    ComponentSearchData,
    ComponentSearchScore,
    removeUndefined,
} from 'models';

import { publicConfig } from '@public-config';

import { ApiService } from 'services/ApiService';
import { ComponentService } from 'services/ComponentService';

import {
    componentSearchFilters as componentSearchFiltersState,
    DEFAULT_SEARCH_COMPONENT_FILTERS,
} from 'components/component-overview/state/component-search-filters';

import {
    componentSearchData as componentSearchDataState,
    DEFAULT_COMPONENT_SEARCH_DATA,
} from 'components/component-overview/state/component-search-data';

import {
    componentSearchWeights as componentSearchWeightsState,
    DEFAULT_COMPONENT_SEARCH_WEIGHTS,
} from 'components/component-overview/state/component-search-weights';

import { componentSearchBar as componentSearchBarState } from 'components/component-overview/state/component-search-bar';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { LocalStorageService } from 'services/LocalStorageService';
import { UserHelpers } from 'helpers/UserHelpers';
import { profileOverviewSearch } from 'components/profile-overview/state/profile-overview-search';

const SearchService = {
    setFilter<FilterKey extends keyof ComponentQuery>(key: FilterKey, value: ComponentQuery[FilterKey]) {
        componentSearchFiltersState[key] = value;
        const componentQuerySchemaKeys = Object.keys(componentQuerySchema);
        const isKeyInProfileOverviewSearch = componentQuerySchemaKeys.includes(key as string);
        if (isKeyInProfileOverviewSearch) {
            (profileOverviewSearch.filters as any)[key] = value;
        }
    },

    mergeFilters(filters: Partial<ComponentQuery>) {
        Object.entries(filters).forEach(([key, value]) => {
            SearchService.setFilter(key as keyof ComponentQuery, value);
        });
    },

    resetFilters() {
        SearchService.mergeFilters(DEFAULT_SEARCH_COMPONENT_FILTERS);
    },

    getCleanFilters() {
        return {
            ...componentSearchFiltersState,
            event: LocalStorageService.get(UserHelpers.localStorageKey.event) || undefined,
        };
    },

    setSearchData<DataKey extends keyof ComponentSearchData>(key: DataKey, value: ComponentSearchData[DataKey]) {
        componentSearchDataState[key] = value;
    },

    setPage(page: number) {
        componentSearchDataState.page = page;
    },

    resetSearchData() {
        Object.keys(DEFAULT_COMPONENT_SEARCH_DATA).forEach((key) => {
            const dataKey = key as keyof ComponentSearchData;
            const dataValue = DEFAULT_COMPONENT_SEARCH_DATA[dataKey];

            SearchService.setSearchData(dataKey, dataValue);
        });
    },

    resetSearch() {
        SearchService.resetFilters();
        SearchService.resetSearchData();
        SearchService.resetSearchWeights();
    },

    setSearchWeight(weight: ComponentSearchScore, value: number) {
        componentSearchWeightsState[weight] = value;
    },

    resetSearchWeights() {
        for (const score of Object.keys(componentSearchWeightsState)) {
            delete componentSearchWeightsState[score as keyof typeof componentSearchWeightsState];
        }

        for (const [score, value] of Object.entries(DEFAULT_COMPONENT_SEARCH_WEIGHTS)) {
            SearchService.setSearchWeight(score as ComponentSearchScore, value);
        }
    },

    debouncedSearch: debounce({ delay: 500 }, () => {
        SearchService.search().then();
    }),

    search: async () => {
        const start = new Date().getTime();

        // cancel previous search calls
        if (componentSearchDataState.isLoading) {
            ComponentService.controller?.abort();
        }

        const cleanFilters = SearchService.getCleanFilters();
        const isInitialSearch = SearchService.isInitialSearch(cleanFilters);

        if (!isInitialSearch) {
            InternalTrackingService.track('product.search', cleanFilters);
        }

        componentSearchDataState.isLoading = true;

        try {
            let result = null;

            if (isInitialSearch) {
                const featuredProducts = await ComponentService.getFeaturedProducts();

                result = {
                    docs: featuredProducts,
                    totalPages: 1,
                    totalResults: featuredProducts.length,
                };
            } else {
                result = await ComponentService.search(
                    {
                        ...cleanFilters,
                        customWeights: componentSearchWeightsState,
                    },
                    componentSearchDataState.page,
                );
            }

            componentSearchDataState.hasSearched = true;
            componentSearchDataState.docs = result.docs;

            if (!isInitialSearch) {
                componentSearchDataState.totalComponents = result.totalResults;
            }

            componentSearchDataState.totalPages = result.totalPages;
            componentSearchDataState.isLoading = false;

            if (!isInitialSearch) {
                const scores: number[] = [];
                const parts: {
                    [key: string]: number[];
                } = {};

                result.docs.forEach((doc: any) => {
                    scores.push(doc.aggregateScore || 0);

                    Object.entries(doc.scores || {}).forEach(([score, value]) => {
                        parts[score] = parts[score] || [];
                        parts[score].push((value as number) || 0);
                    });
                });

                const totalScore = scores.reduce((a, b) => a + b, 0);
                const minimumScore = Math.min(...scores);
                const maximumScore = Math.max(...scores);
                const averageScore = totalScore / scores.length;

                const averageParts = Object.entries(parts).reduce(
                    (acc, [key, value]) => {
                        acc[key] = value.reduce((a, b) => a + b, 0) / value.length;

                        return acc;
                    },
                    {} as { [key: string]: number },
                );

                const prepareResultsForTracking = (results: any[]) => {
                    return JSON.parse(JSON.stringify(results)).map((result: any) => {
                        return pick(result, [
                            '_id',
                            '__position',
                            'type',
                            'name',
                            'productIdentifier',
                            'productSeries',
                            'aggregateScore',
                            'scores',
                        ]);
                    });
                };

                InternalTrackingService.track('product.search.result', {
                    query: componentSearchBarState.query,
                    filters: cleanFilters,
                    results: prepareResultsForTracking(result.docs),
                    page: componentSearchDataState.page + 1,
                    totalPages: componentSearchDataState.totalPages,
                    totalResults: componentSearchDataState.totalComponents,
                    scores: {
                        total: totalScore,
                        minimum: minimumScore,
                        maximum: maximumScore,
                        average: averageScore,
                        parts: averageParts,
                    },
                    duration: new Date().getTime() - start,
                });
            }
        } catch (error: any) {
            if (error?.name === 'AbortError') {
                return;
            }

            componentSearchDataState.hasSearched = true;
            componentSearchDataState.docs = [];
            componentSearchDataState.error = true;
            componentSearchDataState.isLoading = false;
        }
    },

    setSearchBarQuery(query: string) {
        componentSearchBarState.query = query;
    },

    getSearchBarQuery() {
        return componentSearchBarState.query;
    },

    clearSearchBarQuery() {
        componentSearchBarState.query = '';
    },

    resetHasSearched() {
        componentSearchDataState.hasSearched = false;
    },

    isInitialSearch: (filters: ComponentQuery) => {
        return isEqual(
            JSON.parse(JSON.stringify(removeUndefined(DEFAULT_SEARCH_COMPONENT_FILTERS))),
            JSON.parse(JSON.stringify(removeUndefined(filters))),
        );
    },

    getAutosuggest: async (query: string) => {
        return ApiService.get<{
            companies: CompanyProfile[];
            products: Component[];
        }>(`${publicConfig.urls.api}/search-autosuggest?search=${encodeURIComponent(query)}`);
    },

    scrollToContent: () => {
        const content = document.querySelector('#search-content');

        if (content) {
            const { top } = content.getBoundingClientRect();

            window.scrollTo(0, top + scrollY);
        }
    },
};

export { SearchService };
