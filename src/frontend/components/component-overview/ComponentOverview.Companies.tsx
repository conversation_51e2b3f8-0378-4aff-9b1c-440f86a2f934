import React, { FC } from 'react';

import { CompanyProfile } from 'models';

import { GridSection } from 'components/section/GridSection';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';
import { ImpressionTracker } from 'components/component-overview/components/ImpressionTracker';

const ComponentOverviewCompanies: FC<{
    companies: CompanyProfile[];
}> = ({ companies }) => {
    return (
        <GridSection nbCols={3}>
            {companies.map((company, index) => (
                <ImpressionTracker
                    namespace="company.search"
                    data={{
                        company: company.id,
                        position: index + 1,
                    }}
                    key={company.id}
                >
                    <CompanyProfileTeaser company={company} />
                </ImpressionTracker>
            ))}
        </GridSection>
    );
};

export { ComponentOverviewCompanies };
