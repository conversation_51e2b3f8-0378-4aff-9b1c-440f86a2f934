import React, { FC } from 'react';

import { Badge, Flex, Group, Stack, Text } from '@mantine/core';

import {
    Charger,
    ChargerConnector,
    energyConverter,
    PortControlMethods,
    PortControlMethod,
    Component,
    VoltageType,
    voltageConverter,
    powerConverter,
    currentConverter,
} from 'models';
import { FormatHelpers } from 'helpers/formatters';
import { ComponentOverviewHitDatasheetCable } from 'components/component-overview/ComponentOverviewHitDatasheet.Cable';

const DiagramComponentOverviewHitDatasheet: FC<{ component: Component }> = ({ component }) => {
    if (component.type === 'cable') {
        return <ComponentOverviewHitDatasheetCable cable={component} />;
    }

    const electrical = component.electrical ?? {};
    const energyCapacity = 'energyCapacity' in electrical ? electrical.energyCapacity : null;

    const ports = component.electrical.ports;
    const chargingPorts = 'chargingPorts' in electrical ? electrical.chargingPorts : [];

    if (ports.length === 0 && chargingPorts.length === 0) {
        return null;
    }

    const parallelableCapacity = 'parallelableCapacity' in electrical ? electrical.parallelableCapacity : 1;

    return (
        <Stack gap={8} fz="sm">
            {energyCapacity?.value && (
                <Group gap="xs">
                    <Badge variant="outline" color="gray.6" radius="xs" px={6} size="xs">
                        Capacity
                    </Badge>
                    {FormatHelpers.formatMeasurement(energyCapacity, energyConverter)}
                    {parallelableCapacity > 1 ? `/unit (max ${parallelableCapacity}x units parallel)` : ''}
                </Group>
            )}
            {ports.map((port, index) => (
                <DiagramComponentOverviewHitDatasheetPort port={port} index={index} key={index} />
            ))}
            {chargingPorts.map((port, index) => (
                <ChargingPortSpecifications port={port} index={index} key={index} />
            ))}
            {'canServeAs' in component && component.canServeAs.length > 0 && (
                <Stack gap={4}>
                    <DiagramComponentOverviewHitDatasheetTitle title="Can be used as" />
                    <Group gap={4}>
                        {component.canServeAs.map((componentType) => (
                            <Badge variant="outline" color="gray.6" radius="xs" px={6} size="xs" key={componentType}>
                                {componentType}
                            </Badge>
                        ))}
                    </Group>
                </Stack>
            )}
        </Stack>
    );
};

const DiagramComponentOverviewHitDatasheetPort: FC<{
    port: Component['electrical']['ports'][number];
    index: number;
}> = ({ port, index }) => {
    const hasDC = 'DC' in port && port.DC.enabled;
    const hasAC = 'AC' in port && port.AC.enabled;

    if (!hasDC && !hasAC) return null;

    const subtitle: string[] = [];

    if (port.powerFlowDirection) {
        subtitle.push(port.powerFlowDirection);
    }

    if ('purpose' in port && port.purpose) {
        subtitle.push(port.purpose);
    }

    return (
        <Stack gap={4}>
            <DiagramComponentOverviewHitDatasheetTitle title={`Port ${index + 1}`} subtitle={subtitle.join(' • ')} />

            <Stack gap={4}>
                {hasDC && <DiagramPortSpecificationLine portValues={port['DC']} voltageType={'DC'} />}
                {hasAC && <DiagramPortSpecificationLine portValues={port['AC']} voltageType={'AC'} />}
            </Stack>
        </Stack>
    );
};

const DiagramComponentOverviewHitDatasheetTitle = ({ title, subtitle }: { title: string; subtitle?: string }) => {
    return (
        <Text
            inherit
            c="gray.5"
            style={{
                textTransform: 'uppercase',
                fontWeight: 600,
                fontSize: 10,
            }}
        >
            {title}
            {subtitle && (
                <Text span tt="initial" fz="xs" c="gray.5">
                    {' '}
                    ({subtitle})
                </Text>
            )}
        </Text>
    );
};

const DiagramPortSpecificationLine: FC<{
    portValues: any;
    voltageType: VoltageType;
}> = ({ portValues, voltageType }) => {
    const voltage = FormatHelpers.formatMinNomMax(portValues.voltage, voltageConverter);
    const power = FormatHelpers.formatMinNomMax(portValues.power, powerConverter);
    const current = FormatHelpers.formatMinNomMax(portValues.current, currentConverter);

    const specifications = [voltage, power, current].filter(Boolean);
    const controlMethods: string[] = [];

    (portValues.controlMethods || []).forEach((controlMethod: string) => {
        const abbreviation = PortControlMethods.abbreviations[controlMethod as PortControlMethod];

        if (abbreviation) {
            controlMethods.push(abbreviation);
        }
    });

    return (
        <Flex lh={1.25}>
            <Text inherit span c={voltageType} fw={600} pr="xs">
                {voltageType}
            </Text>
            {specifications.length + controlMethods.length > 0 ? (
                <Text inherit span>
                    {specifications.join(' • ')}
                    {specifications.length && controlMethods.length ? <br /> : null}
                    {controlMethods.join(' • ')}
                </Text>
            ) : (
                <Text inherit c="gray.5">
                    No specifications
                </Text>
            )}
        </Flex>
    );
};

const ChargingPortSpecifications: FC<{
    port: Charger['electrical']['chargingPorts'][number];
    index: number;
}> = ({ port, index }) => {
    if (!port.voltageType) return null;

    const subtitle = ChargerConnector.options.find((c) => port.connector === c.value)?.label || 'Unknown connector';

    return (
        <Stack gap={4}>
            <DiagramComponentOverviewHitDatasheetTitle title={`Charging Port ${index + 1}`} subtitle={subtitle} />
            <DiagramPortSpecificationLine portValues={port} voltageType={port.voltageType} />
        </Stack>
    );
};

export { DiagramComponentOverviewHitDatasheet };
