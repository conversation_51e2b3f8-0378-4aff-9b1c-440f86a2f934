import React, { FC, useEffect, useState } from 'react';

import { Box, Portal, ScrollArea, Stack, Transition } from '@mantine/core';
import { useHeadroom, useWindowScroll } from '@mantine/hooks';

import { ComponentCount } from 'models';

import { useSidebarNav } from 'hooks/use-sidebar-nav';

import { SearchBar } from 'components/component-overview/components/SearchBar';
import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

import cx from './ComponentOverview.StickyFilters.module.scss';

const ComponentOverviewStickyFilters: FC<{
    componentCount?: ComponentCount;
}> = ({ componentCount }) => {
    const [hero, setHero] = useState<Element | null>(null);
    const scrollOffset = hero ? hero.getBoundingClientRect().height : 200;

    const { isOpen } = useSidebarNav();

    const scrollUp = useHeadroom();
    const [scroll] = useWindowScroll();

    useEffect(() => {
        const findHero = document.querySelector('[data-hero]');

        if (findHero) {
            setHero(findHero);
        }
    }, []);

    const showSearchBar = scroll.y > scrollOffset;
    const showFilters = scrollUp;

    return (
        <Portal>
            <Transition transition="slide-down" mounted={showSearchBar}>
                {(transitionStyles) => (
                    <Stack data-sidebar-open={isOpen} style={transitionStyles} className={cx.root}>
                        <Box className={cx.inner} data-show-filters={showFilters}>
                            <SearchBar size="md" />
                        </Box>

                        <Transition transition="slide-down" mounted={showFilters}>
                            {(transitionStyles) => (
                                <Box style={transitionStyles} className={cx.filters}>
                                    <ScrollArea w="100%" type="never">
                                        <InlineFilters componentCount={componentCount} variant="dark" size="md" />
                                    </ScrollArea>
                                </Box>
                            )}
                        </Transition>
                    </Stack>
                )}
            </Transition>
        </Portal>
    );
};

export { ComponentOverviewStickyFilters };
