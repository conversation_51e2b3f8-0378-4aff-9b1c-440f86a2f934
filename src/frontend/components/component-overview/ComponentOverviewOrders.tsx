import React, { FC, useEffect, useState } from 'react';

import Link from 'next/link';

import { ActionIcon, Anchor, Flex, Menu, Paper, Stack, Text, Tooltip, UnstyledButton } from '@mantine/core';
import { TbChevronDown, TbPlus } from 'react-icons/tb';

import { ApiService } from 'services/ApiService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { Component } from 'models';
import { Order } from 'models';
import { OrderController, useOrder} from 'hooks/use-order';
import { useComponentMeta } from 'hooks/use-component-meta';
import { useOrderMeta } from 'hooks/use-order-meta';
import {useOrders} from 'hooks/use-orders';
import { useCurrentUser } from 'hooks/use-current-user';
import { QuantityField } from 'components/order-components-table/fields';
import { OrderStatus } from 'components/order/components';
import { IoOpenOutline } from 'react-icons/io5';
import { publicConfig } from '@public-config';

const ComponentOverviewOrders = () => {
    const user = useCurrentUser();
    const { orders } = useOrders({ depth: 1, limit: 9999 });

    const currentOrder = orders.find((order) => order.id === user?.order);

    return (
        <Paper p="md" shadow="sm" pos="relative">
            <Stack gap="sm">
                <Flex justify="space-between">
                    <OrderSwitch currentOrder={currentOrder} />
                    <Flex gap={4}>
                        {currentOrder && <GoToOrderButton order={currentOrder} />}
                        <CreateOrderButton />
                    </Flex>
                </Flex>

                {currentOrder && (
                    <OrderStatus
                        order={currentOrder}
                        justify="flex-start"
                        mb={0}
                        showSecondaryActions={false}
                        showLabels={false}
                    />
                )}

                {currentOrder && <OrderItems order={currentOrder} />}
            </Stack>
        </Paper>
    );
};

const GoToOrderButton: FC<{ order: Order }> = ({ order }) => {
    const { url } = useOrderMeta(order);
    return (
        <Link href={url} legacyBehavior>
            <Tooltip label="Go to quote" withArrow>
                <ActionIcon component="a" size="sm" variant="light" color="primary">
                    <IoOpenOutline size={10} style={{ pointerEvents: 'none' }} />
                </ActionIcon>
            </Tooltip>
        </Link>
    );
};

const CreateOrderButton = () => {
    return (
        <Link href="/quotes/create" legacyBehavior>
            <Tooltip label="Add quote" withArrow>
                <ActionIcon component="a" size="sm" variant="light" color="primary">
                    <TbPlus size={12} strokeWidth={2} />
                </ActionIcon>
            </Tooltip>
        </Link>
    );
};

const OrderSwitch: FC<{
    currentOrder?: Order;
}> = ({ currentOrder }) => {
    const { orders } = useOrders({ depth: 1, limit: 9999 });
    const ordersToShow = orders.filter((order) => order.status === 'draft');

    if (ordersToShow.length === 0) return <Text c="gray.5">No quotes</Text>;

    return (
        <Menu position="bottom-start">
            <Menu.Target>
                <Anchor type="button" c="primary" variant="light">
                    <Flex align="center" gap={4}>
                        {currentOrder?.name ? currentOrder.name : 'Choose quote'}
                        <TbChevronDown size={12} />
                    </Flex>
                </Anchor>
            </Menu.Target>
            <Menu.Dropdown>
                {ordersToShow.map((order) => (
                    <Menu.Item key={order.id} disabled={currentOrder?.id === order.id} py={6} px={8}>
                        <OrderSwitchItem order={order} isActive={currentOrder?.id === order.id} />
                    </Menu.Item>
                ))}
            </Menu.Dropdown>
        </Menu>
    );
};

const OrderSwitchItem: FC<{
    order: Order;
    isActive?: boolean;
}> = ({ order: { id, name }, isActive }) => {
    const switchOrder = async () => {
        await ApiService.post(`${publicConfig.urls.api}/users/switch-order`, { order: id });

        // reload user.order
        window.location.reload();

        LocalNotificationService.showSuccess({
            message: `Active Order switched to ${name}`,
        });
    };

    return (
        <UnstyledButton
            onClick={switchOrder}
            style={{
                width: '100%',
                outline: 'none !important',
            }}
        >
            <Text fw={400}>
                {name}
                {isActive && (
                    <Text span c="gray.5">
                        {' '}
                        (current)
                    </Text>
                )}
            </Text>
        </UnstyledButton>
    );
};

const OrderItems: FC<{ order: Order }> = ({ order: serverSideOrder }) => {
    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 1 });

    const items = orderController.order?.components;

    const [itemsToShow, setItemsToShow] = useState<Order['components']>([]);

    useEffect(() => {
        setItemsToShow(items?.slice(0, 10) ?? []);
    }, [items]);

    if (orderController.order === null) return null;

    if (orderController.isEmpty) return <Text c="gray.5">Order is empty</Text>;

    if (typeof items?.[0].component === 'string') return null;

    return (
        <Stack gap="xs">
            {itemsToShow?.map((orderComponent) => (
                <OrderItem
                    key={(orderComponent.component as Component).id}
                    orderComponent={orderComponent.component as Component}
                    data={orderComponent}
                    controller={orderController}
                />
            ))}
            {items?.length && itemsToShow?.length !== items.length && (
                <Anchor type="button" ta="center" onClick={() => setItemsToShow(items)}>
                    Show all
                </Anchor>
            )}
        </Stack>
    );
};

const OrderItem: FC<{
    orderComponent: Component;
    data: Required<Order>['components'][number];
    controller: OrderController;
}> = ({ orderComponent, data: { quantity }, controller }) => {
    const { uniqueName } = useComponentMeta(orderComponent);

    const removeComponentFromOrder = async () => {
        await controller.removeComponent(orderComponent.id);
    };

    const onQuantityChange = async (newQuantity: number) => {
        await controller.updateComponent(orderComponent.id, { quantity: newQuantity });
    };

    return (
        <Flex gap={8} align="flex-start" justify="space-between">
            <Text fw={600}>{uniqueName}</Text>
            {controller.order?.status === 'draft' ? (
                <QuantityField
                    size="xs"
                    quantity={quantity}
                    onChange={onQuantityChange}
                    onRemove={removeComponentFromOrder}
                />
            ) : (
                <Text>
                    <Text span fz={8}>
                        ✕
                    </Text>
                    &nbsp;{quantity}
                </Text>
            )}
        </Flex>
    );
};

export { ComponentOverviewOrders };
