import React from 'react';

import Link from 'next/link';

import { Button } from '@mantine/core';
import { IoAdd } from 'react-icons/io5';

import { UserReferrer } from 'models';

import { useCurrentUser } from 'hooks/use-current-user';
import { useLocalUserInfo } from 'hooks/use-local-user-info';

const ComponentOverviewActions = () => {
    const currentUser = useCurrentUser();
    const { referrer } = useLocalUserInfo();

    if (currentUser) return null;
    if (referrer !== UserReferrer.REPLUS) return null;

    return (
        <Button variant="filled" size="xs" leftSection={<IoAdd />} component={Link} href="/login?type=manufacturer">
            Sign up as exhibitor
        </Button>
    );
};

export { ComponentOverviewActions };
