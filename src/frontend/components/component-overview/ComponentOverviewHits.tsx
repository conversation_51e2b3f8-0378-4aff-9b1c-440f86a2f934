import { FC } from 'react';

import { An<PERSON>, Box, Flex, Loader, Stack, Text } from '@mantine/core';

import { Component } from 'models';

import { Loader as SearchLoader } from 'components/search/components/Loader';
import { ComponentOverviewHit } from './ComponentOverviewHit';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import {
    DiagramComponentOverviewHit,
    DraggableDiagramComponentOverviewHit,
} from 'components/component-overview/ComponentOverviewHit.Diagram';
import { GridSection } from 'components/section/GridSection';
import { ImpressionTracker } from 'components/component-overview/components/ImpressionTracker';
import { useLocalEvent } from 'hooks/use-local-event';

const ComponentOverviewHits: FC<{
    hits: Component[];
    isLoading?: boolean;
    emptyMessage?: string;
}> = ({ hits, emptyMessage, isLoading }) => {
    const { localEvent, setLocalEvent } = useLocalEvent();

    if (isLoading) {
        return <SearchLoader />;
    }

    return hits.length ? (
        <GridSection nbCols={4}>
            {hits.map((component: any) => (
                <ImpressionTracker
                    namespace="component.search"
                    data={{
                        component: component.id,
                        company: component.manufacturer,
                        position: component.__position,
                    }}
                    key={component.id}
                >
                    <ComponentOverviewHit showBooth component={component} showEmptyImage />
                </ImpressionTracker>
            ))}
        </GridSection>
    ) : (
        <EmptyMessage>
            {emptyMessage || <>Nothing found, try different search criteria?</>}

            {localEvent && (
                <Box>
                    <Anchor component="button" onClick={() => setLocalEvent(null)} fz={12}>
                        Search again without the <span style={{ fontWeight: 500 }}>{localEvent.name}</span> event
                        filter.
                    </Anchor>
                </Box>
            )}
        </EmptyMessage>
    );
};

const DiagramComponentOverviewHits: FC<{
    hits: Component[];
    isLoading?: boolean;
    draggable?: boolean;
}> = ({ hits, isLoading, draggable }) => {
    if (isLoading)
        return (
            <Flex justify="center" mt="xl">
                <Loader size="sm" />
            </Flex>
        );

    return !hits.length ? (
        <Text c={'dimmed'} mt="xs" ta="center" fz="sm">
            No products found. Try adjusting the filters.
        </Text>
    ) : (
        <Stack gap={8} pos="relative">
            {hits.map((component: any) =>
                draggable ? (
                    <DraggableDiagramComponentOverviewHit key={component.id} component={component} />
                ) : (
                    <DiagramComponentOverviewHit key={component.id} component={component} />
                ),
            )}
        </Stack>
    );
};

export { ComponentOverviewHits, DiagramComponentOverviewHits };
