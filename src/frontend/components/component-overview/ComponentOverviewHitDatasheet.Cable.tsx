import React from 'react';

import { Group, Stack, Text } from '@mantine/core';

import { Cable } from 'models';

import { FormatHelpers } from 'helpers/formatters';
import { VoltageTypeBadge } from 'components/buttons';

const ComponentOverviewHitDatasheetCable = ({ cable }: { cable: Cable }) => {
    if (!cable?.electrical && !cable?.mechanical) return null;

    const operatingVoltage = FormatHelpers.formatOperatingVoltage(cable.electrical.operatingVoltage);
    const wireSize = FormatHelpers.formatWireSize(cable.electrical.wireSize);
    const numberOfConductors = cable.electrical.numberOfConductors;
    const hasPE = cable.electrical.hasPE;
    const voltageTypes = cable.electrical.voltageTypes || [];

    const specifications = [
        { label: 'Material', value: cable.mechanical.conductorMaterial },
        {
            label: 'Voltage types',
            value:
                voltageTypes.length > 0
                    ? voltageTypes.map((voltageType) => (
                          <VoltageTypeBadge key={voltageType} voltageType={voltageType} activated enabled />
                      ))
                    : null,
        },
        { label: 'Wire size', value: wireSize },
        {
            label: 'Number of conductors',
            value: numberOfConductors ? `${numberOfConductors}${hasPE ? ' + PE' : ''}` : null,
        },
        { label: 'Operating voltage', value: operatingVoltage },
    ].filter((specification) => specification.value);

    if (specifications.length === 0) return null;

    return (
        <Stack gap={4}>
            {specifications.map((specification) => (
                <CableSpecificationLine
                    key={specification.label}
                    label={specification.label}
                    value={specification.value}
                />
            ))}
        </Stack>
    );
};

const CableSpecificationLine = ({ label, value }: { label: string; value: React.ReactNode }) => {
    return (
        <Group fz="sm" gap={4}>
            <Text span inherit fw={500}>
                {label}:
            </Text>
            {value}
        </Group>
    );
};

export { ComponentOverviewHitDatasheetCable };
