import React, { FC } from 'react';

import { Text, Stack, Anchor } from '@mantine/core';

import Link from 'next/link';

import { useComponentMeta } from 'hooks';

import { Component } from 'models';

import { ComponentThumbnail } from 'components/thumbnail';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import cx from './ComponentOverviewHit.Simple.module.scss';

type Props = {
    component: Component;
    href?: string;
    rightSide?: React.ReactNode;
    target?: HTMLAnchorElement['target'];
};

const ComponentOverviewHitSimple: FC<Props> = ({ component, href: href_, rightSide, target = '_blank' }) => {
    const { uniqueName, subtitle, slug } = useComponentMeta(component);

    const href = href_ ? href_ : component?.id ? ComponentHelpers.urls.view(component.id, slug) : undefined;
    const hasWrapperLink = Boolean(!rightSide && href);

    return (
        <Anchor
            component={hasWrapperLink ? Link : undefined}
            href={hasWrapperLink && href ? href : ''}
            className={cx.root}
            target={target}
        >
            <ComponentThumbnail
                showEmpty
                component={component as Component}
                style={{ width: 40, padding: 2, border: '1px solid var(--mantine-color-gray-2)', flex: 'auto 0 0' }}
            />
            <Stack gap={0}>
                {hasWrapperLink ? (
                    <Text fz="xs" c="primary">
                        {uniqueName}
                    </Text>
                ) : href ? (
                    <Text component={Link} href={href} fz="xs" c="primary">
                        {uniqueName}
                    </Text>
                ) : (
                    <Text fz="xs">{uniqueName}</Text>
                )}

                <Text fz="xs" c="dimmed">
                    {subtitle}
                </Text>
            </Stack>
            {rightSide}
        </Anchor>
    );
};

export { ComponentOverviewHitSimple };
