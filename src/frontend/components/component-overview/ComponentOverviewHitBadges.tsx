import React, { FC } from 'react';

import { Group, Text } from '@mantine/core';

import { CompanyProfileHelpers, Component, ComponentVisibility, PublishedStatus, SavedItemType } from 'models';

import { useComponentMeta } from 'hooks/use-component-meta';
import { useCompanyProfile } from 'hooks/use-company-profile';

import { SaveButton } from 'components/save-button/SaveButton';
import { InAppSupportBadge } from 'components/badges/InAppSupportBadge';
import { PublishedBadge } from 'components/published-badge/PublishedBadge';
import { InternalScoreBadge } from 'components/internal-score-badge/InternalScoreBadge';

const ComponentOverviewHitBadges: FC<{ component: Component; showIntercom?: boolean; isExternalLink?: boolean }> = ({
    component,
    showIntercom,
    isExternalLink,
}) => {
    const { price, leadTime } = useComponentMeta(component);
    const { company: manufacturer } = useCompanyProfile(component.manufacturer);

    // const viewedCount = component.viewedCount || 0;

    const showUnpublished = component.manufacturer && component.visibility === ComponentVisibility.PRIVATE;

    return (
        <Group gap="xs" fz="xs" c="dimmed" mt="auto" pt="xs">
            <InternalScoreBadge item={component as any} />
            <SaveButton id={component.id} name={component.name} type={SavedItemType.COMPONENT} />
            {showIntercom && manufacturer && CompanyProfileHelpers.offersInAppSupport(manufacturer) && (
                <InAppSupportBadge component={component} company={manufacturer} isExternalLink={isExternalLink} />
            )}
            {/* {viewedCount > 0 && (
                <Text inherit>
                    {viewedCount} {viewedCount === 1 ? 'view' : 'views'}
                </Text>
            )} */}
            {price && <Text inherit>Avg. {price} </Text>}
            {leadTime && <Text inherit>Lead time: {leadTime} </Text>}

            {showUnpublished && <PublishedBadge status={PublishedStatus.DRAFT} variant="light" />}
        </Group>
    );
};

export { ComponentOverviewHitBadges };
