import React, { FC } from 'react';

import { Box, UnstyledButton } from '@mantine/core';

import {
    useComponentSearchTabs,
    ComponentSearchTab,
} from 'components/component-overview/hooks/use-component-search-tabs';

import cx from './ComponentOverview.module.scss';

const ComponentOverviewTabs: FC<{
    totals: {
        components: string | number;
        companies: string | number;
        caseStudies: string | number;
    };
}> = ({ totals }) => {
    const { tab: activeTab, setTab } = useComponentSearchTabs();

    const tabs = [
        {
            label: totals.components === 1 ? '1 product' : `${totals.components} products`,
            key: ComponentSearchTab.COMPONENTS,
        },
        {
            label: totals.companies === 1 ? '1 profile' : `${totals.companies} profiles`,
            key: ComponentSearchTab.COMPANIES,
        },
        {
            label: totals.caseStudies === 1 ? '1 case study' : `${totals.caseStudies} case studies`,
            key: ComponentSearchTab.CASE_STUDIES,
        },
    ];

    return (
        <Box className={cx.tabs}>
            {tabs.map((tab) => (
                <UnstyledButton
                    className={cx.tab}
                    onClick={() => {
                        setTab(tab.key as any);
                    }}
                    data-active={activeTab === tab.key}
                    key={tab.key}
                >
                    {tab.label}
                </UnstyledButton>
            ))}
        </Box>
    );
};

export { ComponentOverviewTabs };
