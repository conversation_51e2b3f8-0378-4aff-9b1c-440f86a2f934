import { Center, Pagination, PaginationProps } from '@mantine/core';

function ComponentOverviewPagination({
    page,
    totalPages,
    setPage,
    ...paginationProps
}: {
    page: number;
    totalPages?: number;
    setPage: (page: number) => void;
} & Pick<PaginationProps, 'size'>) {
    if (totalPages && totalPages > 1) {
        return (
            <Center py="md">
                <Pagination
                    value={page + 1}
                    onChange={(newPage) => setPage(newPage - 1)}
                    total={totalPages}
                    {...paginationProps}
                />
            </Center>
        );
    }

    return null;
}

export { ComponentOverviewPagination };
