import React, { <PERSON> } from 'react';

import { GridSection } from 'components/section/GridSection';
import { Stub } from 'components/company-profile/components/Stub';
import { ImpressionTracker } from 'components/component-overview/components/ImpressionTracker';

const ComponentOverviewCaseStudies: FC<{
    caseStudies: any[];
}> = ({ caseStudies }) => (
    <GridSection nbCols={4}>
        {caseStudies.map((caseStudy: any, index) => (
            <REFACTORCaseStudyTeaser caseStudy={caseStudy} position={index + 1} key={caseStudy.id} />
        ))}
    </GridSection>
);

const REFACTORCaseStudyTeaser: FC<{
    caseStudy: any;
    position: number;
}> = ({ caseStudy, position }) => {
    return (
        <ImpressionTracker
            namespace="caseStudy.search"
            data={{
                articleId: caseStudy.id,
                position,
            }}
            key={caseStudy.id}
        >
            <Stub showEmptyImage name={caseStudy.name} description={caseStudy.text} image={caseStudy.teaser?.image} />
        </ImpressionTracker>
    );
};

export { ComponentOverviewCaseStudies };
