import React, { useEffect, FC } from 'react';

import { Text, Stack, Card, Anchor, Tooltip, Box, Flex } from '@mantine/core';

import Link from 'next/link';

import { useComponentMeta, useCompanyProfile } from 'hooks';

import { Component, ComponentSection, ManufacturerProfile, PublishedStatus } from 'models';

import { CompanyLogo } from 'components/company-logo';
import { ComponentThumbnail } from 'components/thumbnail';
import { ComponentOverviewHitDatasheet } from './ComponentOverviewHitDatasheet';
import { ComponentOverviewHitDatasheetCable } from 'components/component-overview/ComponentOverviewHitDatasheet.Cable';
import { ComponentOverviewHitBadges } from 'components/component-overview/ComponentOverviewHitBadges';
import { BoothNumber } from 'components/booth-number/BoothNumber';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { ComponentOverviewHitDatasheetOther } from 'components/component-overview/ComponentOverviewHitDatasheet.Other';

const ComponentOverviewHit: FC<{
    component: Component & { id?: string };
    showEmptyImage?: boolean;
    progressSections?: ComponentSection[];
    showBooth?: boolean;
    showIntercom?: boolean;
    isExternalLink?: boolean;
    onView?: () => void;
    onClick?: () => void;
    children?: React.ReactNode;
}> = ({ component, showEmptyImage, showBooth, showIntercom, isExternalLink, onView, onClick, children }) => {
    const { uniqueName, subtitle, slug } = useComponentMeta(component);

    const { company: manufacturer } = useCompanyProfile(component.manufacturer);

    const images = component?.images?.filter((image: any) => image.file) ?? [];

    useEffect(() => {
        onView?.();
    });

    const isExistingProduct = !!component.id;
    const url = isExistingProduct ? ComponentHelpers.urls.view(component.id, slug) : '#';

    return (
        <Card withBorder onClick={onClick} h="100%" style={{ borderColor: 'var(--mantine-color-gray-2)' }}>
            {(showEmptyImage || images.length > 0) && (
                <Card.Section p="xs" pos="relative">
                    <Anchor
                        component={Link}
                        href={url}
                        style={{ outline: 'none' }}
                        target={isExternalLink ? '_blank' : undefined}
                    >
                        <ComponentThumbnail
                            showEmpty
                            component={component}
                            style={{
                                width: '100%',
                                height: 100,
                            }}
                        />

                        {showBooth && manufacturer && (
                            <Box
                                style={{
                                    position: 'absolute',
                                    top: 6,
                                    right: 6,
                                }}
                            >
                                <BoothNumber company={manufacturer} showEventName />
                            </Box>
                        )}
                    </Anchor>
                </Card.Section>
            )}

            {/* title + manufacturer */}
            <Flex justify="space-between" gap={8}>
                <Stack gap={0} pb="xs">
                    <Anchor
                        component={Link}
                        href={url}
                        fw={600}
                        style={{ flex: 1 }}
                        c="brand"
                        target={isExternalLink ? '_blank' : undefined}
                    >
                        {uniqueName}
                    </Anchor>
                    <Text
                        c="dimmed"
                        fz="sm"
                        style={{ textOverflow: 'ellipsis', whiteSpace: 'nowrap', overflow: 'hidden' }}
                        maw={300}
                    >
                        {subtitle}
                    </Text>
                </Stack>
                {manufacturer && <Manufacturer manufacturer={manufacturer} />}
            </Flex>

            {component.type === 'cable' ? (
                <ComponentOverviewHitDatasheetCable cable={component} />
            ) : component.type === 'other' ? (
                <ComponentOverviewHitDatasheetOther component={component} />
            ) : (
                <ComponentOverviewHitDatasheet component={component} />
            )}

            {/* <Space h="md" /> */}

            {/* <Box>
                <OrderButton componentId={component.id} isLight />
            </Box> */}

            {isExistingProduct && (
                <ComponentOverviewHitBadges
                    component={component}
                    showIntercom={showIntercom}
                    isExternalLink={isExternalLink}
                />
            )}

            {children}
        </Card>
    );
};

const Manufacturer: FC<{ manufacturer: ManufacturerProfile; isExternalLink?: boolean }> = ({
    manufacturer,
    isExternalLink,
}) => {
    if (manufacturer.status === PublishedStatus.PUBLISHED) {
        return (
            <Tooltip label="View profile">
                <Link
                    href={CompanyProfileHelpers.urls.view(manufacturer.slug)}
                    target={isExternalLink ? '_blank' : undefined}
                >
                    <CompanyLogo logos={manufacturer.logos} width={50} fallback={manufacturer.name} />
                </Link>
            </Tooltip>
        );
    }

    return <CompanyLogo logos={manufacturer.logos} width={50} fallback={manufacturer.name} />;
};

export { ComponentOverviewHit };
