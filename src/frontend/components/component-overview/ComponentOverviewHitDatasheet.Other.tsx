import React from 'react';

import { Spoiler } from '@mantine/core';

import { Component } from 'models';

const ComponentOverviewHitDatasheetOther = ({ component }: { component: Component }) => {
    return (
        <Spoiler
            hideLabel="Show less"
            showLabel="Show more"
            maxHeight={95}
            fz="sm"
            styles={{
                control: {
                    fontSize: 'var(--mantine-font-size-sm)',
                    fontWeight: 500,
                },
            }}
        >
            <div dangerouslySetInnerHTML={{ __html: component.description }} />
        </Spoiler>
    );
};

export { ComponentOverviewHitDatasheetOther };
