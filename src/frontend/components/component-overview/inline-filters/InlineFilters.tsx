import React, { FC } from 'react';

import { ComponentCount, ViewOnlyFilter } from 'models';

import { InlineFiltersWrapper, InlineFiltersWrapperProps } from 'components/inline-filters/InlineFilters.Wrapper';
import { InlineFiltersSection, InlineFiltersSectionWithPopover } from 'components/inline-filters/InlineFilters.Section';
import { InlineFiltersSectionRemove } from 'components/inline-filters/InlineFilters.SectionRemove';
import { InlineFiltersMeasurementWrapper } from 'components/inline-filters/InlineFilters.MeasurementWrapper';
import { InlineFiltersMeasurementPlaceholder } from 'components/inline-filters/InlineFilters.MeasurementPlaceholder';
import { InlineFiltersNumber } from 'components/inline-filters/InlineFilters.Number';
import { InlineFiltersSwitcher } from 'components/inline-filters/InlineFilters.Switcher';

import { InlineFiltersMeasurementValue } from './components/InlineFilters.MeasurementValue';
import { InlineFiltersPortWrapper } from './components/InlineFilters.PortWrapper';

import { InlineFiltersType } from './filters/InlineFilters.Type';
import { InlineFiltersPorts } from './filters/InlineFilters.Ports';
import { InlineFiltersCharging } from './filters/InlineFilters.Charging';
import { InlineFiltersManufacturer } from './filters/InlineFilters.Manufacturer';
import { InlineFiltersDistributor } from './filters/InlineFilters.Distributor';
import { InlineFiltersRegionAvailability } from './filters/InlineFilters.RegionAvailability';
import { InlineFiltersCompliances } from './filters/InlineFilters.Compliances';
import { InlineFiltersApplication } from './filters/InlineFilters.Application';
import { InlineFiltersVoltage } from './filters/InlineFilters.Voltage';
import { InlineFiltersPower } from './filters/InlineFilters.Power';
import { InlineFiltersCurrent } from './filters/InlineFilters.Current';
import { InlineFiltersPowerFlowDirection } from './filters/InlineFilters.PowerFlowDirection';
import { InlineFiltersVoltageType } from './filters/InlineFilters.VoltageType';
import { InlineFiltersCapacity } from './filters/InlineFilters.Capacity';
import { InlineFiltersArchived } from './filters/InlineFilters.Archived';
import { InlineFiltersSort } from './filters/InlineFilters.Sort';
import { InlineFiltersIsolated } from './filters/InlineFilters.Isolated';
import { InlineFiltersPurpose } from './filters/InlineFilters.Purpose';
import { InlineFiltersSearch } from './filters/InlineFilters.Search';
import { InlineFiltersAIEnabled } from './filters/InlineFilters.AIEnabled';
import { InlineFiltersInAppSupport } from './filters/InlineFilters.InAppSupport';
import { InlineFiltersClearButton } from './filters/InlineFilters.ClearButton';
import { InlineFiltersMoreButton } from './filters/InlineFilters.MoreButton';
import { InlineFiltersEvent } from './filters/InlineFilters.Event';
import { InlineFiltersBack } from './components/InlineFilters.Back';
import { InlineFiltersPriceFlag, InlineFiltersMaxPrice } from './filters/InlineFilters.Price';
import { InlineFiltersLeadTime } from './filters/InlineFilters.LeadTime';
import { SearchService } from 'components/component-overview/services/SearchService';
import { InlineFiltersCable } from 'components/component-overview/inline-filters/filters/InlineFilters.Cable';

export type InlineFiltersProps = InlineFiltersWrapperProps & {
    componentCount?: ComponentCount;
    hideFilters?: (
        | 'search'
        | 'type'
        | 'manufacturer'
        | 'distributor'
        | 'capacity'
        | 'ports'
        | 'archived'
        | 'aiEnabled'
        | 'inAppSupport'
        | 'clear'
        | 'regionAvailability'
        | 'compliances'
        | 'application'
        | 'charging'
        | 'maxPrice'
        | 'hasPrice'
        | 'hasLeadTime'
        | 'cable'
        | 'more'
        | 'event'
    )[];
    viewOnlyFilters?: ViewOnlyFilter[];
};

const InlineFilters: FC<InlineFiltersProps> & {
    Wrapper: typeof InlineFiltersWrapper;
    Section: typeof InlineFiltersSection;
    SectionWithPopover: typeof InlineFiltersSectionWithPopover;
    RemoveButton: typeof InlineFiltersSectionRemove;
    MeasurementWrapper: typeof InlineFiltersMeasurementWrapper;
    MeasurementPlaceholder: typeof InlineFiltersMeasurementPlaceholder;
    MeasurementValue: typeof InlineFiltersMeasurementValue;
    Number: typeof InlineFiltersNumber;
    PortWrapper: typeof InlineFiltersPortWrapper;
    Switcher: typeof InlineFiltersSwitcher;
    Type: typeof InlineFiltersType;
    Manufacturer: typeof InlineFiltersManufacturer;
    Distributor: typeof InlineFiltersDistributor;
    RegionAvailability: typeof InlineFiltersRegionAvailability;
    Compliances: typeof InlineFiltersCompliances;
    Application: typeof InlineFiltersApplication;
    Ports: typeof InlineFiltersPorts;
    Charging: typeof InlineFiltersCharging;
    Voltage: typeof InlineFiltersVoltage;
    Power: typeof InlineFiltersPower;
    Current: typeof InlineFiltersCurrent;
    PowerFlowDirection: typeof InlineFiltersPowerFlowDirection;
    Isolated: typeof InlineFiltersIsolated;
    Purpose: typeof InlineFiltersPurpose;
    VoltageType: typeof InlineFiltersVoltageType;
    Capacity: typeof InlineFiltersCapacity;
    Archived: typeof InlineFiltersArchived;
    Sort: typeof InlineFiltersSort;
    Search: typeof InlineFiltersSearch;
    AIEnabled: typeof InlineFiltersAIEnabled;
    InAppSupport: typeof InlineFiltersInAppSupport;
    Clear: typeof InlineFiltersClearButton;
    More: typeof InlineFiltersMoreButton;
    Back: typeof InlineFiltersBack;
    MaxPrice: typeof InlineFiltersMaxPrice;
    PriceFlag: typeof InlineFiltersPriceFlag;
    LeadTime: typeof InlineFiltersLeadTime;
    Event: typeof InlineFiltersEvent;
    Cable: typeof InlineFiltersCable;
} = ({ componentCount, hideFilters, viewOnlyFilters, ...props }) => {
    return (
        <InlineFilters.Wrapper {...props}>
            {!hideFilters?.includes('event') && <InlineFilters.Event search={SearchService.search} />}
            {!hideFilters?.includes('search') && <InlineFilters.Search />}
            {!hideFilters?.includes('type') && (
                <InlineFilters.Type
                    componentCount={componentCount}
                    isViewOnly={viewOnlyFilters?.includes(ViewOnlyFilter.TYPE)}
                />
            )}
            {!hideFilters?.includes('manufacturer') && (
                <InlineFilters.Manufacturer isViewOnly={viewOnlyFilters?.includes(ViewOnlyFilter.MANUFACTURER)} />
            )}
            {!hideFilters?.includes('distributor') && (
                <InlineFilters.Distributor isViewOnly={viewOnlyFilters?.includes(ViewOnlyFilter.DISTRIBUTOR)} />
            )}
            {!hideFilters?.includes('cable') && <InlineFilters.Cable />}
            {!hideFilters?.includes('maxPrice') && <InlineFilters.MaxPrice />}
            {!hideFilters?.includes('hasPrice') && <InlineFilters.PriceFlag />}
            {!hideFilters?.includes('hasLeadTime') && <InlineFilters.LeadTime />}
            {!hideFilters?.includes('regionAvailability') && <InlineFilters.RegionAvailability />}
            {!hideFilters?.includes('compliances') && <InlineFilters.Compliances />}
            {!hideFilters?.includes('application') && <InlineFilters.Application />}
            {!hideFilters?.includes('aiEnabled') && <InlineFilters.AIEnabled />}
            {!hideFilters?.includes('ports') && <InlineFilters.Ports />}
            {!hideFilters?.includes('charging') && <InlineFilters.Charging />}
            {!hideFilters?.includes('capacity') && <InlineFilters.Capacity />}
            {!hideFilters?.includes('archived') && <InlineFilters.Archived />}

            {!hideFilters?.includes('more') && <InlineFilters.More />}
        </InlineFilters.Wrapper>
    );
};

InlineFilters.Wrapper = InlineFiltersWrapper;
InlineFilters.Section = InlineFiltersSection;
InlineFilters.SectionWithPopover = InlineFiltersSectionWithPopover;
InlineFilters.RemoveButton = InlineFiltersSectionRemove;
InlineFilters.MeasurementWrapper = InlineFiltersMeasurementWrapper;
InlineFilters.MeasurementPlaceholder = InlineFiltersMeasurementPlaceholder;
InlineFilters.MeasurementValue = InlineFiltersMeasurementValue;
InlineFilters.Number = InlineFiltersNumber;
InlineFilters.PortWrapper = InlineFiltersPortWrapper;
InlineFilters.Switcher = InlineFiltersSwitcher;

InlineFilters.Type = InlineFiltersType;
InlineFilters.Manufacturer = InlineFiltersManufacturer;
InlineFilters.Distributor = InlineFiltersDistributor;
InlineFilters.RegionAvailability = InlineFiltersRegionAvailability;
InlineFilters.Compliances = InlineFiltersCompliances;
InlineFilters.Application = InlineFiltersApplication;
InlineFilters.Ports = InlineFiltersPorts;
InlineFilters.Voltage = InlineFiltersVoltage;
InlineFilters.Power = InlineFiltersPower;
InlineFilters.Charging = InlineFiltersCharging;
InlineFilters.Current = InlineFiltersCurrent;
InlineFilters.PowerFlowDirection = InlineFiltersPowerFlowDirection;
InlineFilters.Isolated = InlineFiltersIsolated;
InlineFilters.Purpose = InlineFiltersPurpose;
InlineFilters.VoltageType = InlineFiltersVoltageType;
InlineFilters.Capacity = InlineFiltersCapacity;
InlineFilters.Archived = InlineFiltersArchived;
InlineFilters.Sort = InlineFiltersSort;
InlineFilters.Search = InlineFiltersSearch;
InlineFilters.AIEnabled = InlineFiltersAIEnabled;
InlineFilters.InAppSupport = InlineFiltersInAppSupport;
InlineFilters.Clear = InlineFiltersClearButton;
InlineFilters.More = InlineFiltersMoreButton;
InlineFilters.Back = InlineFiltersBack;
InlineFilters.MaxPrice = InlineFiltersMaxPrice;
InlineFilters.PriceFlag = InlineFiltersPriceFlag;
InlineFilters.LeadTime = InlineFiltersLeadTime;
InlineFilters.Event = InlineFiltersEvent;
InlineFilters.Cable = InlineFiltersCable;

export { InlineFilters };
