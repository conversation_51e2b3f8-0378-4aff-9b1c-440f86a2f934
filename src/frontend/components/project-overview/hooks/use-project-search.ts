import { ProjectService } from 'services/ProjectService';
import { ApiService } from 'services/ApiService';
import { Project } from 'models';

import { useInfiniteLoad } from 'hooks/use-infinite-load';

function useProjectSearch() {
    const { data, ScrollRef, isLoading, isLoadingMore, mutate } = useInfiniteLoad({
        getSearchUrl: (page) => ProjectService.getSearchUrl(page, 100),
        fetcher: async (url) => {
            try {
                const result: { docs: Project[] } = await ApiService.get(url, { throwError: true });
                return result.docs;
            } catch (e) {
                console.warn('Error getting projects - ', e);
                return [];
            }
        },
        pageSize: 100,
    });

    return {
        projects: data as Project[],

        ScrollRef,
        isLoading,
        isLoadingMore,

        mutate,
    };
}

export { useProjectSearch };
