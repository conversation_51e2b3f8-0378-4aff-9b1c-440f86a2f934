import { useMemo } from 'react';

import {
    averageComponentProgress,
    Component,
    ComponentSection,
    getComponentProgress,
    PermissionComponent,
} from 'models';

import { ComponentProgressRing } from 'components/component-progress/ComponentProgressRing';
import { useComponentPermissions } from 'hooks/use-component-permissions';

const ComponentProgress = ({ component, sections }: { component: Component; sections?: ComponentSection[] }) => {
    const permissions = useComponentPermissions(component);
    const canEdit = permissions[PermissionComponent.EDIT];

    const progress = useMemo(() => {
        return averageComponentProgress(getComponentProgress(component, sections));
    }, [JSON.stringify(component)]);

    if (!canEdit) return null;

    return <ComponentProgressRing progress={progress} />;
};

export { ComponentProgress };
