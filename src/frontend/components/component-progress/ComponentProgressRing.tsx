import { FC, ReactNode, forwardRef, useMemo } from 'react';

import { Box, DefaultMantineColor, Flex, RingProgress, Table, ThemeIcon, Tooltip } from '@mantine/core';
import { IoCheckmarkSharp } from 'react-icons/io5';

import { COMPONENT_PROGRESS_LEVELS } from 'components/component-progress/helpers/config';

import { FilterFunction, getSectionProgress } from 'models';

import cx from './ComponentProgressRing.module.scss';

const SIZE = 28;
const RING_SIZE = 28;
const SMALL_RING_SIZE = 20;
const STROKE = 2;

const ComponentSectionProgressRing = ({ values, filter }: { values: any; filter?: FilterFunction }) => {
    const progress = useMemo(() => getSectionProgress(values, filter), [JSON.stringify(values), filter]);

    return <ComponentProgressRing progress={progress} />;
};

const ComponentProgressRing = ({ progress }: { progress: number }) => {
    if (progress >= 100) {
        return <ComponentProgressRingComplete />;
    }

    const { color } = COMPONENT_PROGRESS_LEVELS.find((level) => progress < level.maxPercent)!;

    return (
        <InfoTooltip>
            <RingProgress
                roundCaps
                classNames={cx}
                sections={[{ value: progress, color }]}
                label={progress.toFixed()}
                data-color={color}
                size={SIZE}
                thickness={STROKE}
            />
        </InfoTooltip>
    );
};

const ComponentProgressRingComplete = () => {
    return (
        <InfoTooltip>
            <ComponentProgressRingIcon color="green" small>
                <IoCheckmarkSharp size={12} />
            </ComponentProgressRingIcon>
        </InfoTooltip>
    );
};

const ComponentProgressRingIcon = forwardRef<
    HTMLDivElement,
    { color: DefaultMantineColor; small?: boolean; children: React.ReactNode }
>(({ color, small = false, children }, ref) => {
    return (
        <Flex w={SIZE} h={SIZE} align="center" justify="center" ref={ref}>
            <ThemeIcon size={small ? SMALL_RING_SIZE : RING_SIZE} radius="xl" color={color}>
                {children}
            </ThemeIcon>
        </Flex>
    );
});
ComponentProgressRingIcon.displayName = 'ComponentProgressRingIcon';

const InfoTooltip: FC<{ children: ReactNode }> = ({ children }) => {
    const Label = (
        <Table>
            <Table.Tbody>
                {COMPONENT_PROGRESS_LEVELS.map(({ label, maxPercent }) => (
                    <Box component="tr" key={maxPercent}>
                        <Table.Td>{label[1]}</Table.Td>
                        <Table.Td align="right">{label[0]}</Table.Td>
                    </Box>
                ))}
            </Table.Tbody>
        </Table>
    );
    return <Tooltip label={Label}>{children}</Tooltip>;
};

export { ComponentSectionProgressRing, ComponentProgressRing, ComponentProgressRingIcon };
