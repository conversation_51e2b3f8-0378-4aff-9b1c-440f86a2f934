import { FC, CSSProperties } from 'react';

import { Box } from '@mantine/core';

import { Component } from 'models';

import { useFile } from 'hooks/use-file';

import { Thumbnail } from './Thumbnail';
import { ComponentIcon } from 'components/component-icons/ComponentIcon';

const ComponentThumbnail: FC<{
    component: Pick<Component, 'type' | 'images'> | undefined;
    style?: CSSProperties;
    showEmpty?: boolean;
}> = ({ component, style, showEmpty }) => {
    const images = component?.images?.filter((image: any) => image.file) ?? [];
    const { file, isLoading } = useFile(images[0]?.file);

    const fallbackSize = style?.width && typeof style.width === 'number' ? style.width / 2 : 50;

    return component?.type ? (
        <Thumbnail
            image={file}
            isLoading={isLoading}
            style={style}
            showEmpty={showEmpty}
            fallback={
                <Box w={fallbackSize}>
                    <ComponentIcon type={component.type} />
                </Box>
            }
        />
    ) : null;
};

export { ComponentThumbnail };
