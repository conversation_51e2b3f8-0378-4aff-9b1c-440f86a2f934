import { Group, Switch } from '@mantine/core';
import { IoSearch, IoSparkles } from 'react-icons/io5';

import { useIsMobile } from 'hooks/use-is-mobile';
import { useSearchPlanPage } from 'hooks/use-search-plan-page';

import { SearchPlanHelpers } from 'components/search-plan/SearchPlanHelpers';

import cx from './SearchPlanSwitch.module.scss';

const SearchPlanSwitch = () => {
    const isMobile = useIsMobile('sm');
    const iconSize = isMobile ? 12 : 14;

    const { type } = useSearchPlanPage();

    return (
        <Group fz="xs" gap={8} className={cx.root} data-search-plan={type}>
            <span>Classic</span>
            <Switch
                size={isMobile ? 'md' : 'lg'}
                classNames={cx}
                checked={type === 'plan'}
                onChange={async (e) => {
                    const newType = e.currentTarget.checked ? 'plan' : 'search';

                    SearchPlanHelpers.switchPage(newType);
                }}
                thumbIcon={type === 'plan' ? <IoSparkles size={iconSize} /> : <IoSearch size={iconSize} />}
            />
            <span>AI Mode</span>
        </Group>
    );
};

export { SearchPlanSwitch };
