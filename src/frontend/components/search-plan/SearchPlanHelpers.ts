import { readLocalStorageValue } from '@mantine/hooks';

import { searchPlanPageState } from 'state/search-plan-page';
import { state as currentUserState } from 'state/current-user';

import { RouterService } from 'services/RouterService';

import { UserHelpers } from 'helpers/UserHelpers';
import { RouterHelpers, SearchTab } from 'helpers/RouterHelpers';
import { UserReferrer, UserType } from 'models';

const SearchPlanHelpers = {
    switchPage: (type: 'search' | 'plan', searchTab: SearchTab = 'overview') => {
        const currentUser = currentUserState.user;

        const referrer = readLocalStorageValue({
            key: UserHelpers.localStorageKey.referrer,
            defaultValue: currentUser?.referrer ?? UserReferrer.REPLUS,
        });
        const userType = readLocalStorageValue({
            key: UserHelpers.localStorageKey.type,
            defaultValue: currentUser?.type ?? UserType.DESIGNER,
        });

        searchPlanPageState.type = type;

        const isSearchOrPlanPage =
            location.pathname.includes(RouterHelpers.urls.search()) ||
            location.pathname.includes(RouterHelpers.urls.searchAssistant());

        if (isSearchOrPlanPage) {
            const url =
                type === 'plan'
                    ? `${RouterHelpers.urls.searchAssistant()}?referrer=${referrer}&type=${userType}`
                    : RouterHelpers.urls.search();

            // replace URL without a page reload
            window.history.pushState({ ...window.history.state, as: url, url: url }, '', url);

            // set hash
            window.location.hash = `#${searchTab}`;
        } else {
            const url =
                type === 'plan'
                    ? `${RouterHelpers.urls.searchAssistant()}?referrer=${referrer}&type=${userType}`
                    : RouterHelpers.urls.searchTab(searchTab);

            RouterService.push(url);
        }
    },
};

export { SearchPlanHelpers };
