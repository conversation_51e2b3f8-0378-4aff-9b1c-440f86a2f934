import { useSearchPlanPage } from 'hooks/use-search-plan-page';

import { Page, PageProps } from 'components/page';

import { PlanPage } from 'components/plan/PlanPage';
import { SearchPage } from 'components/search/SearchPage';

import { SearchPlanSwitch } from 'components/search-plan/SearchPlanSwitch';

const SearchPlanPage = () => {
    const { type } = useSearchPlanPage();

    let pageProps: Partial<PageProps> = {
        breadcrumbs: {
            type: 'floating.fullWidth',
            isSticky: false,
            showToggle: true,
            rightSection: <SearchPlanSwitch />,
            showRightSectionOnMobile: true,
            title: 'RE+Source PRO',
        },
    };

    if (type === 'plan') {
        pageProps = {
            ...pageProps,
            hideFooter: true,
            hideLicenseAgreement: true,
            mobileNavProps: {
                variant: 'dark',
                withSpace: false,
            },
        };
    }

    if (type === 'search') {
        pageProps = {
            ...pageProps,
            showBackground: true,
            showScrollToTop: true,
        };
    }

    return <Page {...pageProps}>{type === 'plan' ? <PlanPage /> : <SearchPage />}</Page>;
};

export { SearchPlanPage };
