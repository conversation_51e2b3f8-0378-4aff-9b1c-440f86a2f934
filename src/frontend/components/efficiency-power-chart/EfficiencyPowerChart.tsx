import { Converter, powerConverter } from 'models';

import { XYC<PERSON> } from 'components/xy-chart/XYChart';

import { EfficiencyField } from 'components/component-fields/EfficiencyField';
import { PowerField } from 'components/component-fields/PowerField';

type Efficiency = Exclude<Converter['electrical']['efficiency'], undefined>;
type PowerSeries = Efficiency['powerSeries'];
type EfficiencySeries = Efficiency['efficiencySeries'];

const EfficiencyPowerChart = ({
    powerSeries = { value: [], unit: 'W' },
    efficiencySeries = { value: [], unit: '%' },
    updatePowerEfficiencySeries,
}: {
    powerSeries?: PowerSeries;
    efficiencySeries?: EfficiencySeries;
    updatePowerEfficiencySeries: (data: { efficiencySeries: EfficiencySeries; powerSeries: PowerSeries }) => void;
}) => {
    const incomingData = powerSeries.value.map((_, index) => ({
        power: powerSeries.value[index],
        efficiency: efficiencySeries.value[index],
    }));

    const bestMaxPower = incomingData.length ? Math.max(...incomingData.map((d) => Math.abs(d.power))) : 0;
    const bestPowerUnit = powerConverter.toBest({ value: bestMaxPower, from: powerConverter.baseUnit }).unit;

    // convert all power to bestPowerUnit
    const data = incomingData.map((d) => ({
        ...d,
        power: powerConverter.convert({
            value: d.power,
            from: powerConverter.baseUnit,
            to: bestPowerUnit,
        }).value,
    }));

    const nomPower = 0;
    const maxPower = data.length ? Math.max(...data.map((d) => Math.abs(d.power))) : 0;
    const maxPowerDiff = data.length ? Math.max(...data.map((d) => Math.abs(d.power - nomPower))) : 0;
    const rightDomainPower = Math.round(nomPower + maxPowerDiff + maxPower / 2);

    const efficiencySeries_ = {
        value: data.map((d) => d.efficiency),
        unit: '%',
    };

    const powerSeries_ = {
        value: data.map((d) => d.power),
        unit: bestPowerUnit,
    };

    return (
        <XYChart
            xUnitConverter={(value) =>
                powerConverter.convert({
                    value,
                    from: bestPowerUnit,
                    to: powerConverter.baseUnit,
                }).value
            }
            xAxisLabel={`Power (${bestPowerUnit})`}
            xAxisDomain={[0, rightDomainPower]}
            yAxisLabel={`Efficiency (%)`}
            yAxisDomain={[0, 100]}
            title={'Efficiency vs power'}
            fields={{
                x: {
                    label: 'Power',
                    Component: PowerField,
                },
                y: {
                    label: 'Efficiency',
                    Component: EfficiencyField,
                },
            }}
            xSeries={powerSeries_}
            ySeries={efficiencySeries_}
            updateXYSeries={({ x, y }) => {
                const efficiencySeries: EfficiencySeries = {
                    value: y,
                    unit: '%',
                };

                const powerSeries: PowerSeries = {
                    value: x,
                    unit: 'W',
                };

                updatePowerEfficiencySeries({ powerSeries, efficiencySeries });
            }}
            referenceLines={[
                {
                    // y-axis
                    y: 0,
                    color: 'rgba(0, 0, 0, 0.1)',
                },
                {
                    // x-axis
                    x: 0,
                    color: 'rgba(0, 0, 0, 0.1)',
                },
            ]}
            curveType="natural"
        />
    );
};

export { EfficiencyPowerChart };
