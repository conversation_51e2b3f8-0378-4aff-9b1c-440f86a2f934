import React, { FC } from 'react';

import { ButtonProps } from '@mantine/core';
import { TbPlus } from 'react-icons/tb';

import { useCreateOrder } from 'hooks/use-create-order';
import { useOrder } from 'hooks/use-order';
import { useOrders } from 'hooks/use-orders';

import { useCurrentUser } from 'hooks/use-current-user';
import { QuantityField } from 'components/order-components-table/fields';
import { AsyncButton } from 'components/async-button/AsyncButton';

const OrderButton: FC<{
    componentId: string;
    isLight?: boolean;
    buttonSize?: ButtonProps['size'];
}> = ({ componentId, isLight, buttonSize = 'compact-sm' }) => {
    const user = useCurrentUser();
    const { orders, mutate: mutateOrders } = useOrders({ depth: 0, limit: 9999 });

    const orderId = user?.order;
    const orderController = useOrder(orderId, { depth: 1 });

    const createOrder = useCreateOrder({
        switchAfterCreate: true,
    });

    const handleCreate = async () => {
        const order = await createOrder({
            name: `Order ${orders.length + 1}`,
            components: [{ component: componentId, quantity: 1 }],
        });

        if (order) {
            // todo: refresh cleaner
            //await mutateUser(async (user?: User) => ({ ...user, order: order.id }) as User);
            window.location.reload();
            await mutateOrders([...orders, order]);
        }
    };

    if (orderController.isLoading) return null;

    if (orderController.order === null || orderController.order.status !== 'draft') {
        return (
            <AsyncButton
                size={buttonSize}
                variant={isLight ? 'outline' : 'filled'}
                leftSection={<TbPlus size={14} />}
                onClick={async (event) => {
                    event.stopPropagation();
                    return handleCreate();
                }}
                disabled={orderController.order?.status !== 'draft'}
            >
                Add to order
            </AsyncButton>
        );
    }

    const handleQuantityChange = async (newQuantity: number) => {
        await orderController.updateComponent(componentId, { quantity: newQuantity });
    };

    const handleRemove = async () => {
        await orderController.removeComponent(componentId);
    };

    const handleAdd = async () => {
        await orderController.addComponent(componentId);
    };

    const componentInOrder = orderController.order?.components?.find((orderComponent) => {
        if (typeof orderComponent.component === 'string') {
            return orderComponent.component === componentId;
        }

        return orderComponent.component.id === componentId;
    });

    const quantity = componentInOrder?.quantity || 0;

    return (
        <React.Fragment>
            {componentInOrder ? (
                <QuantityField size="sm" quantity={quantity} onChange={handleQuantityChange} onRemove={handleRemove} />
            ) : (
                <AsyncButton
                    size={buttonSize}
                    variant={isLight ? 'light' : 'filled'}
                    leftSection={<TbPlus size={14} />}
                    onClick={async (event) => {
                        event.stopPropagation();
                        return handleAdd();
                    }}
                >
                    Add to order
                </AsyncButton>
            )}
        </React.Fragment>
    );
};

export { OrderButton };
