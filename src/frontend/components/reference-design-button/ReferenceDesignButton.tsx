import { useRouter } from 'next/router';

import { Button, ButtonProps, Modal, SimpleGrid } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoAdd } from 'react-icons/io5';

import { Component } from 'models';

import { getId } from 'helpers/getId';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { CompanyButton } from 'components/company-button/CompanyButton';
import { ProjectHelpers } from 'helpers/ProjectHelpers';

const ReferenceDesignButton = ({
    label,
    component,
    companyId,
    ...rest
}: {
    label?: string;
    component?: Component;
    companyId?: string;
} & ButtonProps) => {
    const router = useRouter();

    const [modalOpen, modalHandlers] = useDisclosure();

    const { companies, isLoading } = useCurrentTeamCompanies();

    if (isLoading) return null;

    const createUrl = component
        ? ProjectHelpers.urls.create({
              isReferenceDesign: true,
              referenceComponent: component.id,
              name: `Reference design for ${component.name}`,
          })
        : ProjectHelpers.urls.create({
              isReferenceDesign: true,
              name: `[reference design]`,
          });

    const createReferenceDesign = () => {
        if (companyId) {
            router.push(`${createUrl}&manufacturer=${companyId}`);
            return;
        }

        if (companies.length === 0) {
            router.push(createUrl);
            return;
        }

        const componentCompany = companies.find((company) => company.id === getId(component?.manufacturer));

        if (componentCompany) {
            router.push(`${createUrl}&manufacturer=${componentCompany.id}`);
            return;
        }

        if (companies.length === 1) {
            router.push(`${createUrl}&manufacturer=${companies[0].id}`);
            return;
        }

        modalHandlers.open();
    };

    return (
        <>
            <Button
                variant="subtle"
                onClick={createReferenceDesign}
                leftSection={<IoAdd />}
                {...rest}
                data-reference-design-button
            >
                {label || 'Create a Reference Design'}
            </Button>
            <Modal opened={modalOpen} onClose={modalHandlers.close} withCloseButton={false} size="lg">
                <SimpleGrid cols={{ base: 2, sm: 4 }} spacing={8}>
                    {companies.map((manufacturer) => (
                        <CompanyButton
                            key={manufacturer.id}
                            label="Create design as"
                            company={manufacturer}
                            href={`${createUrl}&manufacturer=${manufacturer.id}`}
                        />
                    ))}
                    <CompanyButton href={createUrl} label="Create design as user" />
                </SimpleGrid>

                <Button fullWidth variant="subtle" bg="transparent" onClick={modalHandlers.close} mt="md">
                    Cancel
                </Button>
            </Modal>
        </>
    );
};

export { ReferenceDesignButton };
