import { omit } from 'radash';
import { FC, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import { IoAddSharp } from 'react-icons/io5';
import { ActionIcon, ActionIconProps, Button, ButtonProps, FileButton, SimpleGrid, Table, Text } from '@mantine/core';

import { useFile } from 'hooks/use-file';

import { getId } from 'helpers/getId';

import { FileService, FileSizeLimitExceededError } from 'services/FileService';
import { ComponentFileType, ComponentFileTypes, ComponentFileUpload, FileVisibility } from 'models';
import { SelectField } from 'components/forms/fields/SelectField';
import { FileRow as FileRowComponent } from 'components/files/FilesTable';
import { LocalNotificationService } from 'services/LocalNotificationService';

const FileRow: FC<{
    file: ComponentFileUpload;
    index: number;
    editable?: boolean;
    showFileType?: boolean;
    selected?: boolean;
    onSelect?: (fileId: string) => void;
}> = ({ file, index, editable = false, showFileType = true, selected, onSelect }) => {
    const { type: fileType, ...fileProps } = file;

    const { getValues, setValue } = useFormContext();

    const { file: internalFile } = useFile(file.file);

    const handleDelete = async () => {
        setValue(
            'files',
            getValues().files.filter((_: never, i: number) => i !== index),
            { shouldDirty: true },
        );
    };

    const toggleVisibility = async () => {
        const flipVisibility = (file: any) => ({
            ...file,
            visibility: file.visibility === FileVisibility.PUBLIC ? FileVisibility.PRIVATE : FileVisibility.PUBLIC,
        });

        setValue(
            'files',
            getValues().files.map((_file: any, i: number) => (i === index ? flipVisibility(_file) : _file)),
            { shouldDirty: true },
        );
    };

    if (!internalFile) return null;

    return (
        <FileRowComponent
            file={{
                ...fileProps,
                type:
                    (editable
                        ? undefined
                        : ComponentFileTypes.options.find(({ value }) => value === fileType)?.label) || '',
                // @ts-ignore
                file: omit(internalFile, ['createdBy']),
                visibility: file.visibility ?? null,
            }}
            deleteFile={editable ? handleDelete : undefined}
            toggleVisibility={editable ? toggleVisibility : undefined}
            selected={selected}
            onSelect={editable ? onSelect : undefined}
        >
            {editable && showFileType && (
                <SelectField name={`files[${index}].type`} data={ComponentFileTypes.options} width={200} />
            )}
        </FileRowComponent>
    );
};

const ComponentFilesField = ({ editable = false }: { editable?: boolean }) => {
    const files = useWatch({ name: 'files' });

    return (
        <SimpleGrid spacing="xs">
            {editable && !files?.length && (
                <Text fw={500}>Upload datasheets, manuals, application notes, certificates, and more.</Text>
            )}
            {editable && (
                <ComponentFilesUploadButton variant="default" leftSection={<IoAddSharp />}>
                    Add file
                </ComponentFilesUploadButton>
            )}
            <ComponentFilesOverview editable={editable} />
        </SimpleGrid>
    );
};

type ComponentFilesUploadButtonProps = {
    fileType?: ComponentFileType;
    type?: 'button' | 'icon';
    children: React.ReactNode;
} & ButtonProps &
    ActionIconProps;

const ComponentFilesUploadButton = ({
    type = 'button',
    fileType,
    children,
    ...buttonProps
}: ComponentFilesUploadButtonProps) => {
    const { setValue } = useFormContext();
    const [id, files = []] = useWatch({ name: ['id', 'files'] });

    const [uploading, setUploading] = useState(false);

    const handleUpload = async (file: File) => {
        try {
            setUploading(true);
            const serverFile = await FileService.create({
                file,
                name: '',
                group: `component:${id}:files`,
            });

            setValue(`files[${files.length}]`, {
                file: serverFile.id,
                type: fileType || 'other',
            });
        } catch (error) {
            if (error instanceof FileSizeLimitExceededError) {
                LocalNotificationService.showError({ title: error.title, message: error.message });
            }

            console.log(error);
        } finally {
            setUploading(false);
        }
    };

    return (
        <FileButton onChange={(file) => file && handleUpload(file)} accept="application/pdf">
            {(props) =>
                type === 'icon' ? (
                    <ActionIcon radius="xs" {...props} {...buttonProps} loading={uploading}>
                        {children}
                    </ActionIcon>
                ) : (
                    <Button {...props} {...buttonProps} loading={uploading}>
                        {children}
                    </Button>
                )
            }
        </FileButton>
    );
};

const ComponentFilesOverview = ({
    editable = false,
    showEmpty = false,
    fileType,
    showFileType = true,
    selectedId,
    onSelect,
}: {
    editable?: boolean;
    showEmpty?: boolean;
    fileType?: ComponentFileType;
    showFileType?: boolean;
    selectedId?: string;
    onSelect?: (fileId: string) => void;
}) => {
    const files = useWatch({ name: 'files' });

    const shownFiles = fileType ? files.filter((file: any) => file.type === fileType) : files;

    if (showEmpty && !shownFiles.length)
        return (
            <Text c="dimmed" fz="sm">
                No files uploaded
            </Text>
        );

    return (
        <Table
            withTableBorder
            highlightOnHover
            style={{
                '& tbody > tr > td': {
                    verticalAlign: 'top',
                },
            }}
        >
            <Table.Tbody>
                {files?.map((file: any, index: number) => {
                    // don't use shownFiles/filter because we want to keep the index
                    if (fileType && file.type !== fileType) return null;

                    // don't use shownFiles/filter because we want to keep the index
                    if (!editable && file.visibility === FileVisibility.PRIVATE) return null;

                    return (
                        <FileRow
                            file={file}
                            index={index}
                            key={file.id}
                            editable={editable}
                            showFileType={showFileType}
                            selected={getId(file.file) === selectedId}
                            onSelect={onSelect}
                        />
                    );
                })}
            </Table.Tbody>
        </Table>
    );
};

export { ComponentFilesField, ComponentFilesUploadButton, ComponentFilesOverview };
