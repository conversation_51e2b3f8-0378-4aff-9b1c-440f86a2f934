import { FC, useEffect, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import {
    ActionIcon,
    Badge,
    Box,
    FileButton,
    Image,
    SimpleGrid,
    UnstyledButton,
    Text,
    rem,
    useMantineTheme,
    Stack,
    Anchor,
    SimpleGridProps,
    Modal,
} from '@mantine/core';

import { IoAddCircleOutline, IoTrashOutline } from 'react-icons/io5';

import { COMPONENT_IMAGE_TYPE_OPTIONS } from 'models';

import { useFile } from 'hooks/use-file';

import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { FileService, FileSizeLimitExceededError } from 'services/FileService';
import { useComponentBulkFields } from 'components/component-bulk-editor/ComponentBulkFieldsContext';

import { LocalNotificationService } from 'services/LocalNotificationService';

type ImagesImageProps = {
    image: {
        file: string;
        type: string;
    };
    index: number;
    editable?: boolean;
};

const ImagesImage: FC<ImagesImageProps> = ({ image, index, editable: passedEditable = false }) => {
    const [hasError, setHasError] = useState(false);

    const { setValue } = useFormContext();
    const [id] = useWatch({ name: ['id', 'images'] });

    const mode = useDatasheetMode();

    const { file, isLoading: loading } = useFile(image.file);

    useEffect(() => {
        if (!file?.url) {
            setHasError(false);
        }
    }, [file?.url]);

    const [uploading, setUploading] = useState(false);

    const [hovered, setHovered] = useState(false);

    const { diff, overrides, addOverride } = useComponentBulkFields();

    const [fullscreen, setFullscreen] = useState(false);

    if (mode === DatasheetMode.VIEW && !file?.url) {
        return null;
    }

    const idName = `images.${index}.file`;

    const hasDiffFieldValues = diff.includes(idName) && !overrides.includes(idName);

    const type = COMPONENT_IMAGE_TYPE_OPTIONS.find((item) => item.value === image.type);

    const handleUpload = async (file: File) => {
        if (image.file) {
            await deleteImage();
        }

        try {
            setUploading(true);

            const serverFile = await FileService.create({
                file,
                name: '',
                group: id ? `component:${id}:images` : `component:images`,
            });

            setValue(idName, serverFile.id);
        } catch (error) {
            if (error instanceof FileSizeLimitExceededError) {
                LocalNotificationService.showError({ title: error.title, message: error.message });
            }

            console.log(error);
        } finally {
            setUploading(false);
        }
    };

    const deleteImage = async () => {
        setValue(idName, null);
    };

    const editable = passedEditable && !hasDiffFieldValues;

    return (
        <>
            {fullscreen && file?.url && (
                <Modal opened onClose={() => setFullscreen(false)} size="xl">
                    <Image src={file.url} fit="contain" alt="" />
                </Modal>
            )}
            <Box
                style={{
                    position: 'relative',
                    width: '100%',
                    aspectRatio: '1 / 1',
                    border: '1px solid var(--mantine-color-gray-4)',
                    borderRadius: 'var(--mantine-radius-sm)',
                    backgroundColor: 'var(--mantine-color-gray-0)',
                    overflow: 'hidden',
                }}
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
            >
                {hasError ? (
                    <ErrorImage />
                ) : hasDiffFieldValues ? (
                    <Stack justify="center" h="100%" p="lg">
                        <Text>This image is not the same accross all components.</Text>
                        <Anchor onClick={() => addOverride(idName)}>Override?</Anchor>
                    </Stack>
                ) : loading || uploading ? (
                    <Text>Loading</Text>
                ) : (
                    <UnstyledButton onClick={() => setFullscreen(true)} style={{ cursor: 'zoom-in' }}>
                        <Image
                            src={file?.url || ''}
                            fit="cover"
                            alt=""
                            styles={{
                                root: {
                                    width: 'auto',
                                    height: 'auto',
                                    maxWidth: '100%',
                                    maxHeight: '100%',
                                    overflow: 'hidden',
                                    margin: 'auto',
                                    aspectRatio: '1 / 1',
                                    objectFit: 'contain',
                                },
                            }}
                            onError={() => {
                                if (file?.url) {
                                    setHasError(true);
                                }
                            }}
                        />
                    </UnstyledButton>
                )}
                {editable && (
                    <FileButton onChange={(file) => file && handleUpload(file)} accept="image/*">
                        {(props) => (
                            <UnstyledButton
                                {...props}
                                style={{
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    width: '100%',
                                    height: '100%',
                                    color: 'var(--mantine-color-gray-5)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                {!file?.url && <IoAddCircleOutline size={20} />}
                            </UnstyledButton>
                        )}
                    </FileButton>
                )}
                <Badge
                    size="xs"
                    variant="filled"
                    style={{
                        position: 'absolute',
                        left: 'var(--mantine-spacing-xs)',
                        top: 'var(--mantine-spacing-xs)',

                        backgroundColor: '#ffffff',
                        color: 'var(--mantine-color-gray-7)',
                    }}
                >
                    {type?.label}
                </Badge>

                {editable && image.file && hovered && (
                    <ActionIcon
                        style={{
                            position: 'absolute',
                            right: 'var(--mantine-spacing-xs)',
                            top: 'var(--mantine-spacing-xs)',

                            backgroundColor: '#ffffff',
                            color: 'var(--mantine-color-gray-7)',
                        }}
                        size="xs"
                        onClick={deleteImage}
                    >
                        <IoTrashOutline size={rem(10)} />
                    </ActionIcon>
                )}
            </Box>
        </>
    );
};

const ComponentImagesField = ({
    columns = 3,
    editable = false,
}: {
    columns?: SimpleGridProps['cols'];
    editable?: boolean;
}) => {
    const images = useWatch({ name: 'images' }) ?? [];

    return (
        <SimpleGrid cols={columns} spacing="xs">
            {images.map((image: any, index: number) => (
                <ImagesImage image={image} index={index} key={index} editable={editable} />
            ))}
        </SimpleGrid>
    );
};

const ErrorImage = () => {
    const theme = useMantineTheme();

    return (
        <Text
            p="xl"
            style={{
                border: '1px',
                borderStyle: 'solid',
                borderColor: theme.colors.gray[6],
                borderRadius: theme.radius.sm,
                height: '100%',
            }}
        >
            Unable to load image
        </Text>
    );
};

export { ComponentImagesField };
