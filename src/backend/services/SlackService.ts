import { capitalize } from 'radash';
import { Component, DiagramChatChannel, Manufacturer, Project, Team, User } from '@/payload-types';
import { Subscription } from 'models';

import { config } from '@config';
import { getCollection } from '../helpers/get-collection';
import { PayloadService } from './PayloadService';

const newUserWebhook = config.slack.newUserWebhook;
const oemWebhook = config.slack.oemWebhook;
const alertWebhook = config.slack.alertWebhook;

class BaseSlackService extends PayloadService {
    static async send(message: string, webhookUrl: string | undefined = newUserWebhook) {
        if (!webhookUrl) {
            console.log(`👖 Slack Message: \n${message}`);
            return;
        }

        const messagePrefix = config.environment !== 'production' ? '(STAGING) ' : '';

        await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: '',
                blocks: [
                    {
                        type: 'section',
                        text: {
                            type: 'mrkdwn',
                            text: messagePrefix + message,
                        },
                    },
                ],
            }),
        });
    }

    static async sendAlert(message: string) {
        return BaseSlackService.send(message, alertWebhook);
    }
}

class SlackService extends BaseSlackService {
    static async newUser(user: User, action: 'login' | 'invite') {
        const payload = await this.getPayload();

        try {
            const { id, email, source } = user;
            const profileUrl = `${payload.getAdminURL()}/collections/users/${id}`;
            const message = `🛎️  New user account by *${action}*: *${email}*${
                source && ` (via ${source})`
            }.\n\n<${profileUrl}|View profile>`;

            SlackService.send(message);
        } catch (error) {
            console.error(error);
        }
    }

    static async newAIRequest(user: User) {
        const payload = await this.getPayload();

        try {
            const { id, email } = user;
            const profileUrl = `${payload.getAdminURL()}/collections/users/${id}`;
            const message = `🤖 *${email}* has requested AI access.\n\n<${profileUrl}|View profile>`;

            SlackService.send(message);
        } catch (error) {
            console.error(error);
        }
    }

    static async newCompany({ company }: { company: Partial<Manufacturer> }) {
        try {
            const createdBy = company.createdBy
                ? await getCollection('users', company.createdBy)
                : { email: 'Unknown' };

            const companyUrl = `${config.urls.frontend}/profiles/${company.slug}`;
            const manageUrl = `${config.urls.frontend}/admin-area/companies`;

            const message = `💼  New company signup: *${company.name}* by *${createdBy.email}*. Please make sure to review their profile and verify whether it is published appropriately. (<${companyUrl}|view profile> | <${manageUrl}|manage companies>)`;

            SlackService.send(message, oemWebhook);
        } catch (error) {
            console.error(error);
        }
    }

    static async companyRequestPublish({ company, requestBy }: { company: Partial<Manufacturer>; requestBy: User }) {
        try {
            const companyUrl = `${config.urls.frontend}/profiles/${company.slug}`;
            const manageUrl = `${config.urls.frontend}/admin-area/companies`;

            const message = `🚨 *${requestBy.email}* has requested to publish profile for company *${company.name}* (<${companyUrl}|view profile> | <${manageUrl}|manage companies>) <@U06U36FETMH>`;

            SlackService.send(message, oemWebhook);
        } catch (error) {
            console.error(error);
        }
    }

    static async companyPublish({ company, publishedBy }: { company: Partial<Manufacturer>; publishedBy: User }) {
        try {
            const companyUrl = `${config.urls.frontend}/profiles/${company.slug}`;
            const manageUrl = `${config.urls.frontend}/admin-area/companies`;

            const message = `✅ Profile for company *${company.name}* was published by *${publishedBy.email}* (<${companyUrl}|view profile> | <${manageUrl}|manage companies>)`;

            SlackService.send(message, oemWebhook);
        } catch (error) {
            console.error(error);
        }
    }

    static async newCompanyAddedAsPartner({
        addedByCompany,
        company,
    }: {
        addedByCompany: Partial<Manufacturer>;
        company: Partial<NonNullable<Manufacturer['partners']>[number]>;
    }) {
        const companyUrl = `${config.urls.frontend}/profiles/${addedByCompany.slug}`;

        SlackService.send(
            [
                '📨 Someone has added a new partner, which does not exist in our database. If an email was provided, we have already reached out to the manufacturer and no further action is required.',
                `Added by: ${addedByCompany?.name} (<${companyUrl}|view profile>)`,
                `Partner name: ${company.name ?? ''}`,
                `Partner description: ${company.description ?? ''}`,
                `Partner LinkedIn: ${company.linkedin ?? ''}`,
                `Partner email: ${company.email ?? ''}`,
            ].join('\n'),
            oemWebhook,
        );
    }

    static async componentPublish({ component, publishedBy }: { component: Partial<Component>; publishedBy: User }) {
        try {
            const componentUrl = `${config.urls.frontend}/components/${component.id}`;

            const message = `✅ Product *${component.name}* was published by *${publishedBy.email}* (<${componentUrl}|view datasheet>)`;

            SlackService.send(message, oemWebhook);
        } catch (error) {
            console.error(error);
        }
    }

    static async aiEnabled(user: User) {
        try {
            const message = `🤖 ${user.email} has been granted AI access`;
            SlackService.send(message);
        } catch (error) {
            console.error(error);
        }
    }

    static async subscriptionRequestDowngrade(user: User, team: Team, level: Subscription) {
        try {
            const message = `🚨 ${user.email} has requested ${team.name} to downgrade to the ${capitalize(level)} subscription.`;
            SlackService.send(message);
        } catch (error) {
            console.error(error);
        }
    }

    static createChatChannel = async (channel: DiagramChatChannel, user: User, project: Project) => {
        try {
            const url = `${config.urls.frontend}/projects/${project.id}/editor`;

            if (!channel.type) {
                throw new Error('Chat channel type is required');
            }

            const message = {
                'team': `📢 New ${channel.name} chat channel created by ${user.name} for project: ${channel.project} (this is just a notification, no further action required to support the user) <${url}|view project>`,
                'engineering-support': `⚠️ Engineering support requested by ${user.name} for project: ${channel.project} <${url}|view project>`,
                'manufacturing-support': `⚠️ Manufacturing support requested by ${user.name} for project: ${channel.project} <${url}|view project>`,
            }[channel.type];

            SlackService.send(message).then();
        } catch (error) {
            console.error(error);
        }
    };
}

export { SlackService, BaseSlackService };
