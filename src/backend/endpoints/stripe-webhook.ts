import { Endpoint } from 'payload';
import Stripe from 'stripe';
import { after } from 'next/server';

import { StripeService } from '../services/StripeService';
import { SlackService } from '../services/SlackService';
import { EmailService } from '../services/EmailService';

import { StripeHelpers } from '../helpers/StripeHelpers';

import { config } from '@config';
import { NoContentResponse } from '@/responses/NoContentResponse';

import { PayloadService } from '@/services/PayloadService';
import { UserReferrer } from 'models';
import { formatPriceInCents } from '@/helpers/format-price';
import { sendSubscriptionUpdateEmail } from '@/helpers/send-subscription-update-email';

const stripe = new Stripe(config.stripe.secretKey);

const stripeWebhook: Endpoint = {
    path: '/stripe/webhook',
    method: 'post',
    handler: async (request) => {
        const event = await StripeService.constructWebhookEvent(request);

        const handlerPromise = checkIfRequestHasBeenHandled(event)
            .then(handleStripeEvent)
            .then(addRequestRecord)
            .catch((error) => {
                const errorMessage = error?.message || error;
                console.error(errorMessage);

                SlackService.send(
                    [
                        `⚠️ Stripe webhook handler failed.`,
                        getEventRow(event.id),
                        `Event Type: ${event.type}`,
                        'Error Message:',
                        '```',
                        errorMessage,
                        '```',
                    ].join('\n'),
                );
            });

        after(handlerPromise);

        return new NoContentResponse();
    },
};

const checkIfRequestHasBeenHandled = async (event: Stripe.Event) => {
    const payload = await PayloadService.getPayload();
    const requests = await payload.find({
        collection: 'stripeRequests',
        where: { eventId: { equals: event.id } },
    });

    const requestRecord = requests.docs[0];

    if (requestRecord) {
        throw new Error(`Request has already been handled: ${event.id}`);
    }

    return event;
};

const handleStripeEvent = async (event: Stripe.Event) => {
    switch (event.type) {
        case 'invoice.payment_succeeded':
            await handleInvoicePaymentSucceeded(event.data);
            break;

        case 'subscription_schedule.aborted':
        case 'subscription_schedule.canceled':
        case 'subscription_schedule.completed':
        case 'subscription_schedule.created':
        case 'subscription_schedule.expiring':
        case 'subscription_schedule.updated':
            await handleSaveStripeSubscriptionSchedule(event.data.object);
            break;

        case 'customer.subscription.paused':
        case 'customer.subscription.pending_update_applied':
        case 'customer.subscription.pending_update_expired':
        case 'customer.subscription.resumed':
        case 'customer.subscription.trial_will_end':
            await handleSaveStripeSubscription(event.data.object);
            break;

        case 'customer.subscription.created':
        case 'customer.subscription.updated':
            await handleSaveStripeSubscription(event.data.object, { muteEmails: true });
            break;

        case 'customer.deleted':
            await handleCustomerDeleted(event.data);
            break;

        case 'customer.subscription.deleted':
            await handleCustomerSubscriptionDeleted(event.data);
            break;

        case 'invoice.payment_failed':
            await handleInvoicePaymentFailed(event.data);
            break;

        default:
            throw new Error(`Unexpected event type ${event.type}`);
    }

    return event;
};

const addRequestRecord = async (event: Stripe.Event) => {
    const payload = await PayloadService.getPayload();
    await payload.create({
        collection: 'stripeRequests',
        data: {
            eventId: event.id,
            createdAt: new Date().toISOString(),
        },
    });
};

const handleInvoicePaymentSucceeded = async ({ object: data }: Stripe.InvoicePaymentSucceededEvent.Data) => {
    const payload = await PayloadService.getPayload();

    if (!data.customer) {
        throw new Error('No customer ID found in invoice payment succeeded event');
    }

    const customer = await StripeService.getCustomer(data.customer);
    const { teamId, signUpUserId } = StripeHelpers.getStripeCustomerData(customer);
    const team = await payload.findByID({ collection: 'teams', id: teamId });

    const subscription = await StripeService.getCustomerSubscription(customer.id);
    const invoice = await StripeService.getInvoice(subscription.latest_invoice);
    const paymentIntent = await StripeService.getPaymentIntent(invoice?.payment_intent ?? null);

    const subscriptions = StripeHelpers.getSubscriptions(subscription);
    const billingCycle = StripeHelpers.getSubscriptionBillingCycle(subscription);

    if (!paymentIntent) {
        const tierAndPrices = await StripeService.getTierAndPrices(subscriptions, billingCycle);

        await SlackService.send(
            [
                `⏭️ Skipping payment success notification for ${team.name} due to lack of payment intent. `,
                '```',
                `Amount due: ${formatPriceInCents(data.amount_due)}`,
                getCustomerIdRow(customer.id),
                'Subscriptions:',
                ...tierAndPrices,
                '```',
            ].join('\n'),
        );

        return;
    }

    const paymentMethod = await StripeService.getPaymentMethod(paymentIntent.payment_method);
    const signUpUser = await payload.findByID({ collection: 'users', id: signUpUserId });
    const paymentInfo = StripeHelpers.getPaymentInfo(invoice!, paymentIntent, paymentMethod!);
    const attachments = StripeHelpers.getInvoiceAttachments(invoice);
    const subscriptionCreated = invoice?.billing_reason === 'subscription_create';
    const subscriptionType = subscriptions.length === 1 ? subscriptions[0].subscription : undefined;
    const planTitle = subscriptionType ? StripeHelpers.getSubscriptionConfig(subscriptionType).title : undefined;

    if (subscriptionCreated) {
        const tierAndPrices = await StripeService.getTierAndPrices(subscriptions, billingCycle);
        const nextPaymentDate = StripeHelpers.formatDate(new Date(invoice.period_end * 1000));

        EmailService.send({
            to: signUpUser.email,
            data: EmailService.copy.subscriptionConfirmation({
                baseUrl: config.urls.frontend,
                subscriptionType,
                planTitle,
                nextPaymentDate,
                tierAndPrices,
                attachments,
                paymentInfo,
                team,
            }),
            referrer: signUpUser.referrer as UserReferrer,
        });
    } else {
        EmailService.send({
            to: signUpUser.email,
            data: EmailService.copy.subscriptionRenewed({
                baseUrl: config.urls.frontend,
                billingCycle,
                planTitle,
                attachments,
                paymentInfo,
                team,
            }),
            referrer: signUpUser.referrer as UserReferrer,
        });
    }

    SlackService.send(
        `🤑 Subscription payment of ${formatPriceInCents(data.amount_due)} succeeded for ${team.name}. ` +
            `Notification sent to ${signUpUser.email}`,
    );
};

const handleSaveStripeSubscription = async (
    subscription: Stripe.Subscription,
    options: { muteEmails?: boolean } = {},
) => {
    const payload = await PayloadService.getPayload();
    const customer = await StripeService.getCustomer(subscription.customer);
    const { signUpUserId, teamId } = StripeHelpers.getStripeCustomerData(customer);
    const signUpUser = await payload.findByID({ collection: 'users', id: signUpUserId });
    const signUpTeam = await payload.findByID({ collection: 'teams', id: teamId });

    const subscriptionSchedule = await StripeService.getCustomerSubscriptionSchedule(customer.id, subscription.id);

    await updateStripeSubscription(subscription, subscriptionSchedule);

    if (!subscriptionSchedule) {
        console.warn(`No subscription schedule found for subscription ${subscription.id}`);
    }

    if (options.muteEmails) {
        return;
    }

    sendSubscriptionUpdateEmail(subscription, subscriptionSchedule, signUpUser, signUpTeam);
};

const handleSaveStripeSubscriptionSchedule = async (schedule: Stripe.SubscriptionSchedule) => {
    const subscription =
        typeof schedule.subscription === 'string'
            ? await stripe.subscriptions.retrieve(schedule.subscription)
            : schedule.subscription;

    if (!subscription) {
        if (schedule.status !== 'released') {
            throw new Error('No subscription found for non-released subscription schedule');
        }

        return;
    }

    await updateStripeSubscription(subscription, schedule);
};

const updateStripeSubscription = async (
    subscription: Stripe.Subscription,
    subscriptionSchedule?: Stripe.SubscriptionSchedule,
) => {
    const payload = await PayloadService.getPayload();
    const customer = await StripeService.getCustomer(subscription.customer);
    const { teamId } = StripeHelpers.getStripeCustomerData(customer);

    await payload.update({
        collection: 'teams',
        id: teamId,
        data: {
            stripeSubscription: subscription as any,
            stripeSubscriptionSchedule: (subscriptionSchedule as any) ?? null,
        },
    });
};

const handleCustomerSubscriptionDeleted = async ({
    object: subscription,
}: Stripe.CustomerSubscriptionDeletedEvent.Data) => {
    const customer = await StripeService.getCustomer(subscription.customer);
    const { teamId } = StripeHelpers.getStripeCustomerData(customer);

    await StripeService.resetTeamSubscription(teamId);
};

const handleCustomerDeleted = async ({ object: data }: Stripe.CustomerDeletedEvent.Data) => {
    const payload = await PayloadService.getPayload();
    const customerId = data.id;
    const {
        docs: [team],
    } = await payload.find({
        collection: 'teams',
        where: { stripeCustomerId: { equals: customerId } },
    });

    await StripeService.resetTeamSubscription(team.id, true);
};

const handleInvoicePaymentFailed = async ({ object: invoice }: Stripe.InvoicePaymentFailedEvent.Data) => {
    const payload = await PayloadService.getPayload();

    if (!invoice.customer) {
        throw new Error('No customer ID found in invoice payment failed event');
    }

    const customer = await StripeService.getCustomer(invoice.customer);
    const { teamId } = StripeHelpers.getStripeCustomerData(customer);
    const team = await payload.findByID({ collection: 'teams', id: teamId });

    const paymentIntent = await StripeService.getPaymentIntent(invoice.payment_intent);

    SlackService.send(
        [
            `⚠️ Payment failed for ${team.name}.`,
            `Amount due: ${formatPriceInCents(invoice.amount_due)}`,
            `Error type: ${paymentIntent?.last_payment_error?.type}`,
            getCustomerIdRow(customer.id),
            getInvoiceNumberRow(invoice),
        ].join('\n'),
    );
};

const getEventRow = (eventId: string) => `Event ID: <https://dashboard.stripe.com/events/${eventId}|${eventId}>`;

const getCustomerIdRow = (customerId: string) =>
    `Customer ID: <https://dashboard.stripe.com/customers/${customerId}|${customerId}>`;

const getInvoiceNumberRow = (invoice: Stripe.Invoice) =>
    `Invoice Number: <https://dashboard.stripe.com/invoices/${invoice.id}|${invoice.number}>`;

export { stripeWebhook };
