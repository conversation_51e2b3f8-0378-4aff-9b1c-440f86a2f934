import { DesignerSubscription, Subscription } from 'models';

import { EmailService } from '../../services/EmailService';

import { EmailCopyBaseProps } from './EmailCopyHelpers';

type Attachments = Parameters<typeof EmailService.send>[0]['attachments'];

type PaymentInfo = {
    billedToName: string;
    invoiceNumber: string;
    paymentAmount: string;
    paymentDate: string;
    last4Digits: string;
};

type SubscriptionProps = EmailCopyBaseProps & {
    baseUrl: string;
    expirationDate: string;
    subscriptionType?: Subscription;
    planTitle?: string;
    nextPaymentDate: string;
    tierAndPrices: string[];
    billingCycle: string;
    attachments: Attachments;
    paymentInfo: PaymentInfo;
};

const EmailCopyHelpersSubscription = {
    subscriptionCancel: (data: Pick<SubscriptionProps, 'baseUrl' | 'expirationDate' | 'team'>) => {
        const { baseUrl, expirationDate, team } = data;

        const accountPageUrl = getAccountPageUrl(baseUrl, team.id);

        return {
            subject: 'Your subscription has been cancelled',
            title: 'We’re sorry to see you go',
            text:
                `Your subscription has been canceled and will expire on ${expirationDate}.` +
                '<br /><br />' +
                `To reactivate your subscription before it expires, visit your <a href="${accountPageUrl}">account page</a>.`,
        };
    },

    subscriptionConfirmation: (
        data: Pick<
            SubscriptionProps,
            | 'baseUrl'
            | 'subscriptionType'
            | 'planTitle'
            | 'nextPaymentDate'
            | 'tierAndPrices'
            | 'paymentInfo'
            | 'attachments'
            | 'team'
        >,
    ) => {
        const { baseUrl, subscriptionType, planTitle, nextPaymentDate, tierAndPrices, paymentInfo, attachments, team } =
            data;

        const accountPageUrl = getAccountPageUrl(baseUrl, team.id);
        const termsOfSerivce = `${baseUrl}/license-agreement`;
        const privacyPolicy = `${baseUrl}/dcide-privacy-policy.pdf`;

        const planTitleCopy = planTitle ? ` the ${planTitle} Plan` : ' a DCIDE Subscription Plan';

        return {
            subject: 'Receipt for your new subscription',
            attachments,
            title: 'Subscription Confirmation',
            text:
                `Congratulations! You have successfully signed up for ${planTitleCopy}` +
                (subscriptionType === DesignerSubscription.PLUS
                    ? `. Enjoy unlimited AI requests, unlimited projects, unlimited image uploads, shared team libraries and much more.`
                    : `, giving you unlimited access to our advanced simulation modules, AI tools, in-app support, and many other premium features. `) +
                '<br /><br />' +
                `You have been charged the amount shown below (plus applicable sales tax) for your subscription, which will automatically renew unless you choose to cancel it. ` +
                `You can cancel at any time by visiting your <a href="${accountPageUrl}">Account page</a> or by contacting our sales team. ` +
                `This subscription is subject to our <a href="${termsOfSerivce}">Terms of Service</a> and <a href="${privacyPolicy}">Privacy Policy</a>.` +
                '<br /><br />' +
                getTierAndPricesCopy(tierAndPrices) +
                '<br />' +
                `<b>Next payment date:</b> ${nextPaymentDate}` +
                '<br /><br />' +
                getPaymentInfoCopy(paymentInfo),
        };
    },

    subscriptionRenewed: (
        data: Pick<
            SubscriptionProps,
            'baseUrl' | 'billingCycle' | 'planTitle' | 'paymentInfo' | 'attachments' | 'team'
        >,
    ) => {
        const { baseUrl, billingCycle, planTitle, paymentInfo, attachments, team } = data;

        const accountPageUrl = getAccountPageUrl(baseUrl, team.id);

        const planTitleCopy = planTitle ? `${planTitle} ` : '';

        return {
            subject: `Confirmed: Your ${billingCycle} subscription has been renewed!`,
            attachments,
            title: 'Subscription Renewed',
            text:
                `Thank you for renewing your ${planTitleCopy}subscription! Here is a summary of your payment:` +
                '<br /><br />' +
                getPaymentInfoCopy(paymentInfo) +
                '<br /><br />' +
                `Please visit your <a href="${accountPageUrl}">account page</a> to download an invoice or make changes to your account.` +
                '<br /><br />' +
                'Let us know if you have any questions, and we will be happy to help!',
        };
    },

    subscriptionUpdate: (
        data: Pick<SubscriptionProps, 'baseUrl' | 'tierAndPrices' | 'nextPaymentDate' | 'attachments' | 'team'> &
            Partial<Pick<SubscriptionProps, 'paymentInfo'>>,
    ) => {
        const { baseUrl, tierAndPrices, nextPaymentDate, paymentInfo, attachments, team } = data;

        const accountPageUrl = getAccountPageUrl(baseUrl, team.id);

        return {
            subject: 'Changes were made to your subscription',
            title: 'Subscription Updated',
            text:
                'Your subscription has been changed. To view or make additional changes to your subscription, visit your ' +
                `<a href="${accountPageUrl}">account page</a>` +
                '<br /><br />' +
                getTierAndPricesCopy(tierAndPrices) +
                '<br />' +
                `<b>Next payment date:</b> ${nextPaymentDate}` +
                (paymentInfo ? '<br /><br />' + getPaymentInfoCopy(paymentInfo) : ''),
            attachments,
        };
    },
};

const getTierAndPricesCopy = (tierAndPrices: string[]) =>
    tierAndPrices.length === 1
        ? `<b>Your plan:</b> ${tierAndPrices[0]}`
        : `<b>Your plans:</b> ${tierAndPrices.join(', ')}`;

const getPaymentInfoCopy = ({ billedToName, invoiceNumber, paymentAmount, paymentDate, last4Digits }: PaymentInfo) =>
    `<b>Billed to:</b> ${billedToName}` +
    '<br />' +
    `<b>Invoice #:</b> ${invoiceNumber}` +
    '<br />' +
    `<b>Payment Date:</b> ${paymentDate}` +
    '<br />' +
    `<b>Payment method:</b> Account ending in ${last4Digits}` +
    '<br /><br />' +
    `<b>Total Paid:</b> ${paymentAmount}`;

const getAccountPageUrl = (baseUrl: string, teamId: string) => `${baseUrl}/account#team-${teamId}`;

export { EmailCopyHelpersSubscription };
