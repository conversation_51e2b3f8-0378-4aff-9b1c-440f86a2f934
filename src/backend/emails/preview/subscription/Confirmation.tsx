import React from 'react';

import { DesignerSubscription } from 'models';

import { EmailCopyHelpers } from '../../helpers/EmailCopyHelpers';
import { General, EmailTemplateProps } from '../../templates/General';
import { formatDate } from './format-date';

const Template = (props: EmailTemplateProps) => <General {...props} showSubject />;

const now = new Date();
const nextMonth = new Date();
nextMonth.setMonth(now.getMonth() + 1);

export const copy = EmailCopyHelpers.subscriptionConfirmation({
    baseUrl: '#',
    subscriptionType: DesignerSubscription.PLUS,
    planTitle: 'DCIDE Plus Plan',
    nextPaymentDate: formatDate(nextMonth),
    tierAndPrices: ['2 seats at $100 per seat/month = $200 billed monthly'],
    paymentInfo: {
        billedToName: 'Billed To Name',
        invoiceNumber: '9BF9789A-0002',
        last4Digits: '1234',
        paymentAmount: '$200',
        paymentDate: formatDate(now),
    },
    attachments: [],
    team: { id: 'team-id', name: 'Team Name' },
});

Template.PreviewProps = copy;

export default Template;
