import React from 'react';

import { SubscriptionBillingCycle } from 'models';

import { EmailCopyHelpers } from '../../helpers/EmailCopyHelpers';
import { General, EmailTemplateProps } from '../../templates/General';
import { formatDate } from './format-date';

const Template = (props: EmailTemplateProps) => <General {...props} showSubject />;

export const copy = EmailCopyHelpers.subscriptionRenewed({
    baseUrl: '#',
    billingCycle: SubscriptionBillingCycle.MONTHLY,
    planTitle: 'DCIDE Plus Plan',
    paymentInfo: {
        billedToName: 'Billed To Name',
        invoiceNumber: '9BF9789A-0002',
        last4Digits: '1234',
        paymentAmount: '$200',
        paymentDate: formatDate(new Date()),
    },
    attachments: [],
    team: { id: 'team-id', name: 'Team Name' },
});

Template.PreviewProps = copy;

export default Template;
