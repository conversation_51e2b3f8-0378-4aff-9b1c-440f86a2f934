import React from 'react';

import { EmailCopyHelpers } from '../../helpers/EmailCopyHelpers';
import { General, EmailTemplateProps } from '../../templates/General';
import { formatDate } from './format-date';

const Template = (props: EmailTemplateProps) => <General {...props} showSubject />;

const nextMonth = new Date();
nextMonth.setMonth(nextMonth.getMonth() + 1);

export const copy = EmailCopyHelpers.subscriptionUpdate({
    baseUrl: '#',
    nextPaymentDate: formatDate(nextMonth),
    tierAndPrices: ['2 seats at $100 per seat/month  = $200 billed monthly'],
    attachments: [],
    team: { id: 'team-id', name: 'Team Name' },
});

Template.PreviewProps = copy;

export default Template;
