import React from 'react';

import { EmailCopyHelpers } from '../../helpers/EmailCopyHelpers';
import { General, EmailTemplateProps } from '../../templates/General';
import { formatDate } from './format-date';

const Template = (props: EmailTemplateProps) => <General {...props} showSubject />;

export const copy = EmailCopyHelpers.subscriptionCancel({
    baseUrl: '#',
    expirationDate: formatDate(new Date()),
    team: { id: 'team-id', name: 'Team Name' },
});

Template.PreviewProps = copy;

export default Template;
