import * as migration_20240104_103223_validate_all_existing_diagrams from './20240104_103223_validate_all_existing_diagrams';
import * as migration_20240105_104000_delete_transactions from './20240105_104000_delete_transactions';
import * as migration_20240109_171640_convert_efficiency_unit from './20240109_171640_convert_efficiency_unit';
import * as migration_20240112_105352_update_subscribers_field from './20240112_105352_update_subscribers_field';
import * as migration_20240118_104942_delete_transactions_v2 from './20240118_104942_delete_transactions_v2';
import * as migration_20240202_083807_update_comment_schema from './20240202_083807_update_comment_schema';
import * as migration_20240321_133313_combine_user_permissions from './20240321_133313_combine_user_permissions';
import * as migration_20240326_151208_add_project_to_designs_and_diagrams from './20240326_151208_add_project_to_designs_and_diagrams';
import * as migration_20240326_152818_move_project_collaborators_from_team from './20240326_152818_move_project_collaborators_from_team';
import * as migration_20240808_093021_update_componentInstance_port_configuration from './20240808_093021_update_componentInstance_port_configuration';
import * as migration_20240813_172300_initialize_groups_images from './20240813_172300_initialize_groups_images';
import * as migration_20240925_100753_add_manufacturer_type from './20240925_100753_add_manufacturer_type';
import * as migration_20241014_133252_cleanup_user_flags from './20241014_133252_cleanup_user_flags';
import * as migration_20241022_122654_create_metrics_views from './20241022_122654_create_metrics_views';
import * as migration_20241024_082857_company_type_to_services from './20241024_082857_company_type_to_services';
import * as migration_20241106_182311_move_component_ports_to_dedicated_collection from './20241106_182311_move_component_ports_to_dedicated_collection';
import * as migration_20241114_181412_move_grounding_configuration_to_ports from './20241114_181412_move_grounding_configuration_to_ports';
import * as migration_20241121_220642_run_ensure_compatible_with_cross_reference from './20241121_220642_run_ensure_compatible_with_cross_reference';
import * as migration_20241205_172726_add_breaker_total_voltage from './20241205_172726_add_breaker_total_voltage';
import * as migration_20241209_192856_convert_strings_to_object_id from './20241209_192856_convert_strings_to_object_id';
import * as migration_20250117_090623_calculate_profile_completeness from './20250117_090623_calculate_profile_completeness';
import * as migration_20250130_222906_add_component_type_to_ports_collection from './20250130_222906_add_component_type_to_ports_collection';
import * as migration_20250206_085003_case_study_collection from './20250206_085003_case_study_collection';
import * as migration_20250217_104920_rename_charger_attributes from './20250217_104920_rename_charger_attributes';
import * as migration_20250428_200209_set_default_battery_parallelable_capacity from './20250428_200209_set_default_battery_parallelable_capacity';
import * as migration_20250430_134324_reset_company_embedding_text from './20250430_134324_reset_company_embedding_text';
import * as migration_20250501_181924_set_manufacturer_details from './20250501_181924_set_manufacturer_details';
import * as migration_20250501_185715_set_component_saves from './20250501_185715_set_component_saves';
import * as migration_20250501_191309_set_component_views from './20250501_191309_set_component_views';
import * as migration_20250501_201706_normalize_sort_rank from './20250501_201706_normalize_sort_rank';
import * as migration_20250516_085805_update_search_threads_with_exhibitor_match from './20250516_085805_update_search_threads_with_exhibitor_match';
import * as migration_20250812_124506_design_voltage_current from './20250812_124506_design_voltage_current';

export const migrations = [
    {
        up: migration_20240104_103223_validate_all_existing_diagrams.up,
        down: migration_20240104_103223_validate_all_existing_diagrams.down,
        name: '20240104_103223_validate_all_existing_diagrams',
    },
    {
        up: migration_20240105_104000_delete_transactions.up,
        down: migration_20240105_104000_delete_transactions.down,
        name: '20240105_104000_delete_transactions',
    },
    {
        up: migration_20240109_171640_convert_efficiency_unit.up,
        down: migration_20240109_171640_convert_efficiency_unit.down,
        name: '20240109_171640_convert_efficiency_unit',
    },
    {
        up: migration_20240112_105352_update_subscribers_field.up,
        down: migration_20240112_105352_update_subscribers_field.down,
        name: '20240112_105352_update_subscribers_field',
    },
    {
        up: migration_20240118_104942_delete_transactions_v2.up,
        down: migration_20240118_104942_delete_transactions_v2.down,
        name: '20240118_104942_delete_transactions_v2',
    },
    {
        up: migration_20240202_083807_update_comment_schema.up,
        down: migration_20240202_083807_update_comment_schema.down,
        name: '20240202_083807_update_comment_schema',
    },
    {
        up: migration_20240321_133313_combine_user_permissions.up,
        down: migration_20240321_133313_combine_user_permissions.down,
        name: '20240321_133313_combine_user_permissions',
    },
    {
        up: migration_20240326_151208_add_project_to_designs_and_diagrams.up,
        down: migration_20240326_151208_add_project_to_designs_and_diagrams.down,
        name: '20240326_151208_add_project_to_designs_and_diagrams',
    },
    {
        up: migration_20240326_152818_move_project_collaborators_from_team.up,
        down: migration_20240326_152818_move_project_collaborators_from_team.down,
        name: '20240326_152818_move_project_collaborators_from_team',
    },
    {
        up: migration_20240808_093021_update_componentInstance_port_configuration.up,
        down: migration_20240808_093021_update_componentInstance_port_configuration.down,
        name: '20240808_093021_update_componentInstance_port_configuration',
    },
    {
        up: migration_20240813_172300_initialize_groups_images.up,
        down: migration_20240813_172300_initialize_groups_images.down,
        name: '20240813_172300_initialize_groups_images',
    },
    {
        up: migration_20240925_100753_add_manufacturer_type.up,
        down: migration_20240925_100753_add_manufacturer_type.down,
        name: '20240925_100753_add_manufacturer_type',
    },
    {
        up: migration_20241014_133252_cleanup_user_flags.up,
        down: migration_20241014_133252_cleanup_user_flags.down,
        name: '20241014_133252_cleanup_user_flags',
    },
    {
        up: migration_20241022_122654_create_metrics_views.up,
        down: migration_20241022_122654_create_metrics_views.down,
        name: '20241022_122654_create_metrics_views',
    },
    {
        up: migration_20241024_082857_company_type_to_services.up,
        down: migration_20241024_082857_company_type_to_services.down,
        name: '20241024_082857_company_type_to_services',
    },
    {
        up: migration_20241106_182311_move_component_ports_to_dedicated_collection.up,
        down: migration_20241106_182311_move_component_ports_to_dedicated_collection.down,
        name: '20241106_182311_move_component_ports_to_dedicated_collection',
    },
    {
        up: migration_20241114_181412_move_grounding_configuration_to_ports.up,
        down: migration_20241114_181412_move_grounding_configuration_to_ports.down,
        name: '20241114_181412_move_grounding_configuration_to_ports',
    },
    {
        up: migration_20241121_220642_run_ensure_compatible_with_cross_reference.up,
        down: migration_20241121_220642_run_ensure_compatible_with_cross_reference.down,
        name: '20241121_220642_run_ensure_compatible_with_cross_reference',
    },
    {
        up: migration_20241205_172726_add_breaker_total_voltage.up,
        down: migration_20241205_172726_add_breaker_total_voltage.down,
        name: '20241205_172726_add_breaker_total_voltage',
    },
    {
        up: migration_20241209_192856_convert_strings_to_object_id.up,
        down: migration_20241209_192856_convert_strings_to_object_id.down,
        name: '20241209_192856_convert_strings_to_object_id',
    },
    {
        up: migration_20250117_090623_calculate_profile_completeness.up,
        down: migration_20250117_090623_calculate_profile_completeness.down,
        name: '20250117_090623_calculate_profile_completeness',
    },
    {
        up: migration_20250130_222906_add_component_type_to_ports_collection.up,
        down: migration_20250130_222906_add_component_type_to_ports_collection.down,
        name: '20250130_222906_add_component_type_to_ports_collection',
    },
    {
        up: migration_20250206_085003_case_study_collection.up,
        down: migration_20250206_085003_case_study_collection.down,
        name: '20250206_085003_case_study_collection',
    },
    {
        up: migration_20250217_104920_rename_charger_attributes.up,
        down: migration_20250217_104920_rename_charger_attributes.down,
        name: '20250217_104920_rename_charger_attributes',
    },
    {
        up: migration_20250428_200209_set_default_battery_parallelable_capacity.up,
        down: migration_20250428_200209_set_default_battery_parallelable_capacity.down,
        name: '20250428_200209_set_default_battery_parallelable_capacity',
    },
    {
        up: migration_20250430_134324_reset_company_embedding_text.up,
        down: migration_20250430_134324_reset_company_embedding_text.down,
        name: '20250430_134324_reset_company_embedding_text',
    },
    {
        up: migration_20250501_181924_set_manufacturer_details.up,
        down: migration_20250501_181924_set_manufacturer_details.down,
        name: '20250501_181924_set_manufacturer_details',
    },
    {
        up: migration_20250501_185715_set_component_saves.up,
        down: migration_20250501_185715_set_component_saves.down,
        name: '20250501_185715_set_component_saves',
    },
    {
        up: migration_20250501_191309_set_component_views.up,
        down: migration_20250501_191309_set_component_views.down,
        name: '20250501_191309_set_component_views',
    },
    {
        up: migration_20250501_201706_normalize_sort_rank.up,
        down: migration_20250501_201706_normalize_sort_rank.down,
        name: '20250501_201706_normalize_sort_rank',
    },
    {
        up: migration_20250516_085805_update_search_threads_with_exhibitor_match.up,
        down: migration_20250516_085805_update_search_threads_with_exhibitor_match.down,
        name: '20250516_085805_update_search_threads_with_exhibitor_match',
    },
    {
        up: migration_20250812_124506_design_voltage_current.up,
        down: migration_20250812_124506_design_voltage_current.down,
        name: 'migration_20250812_124506_design_voltage_current'
    },
];
