import { CollectionAfterReadHook } from 'payload';

const addProductSpecifications: CollectionAfterReadHook = async ({ req, doc }) => {
    const { payload } = req;

    const connectedComponentIds: string[] = [];

    Object.values(doc.componentInstances).forEach((componentInstance: any) => {
        if (componentInstance.componentId) {
            connectedComponentIds.push(componentInstance.componentId);
        }
    });

    if (connectedComponentIds.length) {
        const { docs: components } = await payload.find({
            collection: 'components',
            where: {
                id: {
                    in: connectedComponentIds,
                },
            },
            pagination: false,
        });

        components.forEach((component) => {
            const componentInstances: any[] = Object.values(doc.componentInstances ?? {}).filter(
                (componentInstance: any) => {
                    return componentInstance.componentId === component.id;
                },
            );

            componentInstances.forEach((componentInstance) => {
                componentInstance.specifications = {
                    electrical: component.electrical,
                };

                doc.componentInstances[componentInstance.id] = componentInstance;
            });
        });
    }

    return doc;
};

export { addProductSpecifications };
