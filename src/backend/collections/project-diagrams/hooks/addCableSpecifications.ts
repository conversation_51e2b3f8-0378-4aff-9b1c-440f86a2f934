import { CableHelpers } from 'models';
import { CollectionAfterReadHook } from 'payload';
import { set } from 'radash';

const addCableSpecifications: CollectionAfterReadHook = async ({ req, doc }) => {
    const { payload } = req;

    const connectedCableIds: string[] = [];

    Object.values(doc.connections ?? {}).forEach((connection: any) => {
        if (connection.cableId) {
            connectedCableIds.push(connection.cableId);
        }
    });

    if (connectedCableIds.length) {
        const { docs: cables } = await payload.find({
            collection: 'components',
            where: {
                id: {
                    in: connectedCableIds,
                },
            },
            pagination: false,
        });

        cables.forEach((cable: any) => {
            const fieldsToSet = CableHelpers.mapFieldsToConnection(cable);

            const connections: any[] = Object.values(doc.connections ?? {}).filter((connection: any) => {
                return connection.cableId === cable.id;
            });

            connections.forEach((connection) => {
                Object.entries(fieldsToSet).forEach(([fieldPath, fieldValue]) => {
                    connection = set(connection, fieldPath, fieldValue);
                });

                doc.connections[connection.id] = connection;
            });
        });
    }

    return doc;
};

export { addCableSpecifications };
