import { Access, CollectionConfig } from 'payload';
import { countProducts } from '@/collections/events/endpoints/count-products';

import { AccessHelpers } from '@/helpers/AccessHelpers';
const userIsDeveloper: Access = ({ req }) => AccessHelpers.userIsDeveloper(req.user);

const Events: CollectionConfig = {
    slug: 'events',
    admin: {
        useAsTitle: 'name',
    },
    access: {
        create: userIsDeveloper,
        read: () => true,
        update: userIsDeveloper,
        delete: userIsDeveloper,
    },
    fields: [
        {
            name: 'name',
            type: 'text',
            required: true,
        },
        {
            name: 'slug',
            type: 'text',
            required: true,
        },
        {
            name: 'isActive',
            type: 'checkbox',
        },
        {
            type: 'row',
            fields: [
                {
                    name: 'start',
                    type: 'date',
                    timezone: true,
                    required: true,
                },
                {
                    name: 'end',
                    type: 'date',
                    timezone: true,
                    required: true,
                },
            ],
        },
        {
            name: 'companies',
            type: 'array',
            fields: [
                {
                    name: 'company',
                    type: 'relationship',
                    relationTo: 'manufacturers',
                    required: true,
                },
                {
                    name: 'booth',
                    type: 'text',
                },
                {
                    name: 'hall',
                    type: 'text',
                },
            ],
        },
    ],
    endpoints: [countProducts],
};

export default Events;
