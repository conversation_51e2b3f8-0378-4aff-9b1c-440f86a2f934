import { DiagramConnection, NECInstallationMethod, WireSize } from 'models';

const connectionToPayload = (
    connection: DiagramConnection,
    standard: 'IEC' | 'NEC',
    voltageType: 'AC' | 'DC' | null,
) => {
    let lines = {};

    if (voltageType === 'AC') {
        lines = connection.lines.AC;
    }

    if (voltageType === 'DC') {
        lines = connection.lines.DC;
    }

    return {
        ambientTemperature: connection.installation.temperature.value,
        arrangement: connection.installation.cableArrangement,
        cableArrangement: connection.installation.cableArrangement,
        orientation: connection.installation.cableOrientation,
        cableOrientation: connection.installation.cableOrientation,
        supportType: connection.installation.cableSupportType,
        cableSupportType: connection.installation.cableSupportType,
        cableToCableClearance: connection.installation.cableToCableClearance,
        cablesInVicinity: connection.installation.cablesInVicinity,
        material: connection.conductorMaterial,
        conductorMaterial: connection.conductorMaterial,
        conductorTemperature: connection.conductorTemperature.value,
        cores: connection.cores,
        correctionFactorOverride: connection.installation.correctionFactorOverride,
        crossSection: transformWireSize(connection.wireSize, standard),
        designCurrent: connection.requirements.current.value,
        ductToDuctClearance: connection.installation.ductToDuctClearance,
        installationMethod: transformInstallationMethod(connection.installation.method),
        referenceInstallationMethod: connection.installation.referenceMethod,
        insulationMaterial: connection.insulationMaterial,
        length: connection.length.value,
        lines,
        numberOfCables: connection.multiple.parallel,
        numberOfConductors: getNumberOfConductors(connection, voltageType),
        numberOfDucts: connection.installation.numberOfDucts,
        numberOfTraysOrLadders: connection.installation.numberOfTraysOrLadders,
        soilThermalResistivity: connection.installation.soilThermalResistivity,
        standard,
        voltageType,
    };
};

const transformWireSize = (wireSize: DiagramConnection['wireSize'], standard: 'IEC' | 'NEC') => {
    if (!wireSize) {
        return 0;
    }

    if (standard === 'NEC') {
        return `${WireSize.toAWG(wireSize)} AWG`;
    }

    return +wireSize;
};

const transformInstallationMethod = (method: DiagramConnection['installation']['method']) => {
    if (method === NECInstallationMethod.BURIED) {
        return 'buried';
    }

    if (method === NECInstallationMethod.FREE_AIR) {
        return 'free-air';
    }

    return method;
};

const getNumberOfConductors = (connection: DiagramConnection, voltageType: 'AC' | 'DC' | null) => {
    const { lines } = connection;

    if (voltageType === 'DC') {
        if ((lines.DC['L+'] && lines.DC['L-']) || (lines.DC['L+'] && lines.DC.M) || (lines.DC['L-'] && lines.DC.M)) {
            return 2;
        }

        if (lines.DC['L+'] || lines.DC['L-']) {
            return 1;
        }
    }

    if (voltageType === 'AC') {
        const LS = [lines.AC.L1, lines.AC.L2, lines.AC.L3].filter(Boolean).length;

        if (LS === 3) {
            return 3;
        }

        if (LS === 2) {
            return 2;
        }

        if (LS === 1 && lines.AC.N) {
            return 2;
        }

        if (LS === 1) {
            return 1;
        }
    }

    return 0;
};

export { connectionToPayload };
