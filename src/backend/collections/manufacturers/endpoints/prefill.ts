import type { Endpoint } from 'payload';

import { config } from '@config';

const prefill: Endpoint = {
    path: '/prefill',
    method: 'get',
    handler: async (request) => {
        const linkedin = request.searchParams.get('linkedin');

        const response = await fetch(
            `https://enrichlayer.com/api/v2/company?url=https://www.linkedin.com/company/${linkedin}/&extra=include`,
            {
                method: 'GET',
                headers: {
                    'Authorization': config.proxycurl.apiKey,
                    'Content-Type': 'application/json',
                },
            },
        );

        return Response.json(await response.json());
    },
};

export { prefill };
