import Stripe from 'stripe';
import { config } from '@config';

import { UserReferrer } from 'models';
import { User, Team } from '@/payload-types';

import { EmailService } from '@/services/EmailService';
import { StripeService } from '@/services/StripeService';
import { StripeHelpers } from './StripeHelpers';

export const sendSubscriptionUpdateEmail = async (
    subscription: Stripe.Subscription,
    subscriptionSchedule: Stripe.SubscriptionSchedule | undefined,
    signUpUser: User,
    signUpTeam: Team,
) => {
    if (!subscriptionSchedule) {
        return sendSubscriptionUpdateEmailWithoutSchedule(subscription, signUpUser, signUpTeam);
    }

    const activePhase = StripeHelpers.getActivePhase(subscriptionSchedule).phase;
    const nextPhase = StripeHelpers.getNextSubscriptionSchedulePhase(subscriptionSchedule).phase;

    if (StripeHelpers.isCancelling(subscriptionSchedule)) {
        const phaseEndDate = StripeHelpers.formatDate(new Date(activePhase.end_date * 1000));

        EmailService.send({
            to: signUpUser.email,
            data: EmailService.copy.subscriptionCancel({
                baseUrl: config.urls.frontend,
                expirationDate: phaseEndDate,
                team: signUpTeam,
            }),
            referrer: signUpUser.referrer as UserReferrer,
        });
    } else {
        const phaseToDisplay =
            nextPhase && (await StripeHelpers.isDowngrade(activePhase.items, nextPhase.items))
                ? nextPhase
                : activePhase;

        const nextPaymentDate = StripeHelpers.formatDate(
            new Date((nextPhase ? nextPhase.start_date : activePhase.end_date) * 1000),
        );

        const tierAndPrices = await StripeService.getTierAndPrices(
            StripeHelpers.getSubscriptions(phaseToDisplay.items),
            StripeHelpers.getSubscriptionBillingCycle(subscription),
        );

        await sendEmail(signUpUser, signUpTeam, subscription, nextPaymentDate, tierAndPrices);
    }
};

const sendSubscriptionUpdateEmailWithoutSchedule = async (
    subscription: Stripe.Subscription,
    signUpUser: User,
    signUpTeam: Team,
) => {
    const nextPaymentDate = StripeHelpers.formatDate(new Date(subscription.current_period_end * 1000));

    const tierAndPrices = await StripeService.getTierAndPrices(
        StripeHelpers.getSubscriptions(subscription),
        StripeHelpers.getSubscriptionBillingCycle(subscription),
    );

    await sendEmail(signUpUser, signUpTeam, subscription, nextPaymentDate, tierAndPrices);
};

const sendEmail = async (
    signUpUser: User,
    signUpTeam: Team,
    subscription: Stripe.Subscription,
    nextPaymentDate: string,
    tierAndPrices: string[],
) => {
    const invoice = await StripeService.getInvoice(subscription.latest_invoice);
    const paymentIntent = await StripeService.getPaymentIntent(invoice?.payment_intent ?? null);
    const paymentMethod = await StripeService.getPaymentMethod(paymentIntent?.payment_method ?? null);

    const paymentInfo =
        invoice && paymentIntent && paymentMethod
            ? StripeHelpers.getPaymentInfo(invoice, paymentIntent, paymentMethod)
            : undefined;

    const attachments = StripeHelpers.getInvoiceAttachments(invoice);

    EmailService.send({
        to: signUpUser.email,
        data: EmailService.copy.subscriptionUpdate({
            baseUrl: config.urls.frontend,
            nextPaymentDate,
            attachments,
            paymentInfo,
            tierAndPrices,
            team: signUpTeam,
        }),
        referrer: signUpUser.referrer as UserReferrer,
    });
};
