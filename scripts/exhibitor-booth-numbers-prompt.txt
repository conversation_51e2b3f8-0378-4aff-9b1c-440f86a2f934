=== TESTZONE.TS ===
import type { Endpoint } from 'payload';

const testzone: Endpoint = {
    path: '/testzone',
    method: 'get',
    handler: async (request) => {
        const { payload } = request;

        const { docs: companies }  = await payload.find({
            collection: 'manufacturers',
            select: {
                name: true,
            },
            pagination: false,
        })

        return Response.json(companies, { status: 200 });
    },
};

export { testzone };
=== END TESTZONE.TS ===


=== PROMPT ===

Can you match the booth numbers with the correct companies.

I would like a response like this:

=== RESPONSE ===
[
    {
        company: "COMPANY_ID_TO_BE_REPLACES",
        booth: "BOOTH_NUMBER",
    },
    {
        company: "COMPANY_ID_TO_BE_REPLACES",
        booth: "BOOTH_NUMBER",
    }
]
=== END RESPONSE ===

Make sure you only use existing companies and booth numbers, don't make them up.
Use the company ID's in the response

=== COMPANIES ===
[{"name":"GoodWe","id":"681c05fa15e323315856e413","permissions":[]},{"name":"Ecopro Solar Supply LLC","id":"681b6ff4800c9cca4bf32e9e","permissions":[]}]
=== END COMPANIES ===

=== BOOTH NUMBERS ===
Company Name;Booth Number
AAPL;403
ABC Supply;411
=== END BOOTH NUMBERS ===
