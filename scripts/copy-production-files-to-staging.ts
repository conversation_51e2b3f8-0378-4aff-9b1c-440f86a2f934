import { ListObjectsV2Command, S3, _Object } from '@aws-sdk/client-s3';

import { config } from '@config';

const s3Client = new S3({
    forcePathStyle: false, // Configures to use subdomain/virtual calling format.
    endpoint: config.digitalOcean.spaces.endpoint,
    region: 'us-east-1',
    credentials: {
        accessKeyId: config.digitalOcean.spaces.accessKey,
        secretAccessKey: config.digitalOcean.spaces.secretKey,
    },
});

const folderReplaceRegex = /^dcide-production\//;

const script = async () => {
    try {
        const Contents = await fetchAllObjects();

        for (const { Key } of Contents) {
            if (!Key) continue;
            if (Key?.includes('1å¯æ¬')) continue;

            const source = `dcide-payload-nyc/${Key}`;
            const destination = Key!.replace(folderReplaceRegex, 'dcide-staging/');

            console.log(`Copying from ${source} to dcide-payload-nyc/${destination}`);

            s3Client
                .copyObject({
                    Bucket: 'dcide-payload-nyc',
                    CopySource: source,
                    Key: destination,
                    ACL: 'public-read',
                })
                .catch(console.error);
        }
    } catch (error) {
        console.error(error);
    }
};

async function fetchAllObjects() {
    const command = new ListObjectsV2Command({
        Bucket: 'dcide-payload-nyc',
        Prefix: 'dcide-production',
    });

    let isTruncated = true;
    let fetchCount = 1;
    const allContents: _Object[] = [];

    while (isTruncated) {
        console.log(`Fetching page ${fetchCount++}`);
        const commandResult = await s3Client.send(command);

        if (!commandResult) {
            throw new Error('Error fetching items');
        }

        const { Contents, IsTruncated, NextContinuationToken } = commandResult;

        allContents.push(...(Contents ?? []));
        isTruncated = Boolean(IsTruncated);

        command.input.ContinuationToken = NextContinuationToken;
    }

    return allContents;
}

script();
