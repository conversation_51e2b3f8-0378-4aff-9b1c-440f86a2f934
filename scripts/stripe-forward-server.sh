#!/bin/sh

# You can run this script to forward Stripe events to your local server
# You may have to run `stripe login` first to authenticate with Stripe

stripe listen --forward-to localhost:3000/api/stripe/webhook \
    --events invoice.payment_succeeded,invoice.paid,invoice.payment_failed,customer.subscription.created,customer.subscription.updated,customer.subscription.deleted,invoice.upcoming,customer.deleted,subscription_schedule.aborted,subscription_schedule.canceled,subscription_schedule.completed,subscription_schedule.created,subscription_schedule.expiring,subscription_schedule.updated