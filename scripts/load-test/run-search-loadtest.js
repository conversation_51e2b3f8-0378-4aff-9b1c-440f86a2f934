import { exec } from 'child_process';

const COLLECTION = 'load-test/postman/dcide-loadTest-search-notext-local.json';

// Number of virtual users
const NUM_USERS = 10; // 10 local - 20 good - 35 peak

const SECONDS = 60;
const DURATION_MS = SECONDS * 1000;

// Array to track request latencies
let latencies = [];

// Function to calculate average latency
const calculateAverage = (latencyArray) => {
    if (latencyArray.length === 0) return 0;
    const total = latencyArray.reduce((sum, latency) => sum + latency, 0);
    return total / latencyArray.length;
};

console.log(`Starting load test with ${NUM_USERS} users for ${DURATION_MS / 1000} seconds`);
// Function to start a virtual user
const startVirtualUser = (userId) => {
    //   console.log(`Starting virtual user ${userId}`);

    const newmanProcess = exec(
        `newman run "${COLLECTION}" --iteration-count 99999 --reporters cli`,
        (error, _, stderr) => {
            if (error) {
                console.error(`Error in virtual user ${userId}: ${error.message}`);
                return;
            }
            if (stderr) {
                console.error(`stderr from virtual user ${userId}: ${stderr}`);
                return;
            }
            console.log(`Virtual user ${userId} completed.`);
        },
    );

    // Capture request latencies from newman's output
    newmanProcess.stdout.on('data', (data) => {
        const latencyMatch = data.match(/, (\d+(?:\.\d+)?)(ms|s)/); // Adjust regex if needed
        if (latencyMatch) {
            const type = latencyMatch[2];
            if (type === 's') {
                const latency = parseFloat(latencyMatch[1], 10) * 1000;
                latencies.push({ latency, timestamp: Date.now() });
            }
            if (type === 'ms') {
                const latency = parseInt(latencyMatch[1], 10);
                latencies.push({ latency, timestamp: Date.now() });
            }
        }
    });
};

// Start all virtual users
for (let i = 1; i <= NUM_USERS; i++) {
    startVirtualUser(i);
}

// print the average latency every 10 seconds
const INTERVAL_SECONDS = 10;
const INTERVAL = INTERVAL_SECONDS * 1000;
setInterval(() => {
    const cutoffTime = Date.now() - INTERVAL; // 5 seconds ago
    const lastNRecords = latencies.filter(({ timestamp }) => timestamp > cutoffTime);
    const averageForLastNSeconds = calculateAverage(lastNRecords.map((l) => l.latency));
    const throughputLastNSeconds = lastNRecords.length / INTERVAL_SECONDS;
    console.log(
        `Performance ${INTERVAL_SECONDS} seconds: ${averageForLastNSeconds.toFixed(2)}ms - ${throughputLastNSeconds.toFixed(2)} req/s`,
    );
}, INTERVAL);

// Stop the entire test after DURATION_MS
setTimeout(() => {
    console.log(`\nTest completed after ${DURATION_MS / 1000} seconds /w ${NUM_USERS} users. Exiting...`);

    // Calculate and display the final average
    const finalAverage = calculateAverage(latencies.map((l) => l.latency));

    console.log(`Average: ${finalAverage.toFixed(2)}ms`);
    console.log(`Throughput per second: ${latencies.length / SECONDS}`);

    // Exit the process
    process.exit(0);
}, DURATION_MS);
