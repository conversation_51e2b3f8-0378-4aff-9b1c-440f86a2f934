user-loadtest-target.md

# how to run

# approach

-   business determine user goal - e.g. 1000 users
-   metrics determine usage pattern
-   run load at 10x usage pattern
-   performance tune
-   monitor production
    -   sync endpoint timeseries data
    -   active users ( usage pattern )
-   repeat

# user goal - example 1

registered: 1000
active: 100-200 (10%-20%) (active = at least used 1 project in the last 1 week)
concurrent users = 10-20 (10% of active) ( 20users x 5 diagrams (5 teams) )
collaborators per project: 2-4 (need to look into actual numbers)
test target: 10-20 x 10 = 100-200 users

GREEN: 40users x 10 diagrams (teams) - 1x_webinar - 2.5sec debounce
GREEN: 40users x 10 diagrams (teams) - 2x_webinar - 5sec debounce
RED: 40users x 10 diagrams (teams) - 2x_webinar - 2.5sec debounce
RED: 80users x 20 diagrams (teams) - 1x_webinar - 5sec debounce

note:

-   1x_webinar is too small
-   RED = GATEWAY_TIMEOUT (greater than 15sec) - too laxed. Should be <8sec page load & 1sec sync

# user goal - Kobe

registered: 1000
active: 200 (20%) (active = at least used 1 project in the last 1 week)
concurrent users = 20 (10% of active) ( 20users x 5 diagrams (5 teams) )
collaborators per project: 2-4 (need to look into actual numbers)

# usage pattern:

80% single digram, single user - 2.5sec debounce
15% 2 users diagram - 2.5sec debounce
5% 3 users - 2.5sec debounce

# usage pattern - user (conservative):

20concurrent x .8 = 16 users
20c x .15 = 3 users
20c x .05 = 1 users

# target Test (actual x10):

20concurrent x .8 x 10 = 160 users (160 diagrams)
20c x .15 x 10 = 30 users (15 diagrams)
20c x .05 x 10 = 10 users (8users x 2 diagrams (2 teams))

# metrics to verify:

-   what is the actual debounce?
-   usage pattern

# note:

-   sanity check the ratio's
-   every month update the target based on the current user base ( or when use base doubles )
-   each test user should move different components
-   how do we measure actual load ( compare loadtest cpu% (/sync latency) to actual production ) ( start here )
    -   /sync p95 ( plot as time series )
-   only update component at db level
-   split diagram meta data from diagram data (renderign) when needed
