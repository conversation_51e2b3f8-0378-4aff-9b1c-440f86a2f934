{"info": {"_postman_id": "6a6216f8-d435-4412-ae09-8a49e28bbc3b", "name": "diagram-performance-staging - 150 diagrams - 1users", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29546229"}, "item": [{"name": "move-transaction - webinar-1", "event": [{"listen": "prerequest", "script": {"exec": ["// uid(24)", "const iterate = (", "  count,", "  func,", "  initValue", ") => {", "  let value = initValue", "  for (let i = 1; i <= count; i++) {", "    value = func(value, i)", "  }", "  return value", "}", "", "const uuid = (length) => {", "  const characters =", "    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'", "  return iterate(", "    length,", "    acc => {", "      //return acc + characters.charAt(random(0, characters.length - 1))", "      return acc + characters.char<PERSON>t(Math.floor(Math.random() * (characters.length - 1)))", "    },", "    ''", "  )", "}", "", "const newTransactionId = uuid(24)", "// console.log(\"newTransactionId\", newTransactionId)", "pm.collectionVariables.set(\"newTransactionId\", newTransactionId);", "", "setTimeout(function() {", "    // Whatever you want to do after the wait", "}, 2500);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "dcide-jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************.2RnF-iXhfinnoSLJ1qhIYmYZ-xaV3pN3YzuqBIUPMr0; environmentLockAccess=9dba37e473054ddfad68c36c637c55ce;client-token=Fe26.2*2*14d5e8f475435bb443ef0d0a8a2189ecda153a3ffadbc0972f0c286efa40e7f3*yR3gvDVn-m_lNiVgG92j7A*GJ5Xr4DsD-PT6o9taEKJE4IQ0-Wvw7KCUFQYjSBaRzueeBODvP0Mx4DmF06ppYiwpjFlLMfKeR5Y2c5HxrALhGeEOphS_hRcdUhMJ1t8aBtwA9vCNbREDHyyNGRJe9uXyi_CaduCmpSUhlOtieg72ciJicP4SmaGzOig1hsS84CKWhDK9Mj_45Pk5v2HjYrIwt72zTK4RFKe75UxRLPtwInMBeqacO4yibsC10kn8UXdZHu0Gl1EsuMI21OfT0n24349ANB3yItFUrq_1L0e8j4sW_4DinkrEYV3PDc6piroiZ583DaOGbykgwyrzEhKxJtrpFGrJFUNh5pvxAnmkGIY3uYvvKHiNgXwlJsyPVu2ALl18N-rOVoxPNyC_2Q04CKlM9u1eWGgdPpnByMpGUGI2sAQalMABfVc-f29Nd3eSLRqnNR4-OtgCqsfQyxi86c5h8QhVqLY5UV0emvKVQsEenAbMIM5HfkZAXnIgjVa3IVx1rBf_P2-1cmcRM1wjckjg0CvFu5Y9RMu5Y1L-v_-aXO7UgkkwSw-9VsrpF1XEf7n2LaEvW9y6eHEl6Iy*1719935495814*a9114925f56ad6e310d29169423c353bb78eed9d00bb17dd75ad2859248418be*N90_Q_z7-QUGDOBLIFyXMUgL8bZYMxvgj2ZQNMeNA-c~2", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"transactions\": [\n        {\n            \"id\": \"{{newTransactionId}}\",\n            \"date\": \"2024-06-18T18:39:17.302Z\",\n            \"changes\": [\n                {\n                    \"type\": \"update\",\n                    \"data\": {\n                        \"images.9.position.x\": 5655\n                    },\n                    \"previousData\": {\n                        \"images.9.position.x\": 5740\n                    }\n                },\n                {\n                    \"type\": \"update\",\n                    \"data\": {\n                        \"images.9.position.y\": 3095\n                    },\n                    \"previousData\": {\n                        \"images.9.position.y\": 3100\n                    }\n                }\n            ],\n            \"status\": \"created\"\n        }\n    ],\n    \"pointer\": {\n        \"id\": \"\",\n        \"date\": \"\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://staging-api.dcide.app/api/projectDesignDiagrams/66a7ef68342482636ca1a1e3/sync", "protocol": "https", "host": ["staging-api", "dcide", "app"], "path": ["api", "projectDesignDiagrams", "66a7ef68342482636ca1a1e3", "sync"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// This does not work...", "// setTimeout(function() {", "//     // Whatever you want to do after the wait", "// }, 2500);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "lastTransactionId", "value": ""}, {"key": "newTransactionId", "value": ""}, {"key": "1x_webinar-1", "value": "6671c8c0ae07cde624abaa48", "type": "string"}, {"key": "2x_webinar-1", "value": "6671da03ae07cde624abd498", "type": "string"}, {"key": "2x_webinar-2", "value": "66744d417bd71c0d2344d06b", "type": "string"}, {"key": "2x_webinar-3", "value": "66744ddf7bd71c0d2344d0e3", "type": "string"}, {"key": "2x_webinar-4", "value": "66744df67bd71c0d2344d155", "type": "string"}, {"key": "2x_webinar-5", "value": "66744e1a7bd71c0d2344d1c9", "type": "string"}, {"key": "2x_webinar-6", "value": "66744e6c7bd71c0d2344d23c", "type": "string"}, {"key": "2x_webinar-7", "value": "66744e867bd71c0d2344d2b0", "type": "string"}, {"key": "2x_webinar-8", "value": "66744e9b7bd71c0d2344d322", "type": "string"}, {"key": "2x_webinar-9", "value": "66744eb17bd71c0d2344d394", "type": "string"}, {"key": "2x_webinar-10", "value": "66744ed07bd71c0d2344d407", "type": "string"}]}