{"info": {"_postman_id": "7e2aaa31-f9f8-42e7-992a-bb9853ff72c1", "name": "dcide-loadTest-setup", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29546229"}, "item": [{"name": "create-1users-diagram-1x_webinar", "event": [{"listen": "prerequest", "script": {"exec": ["let variable = parseInt(pm.environment.get(\"load-test-1users_index\"));", "pm.environment.set(\"load-test-1users_index\", variable+1);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\":\"loadTest-1Users-{{load-test-1users_index}}\",\n    \"team\":\"64ee2d83cab83d8e16145ec1\",\n    \"metadata\":{\n        \"startFromExistingDesign\":\"66743e770ba19ee8ef052bdd\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://staging-api.dcide.app/api/projects", "protocol": "https", "host": ["staging-api", "dcide", "app"], "path": ["api", "projects"]}}, "response": []}, {"name": "create-2users-diagram-1x_webinar", "event": [{"listen": "prerequest", "script": {"exec": ["let variable = parseInt(pm.environment.get(\"load-test-2users_index\"));", "pm.environment.set(\"load-test-2users_index\", variable+1);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\":\"loadTest-1Users-{{load-test-2users_index}}\",\n    \"team\":\"64ee2d83cab83d8e16145ec1\",\n    \"metadata\":{\n        \"startFromExistingDesign\":\"66743e770ba19ee8ef052bdd\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://staging-api.dcide.app/api/projects", "protocol": "https", "host": ["staging-api", "dcide", "app"], "path": ["api", "projects"]}}, "response": []}, {"name": "create-3users-diagram-1x_webinar", "event": [{"listen": "prerequest", "script": {"exec": ["let variable = parseInt(pm.environment.get(\"load-test-3users_index\"));", "pm.environment.set(\"load-test-3users_index\", variable+1);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\":\"loadTest-1Users-{{load-test-3users_index}}\",\n    \"team\":\"64ee2d83cab83d8e16145ec1\",\n    \"metadata\":{\n        \"startFromExistingDesign\":\"66743e770ba19ee8ef052bdd\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://staging-api.dcide.app/api/projects/{{load-test-3users_index}}", "protocol": "https", "host": ["staging-api", "dcide", "app"], "path": ["api", "projects", "{{load-test-3users_index}}"]}}, "response": []}], "variable": [{"key": "load-test-1users_index", "value": "1"}, {"key": "load-test-1users_index", "value": ""}]}