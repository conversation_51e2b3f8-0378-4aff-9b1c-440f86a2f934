import { ObjectId } from 'mongodb';

export type Row = {
    name: string;
    booth: string;
    hall: string;
};

export type Manufacturer = {
    id: string;
    name: string;
};

export type NormalizedManufacturer = Manufacturer & { normalizedName: string; normalizedNameLight: string };

export type MatchResult = {
    manufacturerId: string;
    booth: string;
    hall: string;
};

export type EventDocument = {
    companies: Array<{
        company: ObjectId;
        booth: string;
        hall: string;
    }>;
};

export type Arguments = {
    csvFilename: string;
    eventId: string;
    noInsert: boolean;
    forceInsert: boolean;
    appendBooths: boolean;
    showUsage: boolean;
};
