import { MongoClient, ObjectId } from 'mongodb';

import { EventDocument, Manufacturer, MatchResult } from './types';

const DB_NAME = 'dcide-payload-production';

export async function getMongoDbClient() {
    const uri = process.env.MONGODB_URI_PRODUCTION;
    if (!uri) {
        throw new Error('MONGODB_URI_PRODUCTION is not defined in environment variables');
    }

    return await new MongoClient(uri).connect();
}

export async function fetchManufacturers(client: MongoClient): Promise<Manufacturer[]> {
    return client
        .db(DB_NAME)
        .collection('manufacturers')
        .find({}, { projection: { name: 1 } })
        .map((row) => ({ id: row._id.toString(), name: row.name }))
        .toArray();
}

export async function boothsAlreadyExist(client: MongoClient, eventId: string): Promise<boolean> {
    const event = await client
        .db(DB_NAME)
        .collection<EventDocument>('events')
        .findOne({ _id: new ObjectId(eventId) });

    if (!event) {
        throw new Error(`Event with ID ${eventId} not found`);
    }

    return event.companies && event.companies.length > 0;
}

export async function removeExistingBooths(client: MongoClient, eventId: string): Promise<void> {
    await client
        .db(DB_NAME)
        .collection<EventDocument>('events')
        .updateOne({ _id: new ObjectId(eventId) }, { $set: { companies: [] } });
}

export async function insertCloseMatches(
    client: MongoClient,
    eventId: string,
    closeMatches: MatchResult[],
): Promise<void> {
    await client
        .db(DB_NAME)
        .collection<EventDocument>('events')
        .updateOne(
            { _id: new ObjectId(eventId) },
            {
                $push: {
                    companies: {
                        $each: closeMatches.map((match) => ({
                            company: new ObjectId(match.manufacturerId),
                            booth: match.booth,
                            hall: match.hall,
                        })),
                    },
                },
            },
        );
}
