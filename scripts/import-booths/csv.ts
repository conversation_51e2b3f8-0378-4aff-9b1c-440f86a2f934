import fs from 'fs';
import { parse } from 'csv-parse';

import { Row } from './types';

export async function parseCsvFile(filename: string): Promise<Row[]> {
    const data = await fs.promises.readFile(filename, 'utf8');

    return new Promise((resolve, reject) => {
        parse(
            data,
            {
                columns: true,
                skip_empty_lines: true,
            },
            (err, records) => {
                if (err) {
                    reject(err);
                } else if (!headersAreCorrect(records)) {
                    reject(new Error('CSV headers are not correct. Expected headers: name, booth, hall.'));
                } else {
                    resolve(records);
                }
            },
        );
    });
}

function headersAreCorrect(records: unknown[]): records is Row[] {
    if (records.length === 0) {
        return false;
    }

    const firstRecord = records[0];

    return (
        typeof firstRecord === 'object' &&
        firstRecord !== null &&
        'name' in firstRecord &&
        'booth' in firstRecord &&
        'hall' in firstRecord
    );
}
