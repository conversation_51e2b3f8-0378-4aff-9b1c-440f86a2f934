import { MongoClient } from 'mongodb';

import { Arguments } from './types';

import { performMatch } from './match';
import { parseCsvFile } from './csv';

import {
    boothsAlreadyExist,
    fetchManufacturers,
    getMongoDbClient,
    insertCloseMatches,
    removeExistingBooths,
} from './db';

async function run(args: Arguments, client: MongoClient) {
    const data = await parseCsvFile(args.csvFilename);

    const manufacturers = await fetchManufacturers(client);

    const { closeMatches } = performMatch(data, manufacturers);

    if (args.noInsert) {
        console.log('Skipping DB update');
        return;
    }

    if (!args.forceInsert && (await boothsAlreadyExist(client, args.eventId))) {
        console.log('Booths already exists, skipping insert');
        return;
    } else if (!args.appendBooths) {
        await removeExistingBooths(client, args.eventId);
    }

    await insertCloseMatches(client, args.eventId, closeMatches);
}

const args = processArgs();
const client = await getMongoDbClient();

if (args.showUsage) {
    printUsage();
    process.exit(0);
}

if (!validateArgs(args)) {
    printUsage();
    process.exit(1);
}

try {
    await run(args, client);
} finally {
    client.close();
}

function processArgs(): Partial<Arguments> {
    const csvFilename = parseFlagWithArgument('--csv');
    const eventId = parseFlagWithArgument('--event');

    return {
        noInsert: process.argv.includes('--no-insert'),
        forceInsert: process.argv.includes('--force'),
        appendBooths: process.argv.includes('--append'),
        showUsage: process.argv.includes('--help'),
        csvFilename,
        eventId,
    };
}

function parseFlagWithArgument(flag: string): string | undefined {
    const index = process.argv.indexOf(flag);

    if (index !== -1 && process.argv[index + 1]) {
        return process.argv[index + 1];
    }

    return undefined;
}

function validateArgs(args: Partial<Arguments>): args is Arguments {
    if (!args.csvFilename) {
        console.error('Missing required argument: --csv');
        return false;
    }

    if (args.noInsert) return true;

    if (!args.eventId) {
        console.error('Missing required argument: --event');
        return false;
    }

    return true;
}

function printUsage() {
    console.log(`\nUsage: node import-booths.js [options]\n`);
    console.log(`Options:`);
    console.log(`  --csv <filename>   Specify CSV filename (required)`);
    console.log(`  --event <eventId>  Specify event ID (required)`);
    console.log(`  --no-insert        Run matching logic but skip DB update`);
    console.log(`  --force            Insert booths even if they already exist`);
    console.log(`  --append           Add booths to existing list (do not clear)`);
    console.log(`  --help             Show this help message\n`);
}
