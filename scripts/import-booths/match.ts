import { Manufacturer, MatchResult, NormalizedManufacturer, Row } from './types';

export function performMatch(data: Row[], manufacturers: Manufacturer[]) {
    const normalizedManufacturers = manufacturers.map(normalizeManufacturer);

    const closeMatches: MatchResult[] = [];
    const fuzzyMatches: MatchResult[] = [];

    const trackingInfo = {
        closeMatches: 0,
        fuzzyMatches: 0,
        notFound: 0,
    };

    for (const row of data) {
        const manufacturerId = findManufacturerIdByName(row.name, normalizedManufacturers);
        if (manufacturerId) {
            closeMatches.push({
                manufacturerId,
                booth: row.booth,
                hall: row.hall,
            });
            trackingInfo.closeMatches++;
            continue;
        }

        const fuzzyManufacturer = findManufacturerIdByNameFuzzy(row.name, normalizedManufacturers);
        if (fuzzyManufacturer) {
            fuzzyMatches.push({
                manufacturerId: fuzzyManufacturer,
                booth: row.booth,
                hall: row.hall,
            });
            trackingInfo.fuzzyMatches++;
            continue;
        }

        trackingInfo.notFound++;
    }

    console.log(`Total close matches: ${trackingInfo.closeMatches}`);
    console.log(`Total fuzzy matches: ${trackingInfo.fuzzyMatches}`);
    console.log(`Total not found: ${trackingInfo.notFound}`);

    return { closeMatches };
}

function findManufacturerIdByName(rowName: string, manufacturers: NormalizedManufacturer[]): string | null {
    const normalizedRowName = normalizeName(rowName);

    return manufacturers.find((manufacturer) => manufacturer.normalizedName === normalizedRowName)?.id || null;
}

function findManufacturerIdByNameFuzzy(rowName: string, manufacturers: NormalizedManufacturer[]): string | null {
    const normalizedRowName = normalizeNameLight(rowName);

    let bestMatchId: string | null = null;
    let bestName: string | null = null;
    let bestDistance = Infinity;

    for (const manufacturer of manufacturers) {
        const distance = levenshtein(normalizedRowName, manufacturer.normalizedNameLight);
        if (distance < bestDistance) {
            bestDistance = distance;
            bestMatchId = manufacturer.id;
            bestName = manufacturer.normalizedNameLight;
        }
    }

    if (bestDistance <= 1) {
        console.log(`Fuzzy match: "${normalizedRowName}" and "${bestName}" Distance: ${bestDistance}`);
        return bestMatchId;
    }

    return null;
}

// Simple Levenshtein distance implementation
function levenshtein(a: string, b: string): number {
    const matrix = Array.from({ length: a.length + 1 }, () => new Array(b.length + 1).fill(0));

    for (let i = 0; i <= a.length; i++) {
        matrix[i][0] = i;
    }
    for (let j = 0; j <= b.length; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= a.length; i++) {
        for (let j = 1; j <= b.length; j++) {
            if (a[i - 1] === b[j - 1]) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1, // deletion
                    matrix[i][j - 1] + 1, // insertion
                    matrix[i - 1][j - 1] + 1, // substitution
                );
            }
        }
    }
    return matrix[a.length][b.length];
}

function normalizeManufacturer(manufacturer: Manufacturer): NormalizedManufacturer {
    return {
        ...manufacturer,
        normalizedName: normalizeName(manufacturer.name),
        normalizedNameLight: normalizeNameLight(manufacturer.name),
    };
}

function normalizeName(name: string): string {
    return normalizeNameLight(name)
        .replace(/ltd$/g, '')
        .replace(/co$/g, '')
        .replace(/inc$/g, '')
        .replace(/llc$/g, '')
        .replace(/\s+/g, '')
        .replace(CHINA_CITY_REGEX, '');
}

function normalizeNameLight(name: string): string {
    return name.toLowerCase().replace(/[^a-z0-9\s]/gi, '');
}

const CHINA_CITY_NAMES = [
    'suzhou',
    'zhejiang',
    'xiamen',
    'shanghai',
    'jiangsu',
    'shenzhen',
    'hangzhou',
    'wuxi',
    'changzhou',
    'nanjing',
    'yangzhou',
    'nantong',
    'hefei',
    'foshan',
    'guangzhou',
    'dongguan',
    'yantai',
    'jinan',
    'qingdao',
    'chengdu',
    "xi'an",
    'chongqing',
    'beijing',
    'tianjin',
    'shenyang',
    'dalian',
    'harbin',
    'taiyuan',
    'changsha',
    'wuhan',
    'zhengzhou',
    'shijiazhuang',
    'kunming',
    'urumqi',
    'hohhot',
    'lanzhou',
    'xining',
    'nanning',
    'haikou',
    'fuzhou',
    'xuzhou',
    'luoyang',
    'wuhu',
    'taizhou',
    'jinhua',
    'yancheng',
    'jiaxing',
    'weifang',
    'linyi',
    'zibo',
    'tangshan',
    'baotou',
    'yinchuan',
    'xiangyang',
    'zhuhai',
    'sanya',
    'qiqihar',
    'jilin',
    'tonghua',
    'yanji',
    'hulunbuir',
    'ordos',
    'chifeng',
    'jiamusi',
    'mudanjiang',
    'heihe',
    'suifenhe',
    'manzhouli',
    'erenhot',
    'alashankou',
    'horgos',
    'kashgar',
    'hotan',
    'korla',
    'aksu',
    'karamay',
    'shihezi',
    'turpan',
    'yili',
    'altay',
    'tacheng',
    'kizilsu',
    'bayingolin',
    'changji',
    'bortala',
    'shuangyashan',
    'qitaihe',
    'jixi',
    'hegang',
    'yichun',
    'suihua',
    'daqing',
    'qingyuan',
    'shaoguan',
    'zhanjiang',
    'maoming',
    'yangjiang',
    'zhaoqing',
    'meizhou',
    'shantou',
    'chaozhou',
    'jieyang',
    'yunfu',
    'huizhou',
    'zhongshan',
    'jiangmen',
    'heyuan',
];

const CHINA_CITY_REGEX = new RegExp(CHINA_CITY_NAMES.join('|'));
