import fs from 'fs';
import path from 'path';

import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

type AliasEntry = { path: string; isDir?: boolean; isFile?: boolean };

const aliasMap: Record<string, AliasEntry> = {
    '@': {
        path: './src/backend',
        isDir: true,
    },
    '@config': {
        path: './config/config.ts',
        isFile: true,
    },
    '@diagram': {
        path: './src/frontend/components/diagram',
        isDir: true,
        isFile: true,
    },
    '@payload-config': {
        path: './src/backend/payload.config.ts',
        isFile: true,
    },
    '@public-config': {
        path: './config/public-config.ts',
        isFile: true,
    },
    'components': {
        path: './src/frontend/components',
        isDir: true,
    },
    'contexts': {
        path: './src/frontend/contexts',
        isDir: true,
    },
    'data': {
        path: './src/frontend/data',
        isDir: true,
    },
    'elements': {
        path: './src/frontend/elements',
        isDir: true,
    },
    'helpers': {
        path: './src/frontend/helpers',
        isDir: true,
    },
    'hooks': {
        path: './src/frontend/hooks',
        isDir: true,
        isFile: true,
    },
    'models': {
        path: './src/models/index.ts',
        isFile: true,
    },
    'pages': {
        path: './pages',
        isDir: true,
    },
    'services': {
        path: './src/frontend/services',
        isDir: true,
    },
    'state': {
        path: './src/frontend/state',
        isDir: true,
    },
    'sync-engine': {
        path: './src/sync-engine/index.ts',
        isFile: true,
    },
    'types': {
        path: './src/frontend/types',
        isDir: true,
    },
};

const tsconfigPath = path.resolve(__dirname, '../tsconfig.json');
const nextConfigPath = path.resolve(__dirname, '../next.config.ts');

function updateTsconfig() {
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
    tsconfig.compilerOptions.paths = {};

    for (const [alias, entry] of Object.entries(aliasMap)) {
        if (!entry.isFile && !entry.isDir) {
            console.warn(`Alias entry for ${alias} is neither a file nor a directory.`);
            continue;
        }

        if (entry.isFile) {
            tsconfig.compilerOptions.paths[alias] = [entry.path];
        }

        if (entry.isDir) {
            tsconfig.compilerOptions.paths[`${alias}/*`] = [entry.path + '/*'];
        }
    }

    fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 4));

    console.log('Updated tsconfig.json aliases.');
}

function updateNextConfig() {
    let nextConfig = fs.readFileSync(nextConfigPath, 'utf8');

    const aliasEntries = Object.entries(aliasMap)
        .map(([alias, entry]) => `            '${alias}': '${entry.path}',`)
        .join('\n');
    const newAliasBlock = `resolveAlias: {\n${aliasEntries}\n        },`;

    nextConfig = nextConfig.replace(/resolveAlias:\s*{[\s\S]*?},/, newAliasBlock);

    fs.writeFileSync(nextConfigPath, nextConfig);

    console.log('Updated next.config.ts aliases.');
}

updateTsconfig();
updateNextConfig();
