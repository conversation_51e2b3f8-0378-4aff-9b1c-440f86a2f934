import { SkipError } from './errors';
import { Fields } from './fields';

export enum Manufacturer {
    BYD = 'BYD Auto Industry Company Limited',
    Kowint = 'Kowint Energy (ShenZhen) Co.,Ltd',
    Renon = 'Renon Power Technology Inc',
    Ningbo_Deye_Ess = 'NINGBO DEYE ESS TECHNOLOGY CO., LTD',
    Growatt = 'Shenzhen Growatt New Energy Co., Ltd.',
    SMA_America = 'SMA America',
    SolarEdge = 'SolarEdge Technologies Ltd.',
    Sol_Ark = 'Sol-Ark',
    Midnite_Solar = 'MidNite Solar, Inc.',
    Outback_Power = 'OutBack Power',
}

const ManufacturerConfiguration: Record<Manufacturer, { outputFilenameBase: string; parser: (row: any) => object }> = {
    [Manufacturer.BYD]: {
        outputFilenameBase: 'byd',
        parser: (row) => {
            const [name, productSeries, ...productIdentifier] = (row[Fields.MODEL] as string).split('-');

            return {
                name,
                productSeries,
                productIdentifier: productIdentifier.join('-'),
            };
        },
    },
    [Manufacturer.Kowint]: {
        outputFilenameBase: 'kowint',
        parser: (row) => {
            const [productSeries, ...productIdentifier] = (row[Fields.MODEL] as string).split('-');

            return {
                name: productSeries,
                productSeries,
                productIdentifier: productIdentifier.join('-'),
            };
        },
    },
    [Manufacturer.Renon]: {
        outputFilenameBase: 'renon',
        parser: nameIsWholeSeriesIdentifierSplit,
    },
    [Manufacturer.Ningbo_Deye_Ess]: {
        outputFilenameBase: 'ningbo_deye_ess',
        parser: nameIsWholeSeriesIdentifierSplit,
    },
    [Manufacturer.Growatt]: {
        outputFilenameBase: 'growatt',
        parser: function (row) {
            const [name, ...idAndSeries] = (row[Fields.MODEL] as string).split(' ');
            const [productIdentifier, ...productSeries] = idAndSeries.join(' ').split('-');

            return {
                name,
                productSeries: productSeries.join('-'),
                productIdentifier,
            };
        },
    },
    [Manufacturer.SMA_America]: {
        outputFilenameBase: 'sma_america',
        parser: function (row) {
            const model = row[Fields.MODEL] as string;

            const [name, productSeries] = getSmaModelName(model);
            const productIdentifier = model.slice(productSeries.length);

            return { name, productSeries, productIdentifier };
        },
    },
    [Manufacturer.SolarEdge]: {
        outputFilenameBase: 'solar_edge',
        parser: function (row) {
            const model = row[Fields.MODEL] as string;
            const modelNoDashes = model.replace('-', '');
            const usIndex = modelNoDashes.indexOf('US');

            if (usIndex === -1) {
                // These are placeholders (x)
                throw new SkipError(`Found solar edge placeholder: ${model}`);
            }

            const productSeries = modelNoDashes.slice(usIndex - 1, usIndex);
            const name = getSolarEdgeName(productSeries);

            return { name, productSeries, productIdentifier: model };
        },
    },
    [Manufacturer.Sol_Ark]: {
        outputFilenameBase: 'sol_ark',
        parser: function (row: object): object {
            console.assert(row);
            throw new SkipError();
        },
    },
    [Manufacturer.Midnite_Solar]: {
        outputFilenameBase: 'midnite_solar',
        parser: (row) => {
            const [productSeries, ...productIdentifier] = (row[Fields.MODEL] as string).split('-');
            return {
                name: 'All in One Inverter',
                productSeries: productSeries.replace(' ', ''),
                productIdentifier: productIdentifier.join('-'),
            };
        },
    },
    [Manufacturer.Outback_Power]: {
        outputFilenameBase: 'outback_power',
        parser: (row) => {
            const model = row[Fields.MODEL];

            const [name, productSeries] = getOutbackModelName(model);

            return {
                name,
                productSeries,
                productIdentifier: model.replace(productSeries, ''),
            };
        },
    },
};

function nameIsWholeSeriesIdentifierSplit(row: any) {
    const name = row[Fields.MODEL];
    const [productSeries, ...productIdentifier] = (row[Fields.MODEL] as string).split('-');

    return {
        name,
        productSeries,
        productIdentifier: productIdentifier.join('-'),
    };
}

const stringIsManufacturer = (value: string): value is Manufacturer => value in ManufacturerConfiguration;

function getSmaModelName(model: string): [string, string] {
    switch (true) {
        case model.startsWith('SB'):
            return ['Sunny Boy', 'SB'];
        case model.startsWith('STP'):
            return ['Sunny Tripower', 'STP'];
        case model.startsWith('SHP'):
            return ['Sunny Highpower', 'SHP'];
        case model.startsWith('SC'):
            return [' Sunny Central', 'SC'];

        default:
            throw new Error(`Unrecognized SMA model name ${model}`);
    }
}

function getSolarEdgeName(series: string) {
    switch (series) {
        case 'H':
            return 'Single Phase Inverter with HD-Wave Technology';
        case 'A':
            return 'Single Phase Inverter';
        case 'K':
            return 'Three Phase Inverter';

        default:
            throw new Error(`Unrecognized SolarEdge series ${series}`);
    }
}

function getOutbackModelName(model: string): [string, string] {
    switch (true) {
        case model.startsWith('SBX'):
            return ['SkyBox', 'SBX'];
        case model.startsWith('PHXL'):
            return ['Pro Harvest', 'PHXL'];

        default:
            throw new Error(`Unrecognized Outback Power model name ${model}`);
    }
}

export { ManufacturerConfiguration, stringIsManufacturer };
