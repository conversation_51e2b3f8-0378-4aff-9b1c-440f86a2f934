/**
 * generate-bulk-upload-csv.ts
 *
 * Run from app/backend with command:
 * npx ts-node scripts/generate-bulk-upload-csv.ts
 *
 */

import { readFile, writeFile } from 'node:fs/promises';
import { csv2json, json2csv } from 'json-2-csv';
import path from 'node:path';
import { Manufacturer, ManufacturerConfiguration, stringIsManufacturer } from './manufacturers';
import { Fields } from './fields';
import { SkipError } from './errors';

const runImport = async (dataFilePath: string, componentType: string) => {
    const dataBuffer = await readFile(dataFilePath);
    const data = csv2json(dataBuffer.toString(), { trimHeaderFields: true }) as any[];

    const componentTypeParser = getComponentTypeParser(componentType);

    const manufacturerDatas: Record<Manufacturer, object[]> = {
        [Manufacturer.BYD]: [],
        [Manufacturer.Kowint]: [],
        [Manufacturer.Renon]: [],
        [Manufacturer.Ningbo_Deye_Ess]: [],
        [Manufacturer.Growatt]: [],
        [Manufacturer.SMA_America]: [],
        [Manufacturer.SolarEdge]: [],
        [Manufacturer.Sol_Ark]: [],
        [Manufacturer.Midnite_Solar]: [],
        [Manufacturer.Outback_Power]: [],
    };

    for (const row of data) {
        const manufacturer = row[Fields.MANUFACTURER] as string;

        if (!stringIsManufacturer(manufacturer)) continue;

        try {
            manufacturerDatas[manufacturer].push(
                cleanValues({
                    description: row[Fields.DESCRIPTION],
                    ...componentTypeParser(row),
                    ...ManufacturerConfiguration[manufacturer].parser(row),
                }),
            );
        } catch (error) {
            if (error instanceof SkipError) {
                console.log(`Skipping row.${error.message ? ` (${error.message})` : ''}`);
            }
        }
    }

    for (const [manufacturer, rows] of Object.entries(manufacturerDatas)) {
        if (rows.length === 0) continue;

        const baseFilename = ManufacturerConfiguration[manufacturer as Manufacturer].outputFilenameBase;
        const outputFilename = getCsvPath(`${baseFilename}_${componentType}.csv`);

        await writeFile(outputFilename, json2csv(rows, { escapeHeaderNestedDots: false }));
    }
};

runImport(getCsvPath('converter_data.csv'), 'converter');

function getCsvPath(filename: string) {
    return path.join(__dirname, 'csvs', filename);
}

const cleanValues = (row: object) =>
    Object.fromEntries(
        Object.entries(row).map(([key, value]) => [key, typeof value === 'string' ? value.trim() : value]),
    );

const getComponentTypeParser = (type: string) => {
    switch (type) {
        case 'battery':
            return extractBatteryFields;

        case 'converter':
            return extractConverterFields;

        default:
            throw new Error(`Component type ${type} not found`);
    }
};

const extractBatteryFields = (row: any) => ({
    'standards': [UL_1973_ID],
    'electrical.energyCapacity.value': row['Nameplate Energy Capacity'] * KWH_TO_JOULES,
    'electrical.energyCapacity.unit': 'J',
    'electrical.ports.0.DC.power.max': row['Maximum Continuous Discharge Rate2'] * KW_TO_W,
    'electrical.ports.0.DC.power.unit': 'W',
});

const extractConverterFields = (row: any) => ({
    'electrical.ports.1.AC.voltage.nom': row['Nominal Voltage (AC)'],
    'electrical.ports.1.AC.voltage.unit': 'V',
    'electrical.ports.0.DC.voltage.min': row['Voltage Minimum (DC)'],
    'electrical.ports.0.DC.voltage.nom': row['Voltage Nominal (DC)'],
    'electrical.ports.0.DC.voltage.max': row['Voltage Maximum (DC)'],
    'electrical.ports.0.DC.voltage.unit': 'V',
    'electrical.ports.0.DC.power.max': row['Power Level (MAX)'] * KW_TO_W,
    'electrical.ports.0.DC.power.unit': 'W',
    'standards': [],
    'performance.efficiency.nom': row['Efficiency @Vnom (Wtd)'],
    'performance.efficiency.unit': '%',
});

const UL_1973_ID = '6661de391c7f02493db4ed91';
const KWH_TO_JOULES = 3600000;
const KW_TO_W = 1000;

// console.log(
//     Object.fromEntries(
//         Object.entries(
//             data.reduce((counts, row) => {
//                 const manu = row[Fields.MANUFACTURER];

//                 if (manu in counts) {
//                     counts[manu] += 1;
//                 } else {
//                     counts[manu] = 1;
//                 }

//                 return counts;
//             }, {}),
//         ).sort(([, countA], [, countB]) => countB - countA),
//     ),
// );
