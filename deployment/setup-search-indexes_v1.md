# aiembeddings

## full_text_search_index

```
{
    "mappings": {
      "dynamic": false,
      "fields": {
        "embeddingText": {
          "type": "string"
        },
        "type": {
          "type": "string"
        }
      }
    }
}
```

## vector_search_index_case_study

```
{
    "fields": [
        {
            "type": "vector",
            "path": "embedding",
            "numDimensions": 3072,
            "similarity": "cosine"
        },
        {
            "type": "filter",
            "path": "type"
        }
    ]
}
```

# components

## type_1_canServeAs_1

```
db["components"].createIndex({ type: 1, canServeAs: 1 })
```

## full_text_search_index

```
{
    "mappings": {
        "dynamic": false,
        "fields": {
            "embeddingText": {
                "type": "string"
            },
            "name": {
                "type": "string"
            },
            "productIdentifier": {
                "type": "string"
            },
            "type": {
              "type": "string"
            }
        }
    }
}
```

## vector_search_index

```
{
    "fields": [
        {
            "type": "vector",
            "path": "embedding",
            "numDimensions": 3072,
            "similarity": "cosine"
        },
        {
            "type": "filter",
            "path": "type"
        }
    ]
}
```

## manufacturer_1_archivedAt_1_deletedAt_1_sortRank*-1*completeness\*-1_name_1_productSeries_1_productIdentifier_1\_\_id_1

```
manufacturer ^, archivedAt ^, deletedAt ^, sortRank v, completeness v, name ^, productSeries ^, productIdentifier ^, \_id ^
```

# manufacturers

## full_text_search_index

```
{
    "mappings": {
        "dynamic": false,
        "fields": {
            "embeddingText": {
                "type": "string"
            },
            "name": {
                "type": "string"
            }
        }
    }
}
```

## vector_search_index

```
{
    "fields": [
        {
            "type": "vector",
            "path": "embedding",
            "numDimensions": 3072,
            "similarity": "cosine"
        }
    ]
}
```

# componentports

## data.DC.power.nom_1

```
db["componentports"].createIndex(
  { "data.DC.power.nom": 1 },
  {
    partialFilterExpression: {
      $or: [
        { "data.DC.power.nom": null },
        { "data.DC.power.nom": { $gte: 15000 } },
      ],
    },
  }
);

```

## data.AC.power.nom_1

```
db["componentports"].createIndex(
  { "data.AC.power.nom": 1 },
  {
    partialFilterExpression: {
      $or: [
        { "data.AC.power.nom": null },
        { "data.AC.power.nom": { $gte: 15000 } },
      ],
    },
  }
);
```
