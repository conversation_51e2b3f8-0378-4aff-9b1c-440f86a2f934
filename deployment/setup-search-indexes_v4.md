# full_text_search_index_v2
## components

```
{
    "mappings": {
      "dynamic": false,
      "fields": {
        "name": {
          "type": "autocomplete"
        },
        "productIdentifier": {
          "type": "autocomplete"
        },
        "productSeries": {
          "type": "autocomplete"
        },
        "embeddingText": {
          "type": "string",
          "analyzer": "lucene.english"
        },
        "type": {
          "type": "string"
        }
      }
    },
    "synonyms": [
      {
        "name": "searchSynonyms",
        "analyzer": "lucene.english",
        "source": {
          "collection": "search_synonyms"
        }
      }
    ]
  }
```
