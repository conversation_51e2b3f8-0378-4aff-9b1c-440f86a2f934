# full_text_search_index_v3
## components
```
{
  "mappings": {
    "dynamic": false,
    "fields": {
      "name": [
        {
          "type": "string",
          "analyzer": "lucene.english"
        },
        {
          "type": "autocomplete"
        }
      ],
      "productIdentifier": {
        "type": "string",
        "analyzer": "lucene.english"
      },
      "productSeries": {
        "type": "string",
        "analyzer": "lucene.english"
      },
      "embeddingText": {
        "type": "string",
        "analyzer": "lucene.english"
      },
      "type": {
        "type": "string"
      }
    }
  },
  "synonyms": [
    {
      "name": "searchSynonyms",
      "analyzer": "lucene.english",
      "source": {
        "collection": "search_synonyms"
      }
    }
  ]
}
```

# manufacturers_full_text_search_index_v2
## manufacturers
```
{
  "mappings": {
    "dynamic": false,
    "fields": {
      "name": [
        {
          "type": "string",
          "analyzer": "lucene.english"
        },
        {
          "type": "autocomplete"
        }
      ],
      "embeddingText": {
        "type": "string",
        "analyzer": "lucene.english"
      },
      "locations": {
        "type": "document",
        "fields": {
          "address": {
            "type": "document",
            "fields": {
              "coordinates": {
                "type": "geo"
              }
            }
          }
        }
      }
    }
  },
  "synonyms": [
    {
      "name": "searchSynonyms",
      "analyzer": "lucene.english",
      "source": {
        "collection": "search_synonyms"
      }
    }
  ]
}
```
