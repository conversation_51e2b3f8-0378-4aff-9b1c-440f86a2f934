{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/backend/*"], "@config": ["./config/config.ts"], "@diagram": ["./src/frontend/components/diagram"], "@diagram/*": ["./src/frontend/components/diagram/*"], "@payload-config": ["./src/backend/payload.config.ts"], "@public-config": ["./config/public-config.ts"], "components/*": ["./src/frontend/components/*"], "contexts/*": ["./src/frontend/contexts/*"], "data/*": ["./src/frontend/data/*"], "elements/*": ["./src/frontend/elements/*"], "helpers/*": ["./src/frontend/helpers/*"], "hooks": ["./src/frontend/hooks"], "hooks/*": ["./src/frontend/hooks/*"], "models": ["./src/models/index.ts"], "pages/*": ["./pages/*"], "services/*": ["./src/frontend/services/*"], "state/*": ["./src/frontend/state/*"], "sync-engine": ["./src/sync-engine/index.ts"], "types/*": ["./src/frontend/types/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "app/auth/[...auth0].js", "declarations.d.ts"], "exclude": ["node_modules", "**/*.test.tsx", "**/*.test.ts"]}