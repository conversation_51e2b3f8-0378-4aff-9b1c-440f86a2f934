{"headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, baggage, sentry-trace, source-vercel-json"}]}], "git": {"deploymentEnabled": false}, "crons": [{"schedule": "0 2 * * 2", "path": "/api/crons/clean-up-stripe-requests"}, {"schedule": "0 1 * * *", "path": "/api/crons/sync-stripe-data"}, {"schedule": "0 1 * * *", "path": "/api/crons/generate-sitemap"}, {"schedule": "0 8 * * 2,4", "path": "/api/crons/identify-dead-file-urls"}, {"schedule": "0 21 * * *", "path": "/api/crons/update-featured-products"}, {"schedule": "0 6 * * 1", "path": "/api/crons/sync-component-data"}, {"schedule": "0 9 * * *", "path": "/api/crons/automailer"}], "functions": {"src/app/api/bulk-upload/components/route.ts": {"maxDuration": 300}, "src/app/api/bulk-upload/images/route.ts": {"maxDuration": 300}}}