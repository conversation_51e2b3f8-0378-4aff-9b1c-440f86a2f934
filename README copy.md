## Introduction

This repo contains the DCIDE UI web app. It is a full stack [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app) and deployed on Vercel.

## Pre-requisites

There are several packages and tools to install locally to make sure you have a working NextJS environment.

-   Install Node and NPM
-   Install Yarn `npm install -g yarn`
-   Install Vercel CLI `npm install -g vercel@latest`
    -   Log in to vercel CLI using `vercel login`
-   After logging in, link vercel to the repo by running `vercel link` in the repo:

```
$ vercel link
? Set up “~/dcide/dcide-nxt”? [Y/n] y
? Which scope should contain your project? dcide
? Found project “dcide/dcide-nxt”. Link to it? [Y/n] y
✅  Linked to dcide/dcide-nxt (created .vercel)
```

-   Use `vercel env pull` to create a local copy of the `.env` file. Environment variables for all environments, including local, are hosted on Vercel. After pulling them, you can override any values in `.env` by creating a `.env.local`. For example, use the staging API locally by adding `DCIDE_BACKEND_API="https://dcide-api-stage.vercel.app/api"` in `.env.local`
-   Install Dependencies with `yarn install`

To learn more about Next.js, take a look at the following resources:

-   [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
-   [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

## Getting started

-   Run `npx husky install` to enable code formatting with Prettier on commit
-   Run local dev server using `yarn run dev`.
-   Open [http://localhost:3000](http://localhost:3000) to view the app. Local changes will hot reload.

## Deploying

The `main` and `development` branches of this repository are connected to Vercel.

-   **Staging** Pushing to the `development` branch will create a preview deployment in vercel at https://dcide-stage.vercel.app
-   **Production** - Pushing to `main` will deploy to production (will protect this branch to prevent accidental deploys as we launch) at https://v1.dcide.app

See [Vercel Platform](https://vercel.com/) and [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Environments

### Production

-   UI = v1.dcide.app
-   API = dcide-api.vercel.app

### Staging

-   UI = dcide-stage.vercel.app
-   API = dcide-api-stage.vercel.app

### Local

-   UI = http://localhost:3000
-   API = http://localhost:3001 (you can use staging if not making DB changes)

## Development Process

We use trunk based development, which means that all feature branches are created off the `main` branch and PR'd back to main. Please make sure to create a branch and PR for all code changes. Generate a branch name from Linear using `cmd+shift+.` For more info, see [Engineering Process](https://docs.google.com/document/d/1XIvzqQp589fW_Ls9Kd6WP-PsqCoo2FdyAWWVxlfOJT0/edit#heading=h.slwhn2fs0ghe)

## Unit testing

To run a single test file:

```
npm run test:unit -- components/diagram/diagram.test.ts
```

To watch for changes on files used by unit tests:
```
npm run test:unit -- --watch
```
